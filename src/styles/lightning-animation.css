/* 动态闪电动画样式 */

/* 基础闪电容器 */
.lightning-container {
  position: relative;
  width: 100%;
  height: 100px;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* SVG闪电样式 */
.lightning-svg {
  width: 100%;
  height: 100%;
}

/* 主闪电路径 */
.lightning-path {
  stroke: url(#lightningGradient);
  stroke-width: 3;
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  animation: lightningFlash 2s ease-in-out infinite;
  filter: drop-shadow(0 0 10px rgba(255, 255, 0, 0.8));
}

/* 闪电光晕 */
.lightning-glow {
  stroke: url(#glowGradient);
  stroke-width: 8;
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  opacity: 0.3;
  animation: glowPulse 1.5s ease-in-out infinite;
}

/* 分支闪电 */
.lightning-branch {
  stroke: url(#lightningGradient);
  stroke-width: 2;
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  opacity: 0.7;
  animation: branchFlash 1.8s ease-in-out infinite;
}

/* 闪烁点 */
.spark {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #ffff00;
  border-radius: 50%;
  box-shadow: 0 0 10px #ffff00, 0 0 20px #ffff00;
  animation: sparkle 1.5s ease-in-out infinite;
}

.spark-1 {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.spark-2 {
  top: 60%;
  left: 40%;
  animation-delay: 0.3s;
}

.spark-3 {
  top: 30%;
  left: 70%;
  animation-delay: 0.6s;
}

.spark-4 {
  top: 70%;
  left: 85%;
  animation-delay: 0.9s;
}

/* 动画定义 */
@keyframes lightningFlash {
  0%, 100% {
    opacity: 0.3;
    stroke-width: 2;
  }
  10%, 90% {
    opacity: 1;
    stroke-width: 4;
  }
  50% {
    opacity: 0.8;
    stroke-width: 3;
  }
}

@keyframes glowPulse {
  0%, 100% {
    opacity: 0.1;
    stroke-width: 6;
  }
  50% {
    opacity: 0.4;
    stroke-width: 10;
  }
}

@keyframes branchFlash {
  0%, 100% {
    opacity: 0.3;
    stroke-width: 1;
  }
  50% {
    opacity: 0.8;
    stroke-width: 3;
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 快速闪烁版本 */
.lightning-fast {
  animation: lightningFlash 0.8s ease-in-out infinite;
}

.lightning-glow-fast {
  animation: glowPulse 0.6s ease-in-out infinite;
}

/* 慢速脉冲版本 */
.lightning-slow {
  animation: lightningFlash 3s ease-in-out infinite;
}

.lightning-glow-slow {
  animation: glowPulse 2.5s ease-in-out infinite;
}

/* 随机闪烁版本 */
.lightning-random {
  animation: lightningRandom 2.5s ease-in-out infinite;
}

@keyframes lightningRandom {
  0%, 100% {
    opacity: 0.2;
    stroke-width: 2;
  }
  15%, 85% {
    opacity: 1;
    stroke-width: 5;
  }
  30%, 70% {
    opacity: 0.6;
    stroke-width: 3;
  }
  45%, 55% {
    opacity: 0.9;
    stroke-width: 4;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lightning-container {
    height: 60px;
  }
  
  .lightning-path {
    stroke-width: 2;
  }
  
  .lightning-glow {
    stroke-width: 4;
  }
  
  .lightning-branch {
    stroke-width: 1;
  }
}

/* 渐变定义 - 可以在HTML中使用 */
.lightning-gradient {
  --gradient: linear-gradient(90deg, #ffffff 0%, #ffff00 25%, #ffaa00 50%, #ffff00 75%, #ffffff 100%);
}

.glow-gradient {
  --gradient: linear-gradient(90deg, rgba(255, 255, 0, 0.5) 0%, rgba(255, 170, 0, 0.8) 50%, rgba(255, 255, 0, 0.5) 100%);
} 