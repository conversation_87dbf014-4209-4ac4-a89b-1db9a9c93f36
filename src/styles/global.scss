::-webkit-scrollbar {
	width: 0;
	height: 0;
}

// Hide scrollbar for IE, Edge and Firefox
* {
	-ms-overflow-style: none; /* IE and Edge */
	scrollbar-width: none !important; /* Firefox */
	-webkit-overflow-scrolling: touch;
}

html {
	touch-action: manipulation;
	margin: 0 auto;
	line-height: 1.2;
	color: #333;
	// max-width: 750px;
}

body {
	width: 100%;
	height: 100%;
	overflow-x: hidden;
}
img {
	user-select: none;
	-webkit-user-drag: none;
}
#app {
	width: 100%;
	height: 100%;
	background: #000;
	scroll-behavior: smooth;
	.app-container {
		width: 100%;
		height: 100%;
		padding-top: env(safe-area-inset-top);
		display: flex;
		flex-direction: column;
		.main-container {
			flex: 1;
		}
		// padding-bottom: 50px;
	}
}
//对一些年纪 热门 新标签进行处理
.age {
	width: max-content;
	height: 12px;
	padding: 0 4px;
	display: flex;
	align-items: center;
	gap: 2px;
	background: #ff1a9e;
	border-radius: 6px;
	margin-bottom: 4px;
    font-style: italic;

	img {
		width: 10px;
	}

	&-text {
		font-size: 10px;
		color: #fff;
		font-weight: 800;
	}
}
.hot {
	background: linear-gradient(90deg, #ff73dc 0%, #ff4891 100%);
}
.new {
	background: linear-gradient(90deg, #06c3fd 0%, #83fc95 100%);
}
