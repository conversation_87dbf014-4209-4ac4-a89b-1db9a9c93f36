<!--  -->
<template>
    <div>
        <var-popup position="center" v-model:show="showNoMediaPermissionPopup" @click-overlay="close" :safe-area="true"
            :default-style="false" :overlay="false">
            <div class="reminder-popup">
                <div class="reminder-popup-content">
                    <div class="reminder-popup-content-title">
                        Permisson Require
                    </div>
                    <div class="reminder-popup-content-desc">
                        Please go to ‘Setting’ > Find App to enable Microphone and Camera Permission
                    </div>
                    <div class="reminder-popup-content-btn">
                        <div class="reminder-popup-content-btn-item cancel-btn"@click="goSetting" v-ripple="{ color: 'rgba(240,240,240,1)' }">
                            <span>Got it</span>
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="NoMediaPermissonPopup">
import { ref, watch, onMounted, nextTick } from 'vue'
const props = defineProps<Props>()
interface Props {
    show: boolean,
}

const emit = defineEmits(['update:show'])

const showNoMediaPermissionPopup = ref(false)

const dontRemindAgain = ref(false)

watch(() => showNoMediaPermissionPopup.value, (newVal: boolean) => {
    emit('update:show', newVal)
})


watch(() => props.show, (newVal: boolean) => {
    showNoMediaPermissionPopup.value = newVal
}, {
    immediate: true
})
/**
 * Close the popup.
 */
const close = () => {
    showNoMediaPermissionPopup.value = false
}

const changeDontRemindAgain = () => {
    dontRemindAgain.value = !dontRemindAgain.value
}

const goSetting = () => {
   console.log('go-setting')
   close()
}

</script>
<style lang="scss" scoped>
.reminder-popup {
    width: 100svw;
    overflow: hidden;
    font-family: var(--font-family-urbanist);
    padding: 0 38px;
    box-sizing: border-box;

    .reminder-popup-content {
        width: 100%;
        padding: 40px 30px;
        box-sizing: border-box;
        background-color: #1E1D38;
        border-radius: 16px;

        .reminder-popup-content-title {
            font-weight: 700;
            color: #fff;
            font-size: 18px;
            line-height: 22px;
            text-align: center;

        }

        .reminder-popup-content-desc {
            margin-top: 15px;
            font-size: 14px;
            font-weight: 700;
            line-height: 19px;
            color: rgba($color: #fff, $alpha: .7);
            text-align: center;
        }

        .reminder-popup-content-btn {
            margin-top: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;

            .reminder-popup-content-btn-item {
                width: max-content;
                height: 44px;
                border-radius: 8px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 16px;
                padding: 12.5px 40px;
                font-weight: 700;
                &.cancel-btn{
                    color: #fff;
                    background-color: rgba($color: #fff, $alpha: .05);
                }
            }
        }
    }
}
</style>