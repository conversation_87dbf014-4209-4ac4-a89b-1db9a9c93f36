<template>
    <div class="simple-lightning" :class="lightningClass">
        <svg class="lightning-svg" viewBox="0 0 400 100" xmlns="http://www.w3.org/2000/svg">
            <!-- 渐变定义 -->
            <defs>
                <linearGradient id="lightningGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                    <stop offset="25%" style="stop-color:#ffff00;stop-opacity:1" />
                    <stop offset="50%" style="stop-color:#ffaa00;stop-opacity:1" />
                    <stop offset="75%" style="stop-color:#ffff00;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
                </linearGradient>

                <linearGradient id="glowGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#ffff00;stop-opacity:0.5" />
                    <stop offset="50%" style="stop-color:#ffaa00;stop-opacity:0.8" />
                    <stop offset="100%" style="stop-color:#ffff00;stop-opacity:0.5" />
                </linearGradient>
            </defs>

            <!-- 主闪电路径 -->
            <path class="lightning-path" d="M 20 50 L 80 20 L 120 80 L 180 30 L 220 70 L 280 40 L 320 60 L 380 50"
                stroke="url(#lightningGradient)" stroke-width="3" fill="none" stroke-linecap="round"
                stroke-linejoin="round" />

            <!-- 闪电光晕 -->
            <path class="lightning-glow" d="M 20 50 L 80 20 L 120 80 L 180 30 L 220 70 L 280 40 L 320 60 L 380 50"
                stroke="url(#glowGradient)" stroke-width="8" fill="none" stroke-linecap="round" stroke-linejoin="round"
                opacity="0.3" />
        </svg>

        <!-- 闪烁点 -->
        <div class="spark spark-1"></div>
        <div class="spark spark-2"></div>
        <div class="spark spark-3"></div>
        <div class="spark spark-4"></div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
    variant?: 'normal' | 'fast' | 'slow' | 'random'
    size?: 'small' | 'medium' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
    variant: 'normal',
    size: 'medium'
})

const lightningClass = computed(() => {
    const classes = ['simple-lightning']

    // 添加变体类
    if (props.variant !== 'normal') {
        classes.push(`lightning-${props.variant}`)
    }

    // 添加尺寸类
    if (props.size !== 'medium') {
        classes.push(`size-${props.size}`)
    }

    return classes.join(' ')
})
</script>

<style scoped>
.simple-lightning {
    position: relative;
    width: 100%;
    height: 100px;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 8px;
}

.lightning-svg {
    width: 100%;
    height: 100%;
}

.lightning-path {
    animation: lightningFlash 2s ease-in-out infinite;
    filter: drop-shadow(0 0 10px rgba(255, 255, 0, 0.8));
}

.lightning-glow {
    animation: glowPulse 1.5s ease-in-out infinite;
}

/* 闪烁点 */
.spark {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #ffff00;
    border-radius: 50%;
    box-shadow: 0 0 10px #ffff00, 0 0 20px #ffff00;
    animation: sparkle 1.5s ease-in-out infinite;
}

.spark-1 {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.spark-2 {
    top: 60%;
    left: 40%;
    animation-delay: 0.3s;
}

.spark-3 {
    top: 30%;
    left: 70%;
    animation-delay: 0.6s;
}

.spark-4 {
    top: 70%;
    left: 85%;
    animation-delay: 0.9s;
}

/* 动画定义 */
@keyframes lightningFlash {

    0%,
    100% {
        opacity: 0.3;
        stroke-width: 2;
    }

    10%,
    90% {
        opacity: 1;
        stroke-width: 4;
    }

    50% {
        opacity: 0.8;
        stroke-width: 3;
    }
}

@keyframes glowPulse {

    0%,
    100% {
        opacity: 0.1;
        stroke-width: 6;
    }

    50% {
        opacity: 0.4;
        stroke-width: 10;
    }
}

@keyframes sparkle {

    0%,
    100% {
        opacity: 0;
        transform: scale(0.5);
    }

    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

/* 变体样式 */
.lightning-fast .lightning-path {
    animation: lightningFlash 0.8s ease-in-out infinite;
}

.lightning-fast .lightning-glow {
    animation: glowPulse 0.6s ease-in-out infinite;
}

.lightning-slow .lightning-path {
    animation: lightningFlash 3s ease-in-out infinite;
}

.lightning-slow .lightning-glow {
    animation: glowPulse 2.5s ease-in-out infinite;
}

.lightning-random .lightning-path {
    animation: lightningRandom 2.5s ease-in-out infinite;
}

@keyframes lightningRandom {

    0%,
    100% {
        opacity: 0.2;
        stroke-width: 2;
    }

    15%,
    85% {
        opacity: 1;
        stroke-width: 5;
    }

    30%,
    70% {
        opacity: 0.6;
        stroke-width: 3;
    }

    45%,
    55% {
        opacity: 0.9;
        stroke-width: 4;
    }
}

/* 尺寸样式 */
.size-small {
    height: 60px;
}

.size-small .lightning-path {
    stroke-width: 2;
}

.size-small .lightning-glow {
    stroke-width: 4;
}

.size-large {
    height: 150px;
}

.size-large .lightning-path {
    stroke-width: 4;
}

.size-large .lightning-glow {
    stroke-width: 12;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .simple-lightning {
        height: 60px;
    }

    .lightning-path {
        stroke-width: 2;
    }

    .lightning-glow {
        stroke-width: 4;
    }
}
</style>