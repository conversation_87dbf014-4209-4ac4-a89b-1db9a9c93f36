<template>
    <div class="lightning-integration">
        <!-- 标题区域 -->
        <div class="header-section">
            <h1 class="title">
                <span class="title-text">Live Web</span>
                <SimpleLightning variant="fast" size="small" />
            </h1>
            <p class="subtitle">将静态闪电转换为动态效果</p>
        </div>

        <!-- 功能展示区域 -->
        <div class="features-section">
            <div class="feature-card">
                <div class="feature-icon">
                    <SimpleLightning variant="normal" size="small" />
                </div>
                <h3>基础闪烁</h3>
                <p>标准的闪电闪烁效果，适合大多数场景</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <SimpleLightning variant="fast" size="small" />
                </div>
                <h3>快速闪烁</h3>
                <p>更快的闪烁频率，提供强烈的视觉冲击</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <SimpleLightning variant="slow" size="small" />
                </div>
                <h3>慢速脉冲</h3>
                <p>柔和的脉冲效果，适合需要温和视觉的场景</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <SimpleLightning variant="random" size="small" />
                </div>
                <h3>随机闪烁</h3>
                <p>不规则的闪烁模式，模拟真实的闪电效果</p>
            </div>
        </div>

        <!-- 高级效果展示 -->
        <div class="advanced-section">
            <h2>高级动态效果</h2>
            <div class="advanced-demo">
                <AdvancedLightning :show-controls="true" height="120px" />
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="usage-section">
            <h2>使用方法</h2>
            <div class="code-examples">
                <div class="code-example">
                    <h4>基础使用</h4>
                    <pre><code>&lt;SimpleLightning /&gt;</code></pre>
                </div>
                <div class="code-example">
                    <h4>自定义配置</h4>
                    <pre><code>&lt;SimpleLightning 
  variant="fast" 
  size="large" 
/&gt;</code></pre>
                </div>
                <div class="code-example">
                    <h4>高级组件</h4>
                    <pre><code>&lt;AdvancedLightning 
  :show-controls="true"
  height="150px"
/&gt;</code></pre>
                </div>
            </div>
        </div>

        <!-- 效果对比 -->
        <div class="comparison-section">
            <h2>效果对比</h2>
            <div class="comparison-grid">
                <div class="comparison-item">
                    <h4>静态闪电</h4>
                    <div class="static-lightning">
                        <svg viewBox="0 0 400 100" class="static-svg">
                            <path d="M 20 50 L 80 20 L 120 80 L 180 30 L 220 70 L 280 40 L 320 60 L 380 50"
                                stroke="#ffff00" stroke-width="3" fill="none" stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                    </div>
                    <p>静态效果，缺乏活力</p>
                </div>

                <div class="comparison-item">
                    <h4>动态闪电</h4>
                    <SimpleLightning variant="fast" />
                    <p>动态效果，充满活力</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import SimpleLightning from '@/components/SimpleLightning.vue'
import AdvancedLightning from '@/components/AdvancedLightning.vue'
</script>

<style scoped>
.lightning-integration {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
    min-height: 100vh;
    color: #fff;
}

.header-section {
    text-align: center;
    margin-bottom: 50px;
    padding: 40px 20px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    border: 1px solid #333;
}

.title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    color: #ffff00;
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.title-text {
    background: linear-gradient(45deg, #ffff00, #ffaa00);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    color: #ccc;
    font-size: 1.2rem;
}

.features-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 30px;
    border-radius: 12px;
    border: 1px solid #333;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 255, 0, 0.2);
}

.feature-icon {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
}

.feature-card h3 {
    color: #ffff00;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.feature-card p {
    color: #ccc;
    line-height: 1.6;
}

.advanced-section {
    margin-bottom: 50px;
    padding: 30px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    border: 1px solid #333;
}

.advanced-section h2 {
    color: #ffff00;
    margin-bottom: 20px;
    text-align: center;
}

.advanced-demo {
    display: flex;
    justify-content: center;
}

.usage-section {
    margin-bottom: 50px;
}

.usage-section h2 {
    color: #ffff00;
    margin-bottom: 30px;
    text-align: center;
}

.code-examples {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.code-example {
    background: #1a1a1a;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #333;
}

.code-example h4 {
    color: #ffff00;
    margin-bottom: 15px;
}

.code-example pre {
    background: #000;
    padding: 15px;
    border-radius: 6px;
    overflow-x: auto;
    border: 1px solid #555;
}

.code-example code {
    color: #00ff00;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.comparison-section {
    margin-bottom: 50px;
}

.comparison-section h2 {
    color: #ffff00;
    margin-bottom: 30px;
    text-align: center;
}

.comparison-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
}

.comparison-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 30px;
    border-radius: 12px;
    border: 1px solid #333;
    text-align: center;
}

.comparison-item h4 {
    color: #ffff00;
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.comparison-item p {
    color: #ccc;
    margin-top: 15px;
}

.static-lightning {
    background: #000;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
}

.static-svg {
    width: 100%;
    height: 60px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .lightning-integration {
        padding: 10px;
    }

    .title {
        font-size: 2rem;
        flex-direction: column;
        gap: 10px;
    }

    .features-section {
        grid-template-columns: 1fr;
    }

    .code-examples {
        grid-template-columns: 1fr;
    }

    .comparison-grid {
        grid-template-columns: 1fr;
    }
}
</style>