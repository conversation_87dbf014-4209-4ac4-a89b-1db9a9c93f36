<!-- 需要绑定邮箱提示 -->
<template>
    <div class="anchor-call">
        <var-popup position="bottom" v-model:show="showAnchorCall" :safe-area="true" :default-style="false"
            @click-overlay="close">
            <div class="anchor-call-dialog">
                <div class="anchor-call-img">
                    <img src="@/assets/test/5.jpg" alt="" class="anchor-call-img-item">
                </div>
                <div class="anchor-call-dialog-content">
                    <div class="anchor-call-dialog-content-top">
                        <div class="anchor-call-dialog-content-top-1">
                            <div class="name">
                                DUVAI❤️‍🔥💞DUVAI
                            </div>
                            <div class="age">
                                <img src="@/assets/home/<USER>" alt="">
                                <span>32</span>
                            </div>
                        </div>
                        <!--每分钟所花的金币数-->
                        <div class="anchor-call-dialog-content-top-2">
                            <span>Call Price:20 coins/minute</span>
                        </div>
                    </div>
                    <div class="anchor-call-dialog-content-bottom">
                        <div class="accept-btn-tips">
                            <img src="@/assets/home/<USER>" alt="">
                            <span>Ringing- Answer time!</span>
                            <div class="rotate-icon">

                            </div>
                        </div>
                        <div class="refuse-btn" @click="refuseCall">

                        </div>
                        <div class="accept-panel">
                            <div class="w w1"></div>
                            <div class="w w2"></div>
                            <div class="accept-btn"></div>

                        </div>
                    </div>
                    <div class="pointer">

                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">

export default {
    name: 'anchorCall',
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {

            showAnchorCall: this.show
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.showAnchorCall = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        close() {
            this.showAnchorCall = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
        confirm() {

            this.$emit('confirm')
            this.close()
        },
/*************  ✨ Windsurf Command ⭐  *************/
/*******  2dfae431-d11d-45db-b6ef-9bcf34ad0f6c  *******/
        refuseCall() {
            this.close()
        }
    }
}
</script>
<style lang='scss' scoped>
.anchor-call-dialog {
    display: flex;
    justify-content: center;
    height: 100svh;
    font-family: var(--var-font-family-urbanist);

    .anchor-call-img {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        left: 0;
        top: 0;
        position: absolute;
        z-index: 1;

        &-item {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba($color: #000000, $alpha: .3);
        }

    }

    .anchor-call-dialog-content {
        position: relative;
        width: 100%;
        height: 100%;
        padding: 143px 0 61px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        z-index: 2;

        .anchor-call-dialog-content-top {
            width: 100%;
            display: flex;
            align-items: center;
            flex-direction: column;
            gap: 12px;

            &-1 {
                display: flex;
                align-items: center;
                justify-content: center;

                .name {
                    font-size: 20px;
                    font-weight: 700;
                    font-style: italic;
                    line-height: 24px;
                    margin-inline-end: 2px;
                    color: #fff;
                }

                .age {
                    padding: 0px 4px;
                    display: flex;
                    align-items: center;
                    gap: 1px;
                    background-color: $common-color;
                    border-radius: 6px;

                    img {
                        width: 10px;
                        height: 10px;
                    }

                    span {
                        font-size: 10px;
                        font-weight: 800;
                        font-style: italic;
                        line-height: 12px;
                        color: #fff;
                    }
                }
            }

            &-2 {
                padding: 10px 20px;
                box-sizing: border-box;
                font-size: 15px;
                font-weight: 700;
                line-height: 18px;
                color: #fff;
                background: linear-gradient(90deg, rgba(255, 129, 43, 0.2) 0%, rgba(255, 11, 194, 0.2) 100%);
                border-radius: 20px;
            }
        }

        .anchor-call-dialog-content-bottom {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 115px;
            position: relative;

            .refuse-btn {
                width: 70px;
                height: 70px;
                background-image: url('@/assets/anchor/refuse.png');
                background-size: contain;
                background-repeat: no-repeat;
                border-radius: 24px;
                overflow: hidden;
            }

            .accept-panel {
                width: 114px;
                height: 114px;
                margin-right: -22px;
                position: relative;

                .w {
                    opacity: 0;
                    width: 70px;
                    height: 70px;
                    top: 24px;
                    left: 24px;
                    border-radius: 24px;
                    background: rgb(96, 217, 111);
                    position: absolute;
                    animation: innerWave 2s infinite linear;
                    animation-delay: 1s;

                    &.w2 {
                        animation-delay: 1.4s;
                    }
                }

                .accept-btn {
                    top: 24px;
                    width: 70px;
                    height: 70px;
                    background-image: url('@/assets/anchor/accept.png');
                    background-size: contain;
                    background-repeat: no-repeat;
                    pointer-events: auto;
                    border-radius: 24px;
                    position: absolute;
                    left: 24px;
                    overflow: hidden;
                }
            }

            .accept-btn-tips {
                position: absolute;
                top: -70px;
                right: 0;
                display: flex;
                align-items: center;
                gap: 8px;
                width: max-content;
                padding: 10px 12px;
                box-sizing: border-box;
                background: #00000033;
                border-radius: 10px;

                img {
                    width: 36px;
                    height: 36px;
                    object-fit: cover;
                    border-radius: 50%;
                }

                span {
                    font-size: 15px;
                    font-weight: 700;
                    line-height: 18px;
                    color: #fff;
                }

                .rotate-icon {
                    position: absolute;
                    width: 0px;
                    height: 0px;
                    border-left: 10px solid transparent;
                    border-right: 10px solid transparent;
                    border-top: 10px solid #00000033;
                    right: 30px;
                    bottom: -10px;
                }
            }
        }

        .pointer {
            width: 48px;
            height: 48px;
            background-image: url('@/assets/anchor/pointer.png') ;
            background-size: contain;
            position: absolute;
            bottom: 50px;
            right: 51px;
            animation: up-down 2s linear infinite;
            pointer-events: none;
        }
    }
}
</style>