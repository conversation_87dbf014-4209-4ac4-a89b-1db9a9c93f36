<!--礼物动画-->
<template>
    <div class="gift-animation-container">
        <div class="gift-list">
            <div 
                v-for="gift in giftAnimations" 
                :key="gift.id" 
                class="gift-item"
                :class="{ 'gift-leaving': gift.isLeaving }"
                :style="{ top: gift.top + 'px' }"
            >
                <div class="gift-content">
                    <div class="gift-content-left"
                        :class="{ 'is-vip': gift.vipType === 1, 'is-svip': gift.vipType === 2 }">
                        <img src="@/assets/home/<USER>" alt="" class="user-avatar">
                        <div class="gift-content-left-text">
                            <span class="gift-user">{{ gift.userName }}</span>
                            <span class="gift-name">{{ gift.giftName }}</span>
                        </div>
                        <!--礼物图片-->
                        <img :src="gift.giftUrl" alt="" class="gift-img" />
                    </div>
                    <span class="gift-count" :class="{ 'gift-count-bouncing': gift.isBouncing }">
                        <span class="gift-count-x">x</span>
                        {{ gift.count }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup name="GiftAnimation">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

interface GiftAnimation {
    id: number
    userName: string
    giftName: string
    giftUrl: string
    vipType: number
    count: number
    top: number
    timestamp: number
    isLeaving: boolean
    isBouncing: boolean
    removeTimer: any
}

interface Props {
    containerHeight?: number // 容器高度，默认80vw
    giftDuration?: number // 礼物显示时长，默认3000ms
    maxGifts?: number // 最大礼物数量，默认5个
}

const props = withDefaults(defineProps<Props>(), {
    containerHeight: 300, // 固定高度，避免window.innerWidth的问题
    giftDuration: 3000,
    maxGifts: 5
})

const emit = defineEmits<{
    giftAdded: [gift: GiftAnimation]
    giftRemoved: [giftId: number]
}>()

const giftAnimations = ref<GiftAnimation[]>([])
const giftIdCounter = ref(0)

// 重新计算礼物位置
const recalculateGiftPositions = () => {
    const visibleGifts = giftAnimations.value.filter(gift => !gift.isLeaving)
    const sortedGifts = visibleGifts.sort((a, b) => b.timestamp - a.timestamp)

    sortedGifts.forEach((gift, index) => {
        const bottomPosition = props.containerHeight - 60 - (index * 60)
        gift.top = Math.max(0, bottomPosition)
    })

    console.log('重新计算位置:', sortedGifts.map(g => `${g.userName}: ${g.top}px`))
}

// 移除礼物动画
const removeGiftAnimation = (giftId: number) => {
    const index = giftAnimations.value.findIndex(gift => gift.id === giftId)
    if (index > -1) {
        const gift = giftAnimations.value[index]
        gift.isLeaving = true

        // 立即重新计算其他礼物的位置
        recalculateGiftPositions()

        // 等待动画完成后移除
        setTimeout(() => {
            const removeIndex = giftAnimations.value.findIndex(g => g.id === giftId)
            if (removeIndex > -1) {
                giftAnimations.value.splice(removeIndex, 1)
                // console.log('移除礼物动画:', giftId, '剩余礼物数量:', giftAnimations.value.length)
                emit('giftRemoved', giftId)
            }
        }, 300)
    }
}

// 添加礼物动画
const addGiftAnimation = (userName: string, giftName: string, giftUrl: string, vipType: number) => {
    // console.log('GiftAnimation组件: 开始添加礼物', { userName, giftName, giftUrl, vipType })
    
    // 检查是否已存在相同用户和礼物的动画
    const existingGift = giftAnimations.value.find(gift => 
        gift.userName === userName && 
        gift.giftName === giftName && 
        !gift.isLeaving
    )

    if (existingGift) {
        // 如果存在相同礼物，增加数量并触发跳动效果
        existingGift.count += 1
        
        // 先移除跳动类，然后重新添加以触发动画
        existingGift.isBouncing = false
        
        // 使用 nextTick 确保 DOM 更新后再添加动画类
        nextTick(() => {
            existingGift.isBouncing = true
            
            // 重置跳动效果
            setTimeout(() => {
                existingGift.isBouncing = false
            }, 300)
        })
        
        // 重新计算消失时间（从当前时间开始重新计时）
        clearTimeout(existingGift.removeTimer)
        existingGift.removeTimer = setTimeout(() => {
            removeGiftAnimation(existingGift.id)
        }, props.giftDuration)
        
        // console.log('增加礼物数量:', existingGift.userName, existingGift.giftName, '数量:', existingGift.count, '重新计时')
        return
    }

    // 如果礼物数量超过限制，移除最旧的礼物
    if (giftAnimations.value.length >= props.maxGifts) {
        const oldestGift = giftAnimations.value.find(gift => !gift.isLeaving)
        if (oldestGift) {
            removeGiftAnimation(oldestGift.id)
        }
    }

    const giftId = ++giftIdCounter.value

    const gift: GiftAnimation = {
        id: giftId,
        userName,
        giftName,
        giftUrl,
        vipType,
        count: 1,
        top: 0,
        timestamp: Date.now() + giftIdCounter.value,
        isLeaving: false,
        isBouncing: false,
        removeTimer: null
    }

    // 添加新礼物
    giftAnimations.value.push(gift)

    // 重新计算所有礼物的位置
    recalculateGiftPositions()

    // console.log('添加礼物动画:', gift, '当前礼物数量:', giftAnimations.value.length)

    // 设置消失时间
    gift.removeTimer = setTimeout(() => {
        removeGiftAnimation(giftId)
    }, props.giftDuration)

    emit('giftAdded', gift)
}

// 清空所有礼物
const clearAllGifts = () => {
    giftAnimations.value.forEach(gift => {
        if (gift.removeTimer) {
            clearTimeout(gift.removeTimer)
        }
    })
    giftAnimations.value = []
}

// 组件卸载时清理定时器
onUnmounted(() => {
    clearAllGifts()
})

// 暴露方法给父组件 - 必须在最后
defineExpose({
    addGiftAnimation,
    removeGiftAnimation,
    clearAllGifts,
    giftAnimations
})
</script>

<style lang="scss" scoped>
.gift-animation-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 999;
    overflow: hidden;

    .gift-list {
        position: relative;
        width: 100%;
        height: 100%;
    }

    .gift-item {
        position: absolute;
        left: 0;
        height: 50px;
        border-radius: 25px;
        display: flex;
        align-items: center;
        padding: 0 15px;
        box-sizing: border-box;
        animation: slideInFromLeft 0.5s ease-out forwards;
        transition: top 0.3s ease-out, transform 0.3s ease-out, opacity 0.3s ease-out;

        .gift-content {
            display: flex;
            align-items: center;
            gap: 10px;
            width: 100%;

            .gift-content-left {
                width: 171px;
                height: 36px;
                background: rgba(0, 0, 0, 0.3);
                border-radius: 30px;
                display: flex;
                align-items: center;
                gap: 3px;
                padding: 0 3px;
                box-sizing: border-box;
                position: relative;

                &.is-vip {
                    background: url('@/assets/live/vip-send-gift-bg.png') no-repeat center center;
                    background-size: 100% 100%;
                }

                &.is-svip {
                    background: url('@/assets/live/svip-send-gift-bg.png') no-repeat center center;
                    background-size: 100% 100%;
                }

                .user-avatar {
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                }

                .gift-content-left-text {
                    display: flex;
                    flex-direction: column;
                    gap: 1px;

                    .gift-user {
                        width: 80px;
                        font-size: 15px;
                        color: #fff;
                        font-weight: 700;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .gift-name {
                        font-size: 10px;
                        line-height: 12px;
                        font-weight: 700;
                        color: rgba($color: #fff, $alpha: 0.7);
                    }
                }

                .gift-img {
                    position: absolute;
                    right: 0;
                    width: 50px;
                    height: 50px;
                }
            }

            .gift-count {
                margin-left: 7px;
                font-size: 24px;
                font-weight: 700;
                color: #fff;
                display: flex;
                align-items: center;
                gap: 2px;

                .gift-count-x {
                    font-size: 14px;
                    font-weight: 500;
                }

                &.gift-count-bouncing {
                    animation: giftCountBounce 0.3s ease-in-out !important;
                    transform-origin: center;
                }
            }
        }

        &.gift-leaving {
            transform: translateX(100%);
            opacity: 0;
        }
    }
}

@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes giftCountBounce {
    0% {
        transform: scale(1);
        color: #fff;
    }
    25% {
        transform: scale(1.4);
        color: #FFD700;
    }
    50% {
        transform: scale(1.2);
        color: #FF6B9D;
    }
    75% {
        transform: scale(1.1);
        color: #FFD700;
    }
    100% {
        transform: scale(1);
        color: #fff;
    }
}
</style>
