<!-- 选择性别弹窗 -->
<template>
    <div>
        <var-popup position="bottom" v-model:show="reportAnchorDialogShow" @click-overlay="close" :safe-area="true"
            :default-style="false">
            <div class="report-anchor-dialog">
                <div class="select-box-item">
                    <span>Report</span>
                </div>
                <div class="select-box-item">
                    <span>Block</span>
                </div>
                <div class="confirm-btn" @click="close">
                    Cancel
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">
export default {
    name: 'reportAnchorDialog',
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            selectGender: 'male',
            reportAnchorDialogShow: this.show
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.reportAnchorDialogShow = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        close() {
            this.reportAnchorDialogShow = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
    }
}
</script>
<style lang='scss' scoped>
.report-anchor-dialog {
    background: rgb(24, 25, 47);
    border-radius: 16px 16px 0 0;
    padding: 20px;
    font-family: var(--var-font-family-type3);

    .select-box-item {
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgb(34, 35, 65);
        border-radius: 17px;
        margin: 0px auto 13px;
        padding: 0px 20px 0px 13px;
        height: 44px;

        span {
            font-size: 16px;
            font-weight: 400;
            line-height: 14px;
            color: #fff;
        }

        img {
            width: 34px;
            height: 34px;
        }
    }

    .confirm-btn {
        width: 100%;
        height: 52px;
        font-size: 16px;
        color: #fff;
        text-align: center;
        line-height: 52px;

    }
}
</style>