<template>
    <div class="lazy-image-container" ref="container" :style="containerStyle">
        <img :src="placeholderSrc" :data-src="realSrc" :alt="alt" :class="['lazy-image', { 'loaded': isLoaded }]"
            @load="onPlaceholderLoad" ref="image" />
        <div v-if="!isLoaded" class="loading-overlay">
            <div class="loading-spinner"></div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'

interface Props {
    src: string
    placeholderSrc?: string
    alt?: string
    width?: string
    height?: string
    className?: string
}

const props = withDefaults(defineProps<Props>(), {
    placeholderSrc: '/src/assets/common/position.png',
    alt: '',
    width: '100%',
    height: '100%',
    className: ''
})

const container = ref<HTMLElement>()
const image = ref<HTMLImageElement>()
const isLoaded = ref(false)
const isInView = ref(false)
const observer = ref<IntersectionObserver | null>(null)

const realSrc = computed(() => props.src)

const containerStyle = computed(() => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
}))

const onPlaceholderLoad = () => {
    // 占位图加载完成后，开始加载真实图片
    if (isInView.value) {
        loadRealImage()
    }
}

const loadRealImage = () => {
    if (!image.value || isLoaded.value) return

    const realImg = new Image()
    realImg.onload = () => {
        if (image.value) {
            image.value.src = realSrc.value
            image.value.classList.add('loaded')
            isLoaded.value = true
        }
    }
    realImg.onerror = () => {
        console.warn('Failed to load image:', realSrc.value)
    }
    realImg.src = realSrc.value
}

const setupIntersectionObserver = () => {
    if (!container.value) return

    observer.value = new IntersectionObserver(
        (entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    isInView.value = true
                    if (image.value?.complete) {
                        loadRealImage()
                    }
                    // 一旦加载，就不再观察
                    if (observer.value) {
                        observer.value.unobserve(entry.target)
                    }
                }
            })
        },
        {
            rootMargin: '50px', // 提前50px开始加载
            threshold: 0.1
        }
    )

    observer.value.observe(container.value)
}

onMounted(() => {
    setupIntersectionObserver()
})

onUnmounted(() => {
    if (observer.value) {
        observer.value.disconnect()
    }
})
</script>

<style scoped>
.lazy-image-container {
    position: relative;
    overflow: hidden;
}

.lazy-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: opacity 0.3s ease, transform 0.3s ease;
    opacity: 0.7;
    transform: scale(0.95);
    flex-shrink: 0;
}

.lazy-image.loaded {
    opacity: 1;
    transform: scale(1);
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.1);
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

</style>