<!--  -->
<template>
    <div>
        <var-popup position="bottom" v-model:show="showLevelPopup" @click-overlay="close" :safe-area="true"
            :default-style="false" :overlay-style="{
                opacity: 0,
            }">
            <div class="level-popup">
                <div class="main-container">
                    <div class="main-container-header">
                        <img src="@/assets/home/<USER>" alt="" class="main-container-header-avatar">
                        <div class="main-container-header-title">
                            <span class="main-container-header-title-name">Visitor-22323</span>
                            <div class="main-container-header-title-level" :class="getVipLevelClass(level)">
                                <span>{{ level }}</span>
                            </div>
                        </div>
                    </div>
                    <!--中间的等级轮播-->
                    <div class="main-container-swiper">
                        <Swiper :modules="[Autoplay, Pagination]" :slides-per-view="1.04" :space-between="8"
                            :autoplay="false" :pagination="false" class="mySwiper" @slide-change="handleSlideChange">
                            <SwiperSlide v-for="(item, index) in levelList" :key="index">
                                <div class="main-container-swiper-item">
                                    <img :src="returnLevelTagImg(index + 1)" alt=""
                                        class="main-container-swiper-item-img">
                                    <div class="main-container-swiper-item-box" :class="'level' + '_' + (index + 1)">
                                        <div class="main-container-swiper-item-box-title">
                                            {{ returnLevelTitle(index + 1) }}
                                        </div>
                                        <div class="lock-level" v-if="index * 10 > level">
                                            The level still locked
                                        </div>
                                        <!--下一级所需要的经验-->
                                        <div v-if="index === 0" class="next-level-exp">
                                            <div class="next-level-exp-top">
                                                <span class="now-level">Lv.0</span>
                                                <span class="next-level">Lv.1</span>
                                            </div>
                                            <div class="exp-progress">
                                                <div class="exp-progress-bar"
                                                    :style="{ width: '50%', 'min-width': '10%' }">

                                                </div>
                                            </div>
                                            <div class="exp-progress-box">
                                                1 EXP needed to level up
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </SwiperSlide>
                        </Swiper>
                        <div class="main-container-level-list">
                            <div class="main-container-level-list-title">
                                Unlocked Rewards
                            </div>
                            <div class="main-container-level-list-item"
                                v-for="(item, index) in levelList[activeSwiperIndex].list"
                                :class="'item' + '_' + activeSwiperIndex" :key="item.title">
                                <div class="list-item-left">
                                    <div class="list-item-left-title">
                                        {{ item.title }}
                                    </div>
                                    <div class="list-item-left-name">
                                        <span class="name">{{ item.name }}</span>
                                        <span class="desc" v-if="item.desc">{{ item.desc }}</span>
                                    </div>
                                </div>
                                <div v-if="!item.isGet" class="luck-box">
                                    <img src="@/assets/vip/luck.png" alt="">
                                </div>

                            </div>

                        </div>
                    </div>
                    <div class="main-container-level-btn">
                    <div class="main-container-level-btn-box" :class="'btn' + '_' + activeSwiperIndex">
                        How to level up
                    </div>
                </div>
                </div>
                
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="LevelPopup">
import { ref, watch } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay, Pagination } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/autoplay'
import 'swiper/css/pagination'

const props = defineProps<Props>()
interface Props {
    show: boolean,
    userInfo: any
}
const emit = defineEmits(['update:show'])

const showLevelPopup = ref(false)


watch(() => showLevelPopup.value, (newVal: boolean) => {
    emit('update:show', newVal)
})


watch(() => props.show, (newVal: boolean) => {
    showLevelPopup.value = newVal
}, {
    immediate: true
})
/**
 * Close the popup.
 */
const close = () => {
    showLevelPopup.value = false
}
const level = ref(Math.floor(Math.random() * 99) + 1)
const levelList = ref([
    {
        list: [
            {
                title: 'Lv.1',
                name: 'Level medal',
                desc: '',
                img: '',
                isGet: false,
                type: 1,
            }
        ]
    },
    {
        list: [
            {
                title: 'Lv.10',
                name: 'Level medal',
                desc: '',
                img: '',
                isGet: false,
                type: 1,
            },
            {
                title: 'Lv.10',
                name: 'Level exclusive gifts',
                desc: 'Unlock Crystal Trojan Gift',
                img: '',
                isGet: false,
                type: 2,
            }
        ]
    },
    {
        list: [
            {
                title: 'Lv.20',
                name: 'Level medal',
                desc: '',
                img: '',
                isGet: false,
                type: 1,
            },
            {
                title: 'Lv.20',
                name: 'Level exclusive gifts',
                desc: 'Unlock Diamond Ring Gift',
                img: '',
                isGet: false,
                type: 2,
            },
            {
                title: 'Lv.20',
                name: 'Special avatar frame',
                desc: 'Unlock Golden Crown Frame',
                img: '',
                isGet: false,
                type: 3,
            }
        ]
    },
    {
        list: [
            {
                title: 'Lv.30',
                name: 'Level medal',
                desc: '',
                img: '',
                isGet: false,
                type: 1,
            },
            {
                title: 'Lv.30',
                name: 'Level exclusive gifts',
                desc: 'Unlock Luxury Car Gift',
                img: '',
                isGet: false,
                type: 2,
            },
            {
                title: 'Lv.30',
                name: 'Special effects',
                desc: 'Unlock Rainbow Trail Effect',
                img: '',
                isGet: false,
                type: 4,
            }
        ]
    },
    {
        list: [
            {
                title: 'Lv.40',
                name: 'Level medal',
                desc: '',
                img: '',
                isGet: false,
                type: 1,
            },
            {
                title: 'Lv.40',
                name: 'Level exclusive gifts',
                desc: 'Unlock Private Jet Gift',
                img: '',
                isGet: false,
                type: 2,
            },
            {
                title: 'Lv.40',
                name: 'VIP chat privileges',
                desc: 'Unlock VIP Chat Room Access',
                img: '',
                isGet: false,
                type: 5,
            }
        ]
    },
    {
        list: [
            {
                title: 'Lv.50',
                name: 'Level medal',
                desc: '',
                img: '',
                isGet: false,
                type: 1,
            },
            {
                title: 'Lv.50',
                name: 'Level exclusive gifts',
                desc: 'Unlock Yacht Gift',
                img: '',
                isGet: false,
                type: 2,
            },
            {
                title: 'Lv.50',
                name: 'Exclusive emoji pack',
                desc: 'Unlock Premium Emoji Collection',
                img: '',
                isGet: false,
                type: 6,
            }
        ]
    },
    {
        list: [
            {
                title: 'Lv.60',
                name: 'Level medal',
                desc: '',
                img: '',
                isGet: false,
                type: 1,
            },
            {
                title: 'Lv.60',
                name: 'Level exclusive gifts',
                desc: 'Unlock Island Resort Gift',
                img: '',
                isGet: false,
                type: 2,
            },
            {
                title: 'Lv.60',
                name: 'Priority support',
                desc: '24/7 VIP Customer Support',
                img: '',
                isGet: false,
                type: 7,
            }
        ]
    },
    {
        list: [
            {
                title: 'Lv.70',
                name: 'Level medal',
                desc: '',
                img: '',
                isGet: false,
                type: 1,
            },
            {
                title: 'Lv.70',
                name: 'Level exclusive gifts',
                desc: 'Unlock Space Station Gift',
                img: '',
                isGet: false,
                type: 2,
            },
            {
                title: 'Lv.70',
                name: 'Exclusive title',
                desc: 'Unlock "Legendary" Title',
                img: '',
                isGet: false,
                type: 8,
            }
        ]
    },
    {
        list: [
            {
                title: 'Lv.80',
                name: 'Level medal',
                desc: '',
                img: '',
                isGet: false,
                type: 1,
            },
            {
                title: 'Lv.80',
                name: 'Level exclusive gifts',
                desc: 'Unlock Time Machine Gift',
                img: '',
                isGet: false,
                type: 2,
            },
            {
                title: 'Lv.80',
                name: 'Custom profile theme',
                desc: 'Unlock Dark Theme Profile',
                img: '',
                isGet: false,
                type: 9,
            }
        ]
    },
    {
        list: [
            {
                title: 'Lv.90',
                name: 'Level medal',
                desc: '',
                img: '',
                isGet: false,
                type: 1,
            },
            {
                title: 'Lv.90',
                name: 'Level exclusive gifts',
                desc: 'Unlock Universe Gift',
                img: '',
                isGet: false,
                type: 2,
            },
            {
                title: 'Lv.90',
                name: 'Legendary status',
                desc: 'Become a Legendary User',
                img: '',
                isGet: false,
                type: 10,
            }
        ]
    }
])

const activeSwiperIndex = ref(0)
// 更优雅的VIP等级class获取方法
const getVipLevelClass = (vipLevel: number) => {
    if (!vipLevel || vipLevel < 1 || vipLevel > 100) return ''

    // 计算等级组 (1-9为第1组，10-19为第2组，以此类推)
    const levelGroup = Math.ceil(vipLevel / 10)

    // 确保等级组在1-9范围内
    if (levelGroup >= 1 && levelGroup <= 9) {
        return `vip-level-${levelGroup}`
    }

    return ''
}

const returnLevelTitle = (level: number) => {
    if (level === 1) {
        return 'Lv.1 - Lv.9'
    } else {
        const startLevel = (level - 1) * 10
        const endLevel = level * 10 - 1
        return `Lv.${startLevel} - Lv.${endLevel}`
    }
}

const returnLevelTagImg = (level: number) => {
    // 确保level在1-9范围内
    const validLevel = Math.max(1, Math.min(10, level))
    // 使用动态导入
    return new URL(`../../assets/vip/level-${validLevel}.png`, import.meta.url).href
}

const handleSlideChange = (swiper: any) => {
    console.log(swiper.activeIndex)
    activeSwiperIndex.value = swiper.activeIndex
}
</script>
<style lang="scss" scoped>
.level-popup {
    width: 100%;
    overflow: hidden;
    font-family: var(--font-family-urbanist);
    .main-container {
        padding-top: 20px;
        padding-bottom: calc(env(safe-area-inset-bottom) + 120px);
        width: 100%;
        height: 470px;
        border-radius: 16px 16px 0 0;
        display: flex;
        flex-direction: column;
        background-color: rgba(10, 10, 27, 1);
        overflow-y: auto;
        box-sizing: border-box;

        .main-container-header {
            display: flex;
            align-items: center;
            padding-left: 15px;
            box-sizing: border-box;

            .main-container-header-avatar {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                object-fit: cover;
                object-position: top;
                margin-right: 10px;
            }

            .main-container-header-title {
                display: flex;
                align-items: center;
                gap: 4px;

                .main-container-header-title-name {
                    font-size: 16px;
                    font-weight: 700;
                    color: #fff;
                    line-height: 19px;
                }

                .main-container-header-title-level {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    padding: 0 6px 0 24px;
                    border-radius: 30px;
                    background: linear-gradient(90deg, #989898 0%, #D4D4D4 100%);
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    background-position: left center;

                    // VIP等级背景图样式
                    &.vip-level-1 {
                        background-image: url('@/assets/vip/1-9.png');
                    }

                    &.vip-level-2 {
                        background-image: url('@/assets/vip/10-19.png');
                    }

                    &.vip-level-3 {
                        background-image: url('@/assets/vip/20-29.png');
                    }

                    &.vip-level-4 {
                        background-image: url('@/assets/vip/30-39.png');
                    }

                    &.vip-level-5 {
                        background-image: url('@/assets/vip/40-49.png');
                    }

                    &.vip-level-6 {
                        background-image: url('@/assets/vip/50-59.png');
                    }

                    &.vip-level-7 {
                        background-image: url('@/assets/vip/60-69.png');
                    }

                    &.vip-level-8 {
                        background-image: url('@/assets/vip/70-79.png');
                    }

                    &.vip-level-9 {
                        background-image: url('@/assets/vip/80-89.png');
                    }

                    &.vip-level-10 {
                        background-image: url('@/assets/vip/90-99.png');
                    }

                    span {
                        font-size: 10px;
                        font-weight: 700;
                        line-height: 14px;
                        color: #fff;
                    }
                }
            }
        }

        .main-container-swiper {
            padding-left: 15px;
            box-sizing: border-box;
            // margin-top: 20px;

            .main-container-swiper-item {
                padding-top: 30px;
                position: relative;

                .main-container-swiper-item-img {
                    position: absolute;
                    top: 0;
                    right: 10px;
                    width: 120px;
                    height: 120px;
                }

                .main-container-swiper-item-box {
                    width: 100%;
                    height: 120px;

                    background-repeat: no-repeat;
                    border-radius: 10px;
                    padding: 20px 0 0 20px;
                    box-sizing: border-box;

                    .main-container-swiper-item-box-title {
                        font-size: 18px;
                        font-weight: 700;
                        color: #fff;
                        line-height: 22px;
                    }

                    .lock-level {
                        margin-top: 20px;
                        font-size: 12px;
                        font-weight: 600;
                        color: rgba($color: #fff, $alpha: .5);
                        line-height: 14px;
                    }

                    .next-level-exp {
                        margin-top: 15px;
                        width: 150px;
                        display: flex;
                        flex-direction: column;

                        .next-level-exp-top {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            font-size: 12px;
                            font-weight: 600;
                            line-height: 14px;

                            .now-level {
                                color: #fff;
                            }

                            .next-level {
                                color: rgba($color: #fff, $alpha: .5);
                            }

                        }

                        .exp-progress {
                            margin-top: 5px;
                            width: 100%;
                            height: 4px;
                            background-color: rgba($color: #fff, $alpha: .1);
                            border-radius: 4px;
                            position: relative;

                            .exp-progress-bar {
                                position: absolute;
                                left: 0;
                                top: 0;
                                height: 100%;
                                background-color: #fff;
                                border-radius: 4px;
                            }
                        }

                        .exp-progress-box {
                            margin-top: 6px;
                            font-size: 10px;
                            font-weight: 700;
                            color: rgba($color: #fff, $alpha: .5);
                            line-height: 12px;
                        }
                    }

                    &.level_1 {
                        background: url('@/assets/vip/level-1-bg.png') no-repeat;
                        background-size: 100% 100%;
                    }

                    &.level_2 {
                        background: url('@/assets/vip/level-2-bg.png') no-repeat;
                        background-size: 100% 100%;
                    }

                    &.level_3 {
                        background: url('@/assets/vip/level-3-bg.png') no-repeat;
                        background-size: 100% 100%;
                    }

                    &.level_4 {
                        background: url('@/assets/vip/level-4-bg.png') no-repeat;
                        background-size: 100% 100%;
                    }

                    &.level_5 {
                        background: url('@/assets/vip/level-5-bg.png') no-repeat;
                        background-size: 100% 100%;
                    }

                    &.level_6 {
                        background: url('@/assets/vip/level-6-bg.png') no-repeat;
                        background-size: 100% 100%;
                    }

                    &.level_7 {
                        background: url('@/assets/vip/level-7-bg.png') no-repeat;
                        background-size: 100% 100%;
                    }

                    &.level_8 {
                        background: url('@/assets/vip/level-8-bg.png') no-repeat;
                        background-size: 100% 100%;
                    }

                    &.level_9 {
                        background: url('@/assets/vip/level-9-bg.png') no-repeat;
                        background-size: 100% 100%;
                    }

                    &.level_10 {
                        background: url('@/assets/vip/level-10-bg.png') no-repeat;
                        background-size: 100% 100%;
                    }
                }
            }
        }

        .main-container-level-list {
            margin-top: 33px;
            padding: 0 15px 0 0;
            box-sizing: border-box;

            .main-container-level-list-title {
                font-size: 18px;
                font-weight: 700;
                color: #fff;
                line-height: 22px;
            }

            .main-container-level-list-item {
                margin-top: 10px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                height: 60px;
                border-radius: 10px;
                padding: 0 20px;
                box-sizing: border-box;
                position: relative;

                &.item_0 {
                    background-color: rgba(118, 226, 164, .15);
                }

                &.item_1 {
                    background-color: rgba(114, 218, 255, .15);
                }

                &.item_2 {
                    background-color: rgba(84, 176, 255, .15)
                }

                &.item_3 {
                    background-color: rgba(125, 150, 254, .15)
                }

                &.item_4 {
                    background-color: rgba(191, 104, 229, .15)
                }

                &.item_5 {
                    background-color: rgba(255, 108, 252, .15)
                }

                &.item_6 {
                    background-color: rgba(252, 52, 65, .15)
                }

                &.item_7 {
                    background-color: rgba(255, 36, 29, .15);
                }

                &.item_8 {
                    background-color: rgba(37, 78, 252, .15);
                }

                &.item_9 {
                    background-color: rgba(37, 78, 252, .15);
                }

                .list-item-left {
                    display: flex;
                    align-items: center;
                    gap: 20px;

                    &-title {
                        font-size: 15px;
                        font-weight: 700;
                        color: #fff;
                        line-height: 18px;
                    }

                    &-name {
                        display: flex;
                        flex-direction: column;

                        .name {
                            font-size: 15px;
                            font-weight: 700;
                            color: #fff;
                            line-height: 18px;
                        }

                        .desc {
                            margin-top: 4px;
                            font-size: 12px;
                            font-weight: 600;
                            color: rgba($color: #fff, $alpha: .5);
                            line-height: 14px;
                        }
                    }
                }

                .luck-box {
                    position: absolute;
                    left: 0;
                    top: 0;
                    border-radius: 10px 0 10px 0;
                    background-color: rgba($color: #fff, $alpha: .1);
                    padding: 2px 7px;

                    img {
                        width: 12px;
                        height: 12px;
                    }
                }
            }


        }
    }

    .main-container-level-btn {
        position: absolute;
        bottom: calc(env(safe-area-inset-bottom) + 30px);
        width: 100%;
        padding: 0 23px;
        box-sizing: border-box;

        &-box {
            width: 100%;
            height: 60px;
            color: #fff;
            font-size: 18px;
            font-weight: 700;
            line-height: 60px;
            text-align: center;
            border-radius: 16px;

            &.btn_0 {
                background: linear-gradient(90deg, #234C33 0%, #82B2B3 100%);
            }

            &.btn_1 {
                background: linear-gradient(90deg, #80DEDC 0%, #1F96D0 100%);

            }

            &.btn_2 {
                background: linear-gradient(90deg, #70D0FF 0%, #1C94FC 100%);
            }

            &.btn_3 {
                background: linear-gradient(90deg, #9089FF 0%, #7B48DF 100%);
            }

            &.btn_4 {
                background: linear-gradient(90deg, #9F6FFF 0%, #8724DE 100%);

            }

            &.btn_5 {
                background: linear-gradient(96.34deg, #DD0C9D 0%, #CD57F8 100%);
            }

            &.btn_6 {
                background: linear-gradient(96.34deg, #E1014A 0%, #E74186 100%);

            }

            &.btn_7 {
                background: linear-gradient(96.34deg, #F44517 0%, #FC7E24 100%);
            }

            &.btn_8 {
                background: linear-gradient(90deg, #A93FEF 0%, #5F31E7 100%);

            }

            &.btn_9 {
                background: linear-gradient(90deg, #CE8A1A 0%, #B77408 50.48%, #FFE560 100%);
            }
        }

    }
}
</style>