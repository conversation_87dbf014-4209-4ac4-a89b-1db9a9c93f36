// 测试函数式snackbar
import { snackbar } from './index'

// 测试成功提示
export const testSuccessSnackbar = () => {
  snackbar.success('操作成功', '数据已保存到服务器')
}

// 测试通知提示
export const testNoticeSnackbar = () => {
  snackbar.notice('系统通知', '有新消息到达，请及时查看')
}

// 测试错误提示
export const testErrorSnackbar = () => {
  snackbar.error('操作失败', '网络连接异常，请检查网络设置')
}

// 测试自定义配置
export const testCustomSnackbar = () => {
  snackbar.show({
    title: '自定义标题',
    desc: '这是一个自定义配置的提示消息',
    type: 'success',
    duration: 5000
  })
}

// 测试手动关闭
export const testManualCloseSnackbar = () => {
  const instance = snackbar.success('手动关闭测试', '这个提示将在3秒后手动关闭')
  setTimeout(() => {
    instance.close()
  }, 3000)
} 