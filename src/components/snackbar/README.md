# Snackbar 组件

这是一个函数式的 Snackbar 组件，支持成功、通知、错误三种类型的提示消息。

## 功能特性

- ✅ 函数式调用，使用简单
- ✅ 支持三种类型：success、notice、error
- ✅ 自动关闭和手动关闭
- ✅ 同时只能显示一个snackbar
- ✅ 支持自定义标题、描述和持续时间
- ✅ TypeScript 支持

## 使用方法

### 基本用法

```typescript
import { snackbar } from '@/components/snackbar'

// 成功提示
snackbar.success('操作成功', '数据已保存')

// 通知提示
snackbar.notice('系统通知', '有新消息到达')

// 错误提示
snackbar.error('操作失败', '请检查网络连接')
```

### 高级用法

```typescript
// 自定义配置
snackbar.show({
  title: '自定义标题',
  desc: '自定义描述',
  type: 'success',
  duration: 3000
})

// 手动关闭
const instance = snackbar.success('操作成功')
// 3秒后手动关闭
setTimeout(() => {
  instance.close()
}, 3000)
```

## API

### snackbar.success(title, desc?, duration?)

显示成功提示

- `title` (string): 标题
- `desc` (string, 可选): 描述
- `duration` (number, 可选): 持续时间，默认2000ms

### snackbar.notice(title, desc?, duration?)

显示通知提示

- `title` (string): 标题
- `desc` (string, 可选): 描述
- `duration` (number, 可选): 持续时间，默认2000ms

### snackbar.error(title, desc?, duration?)

显示错误提示

- `title` (string): 标题
- `desc` (string, 可选): 描述
- `duration` (number, 可选): 持续时间，默认2000ms

### snackbar.show(options)

显示自定义配置的提示

- `options` (SnackbarOptions): 配置选项
  - `title` (string, 可选): 标题
  - `desc` (string, 可选): 描述
  - `type` ('success' | 'notice' | 'error', 可选): 类型，默认'success'
  - `duration` (number, 可选): 持续时间，默认2000ms

## 类型定义

```typescript
interface SnackbarOptions {
  title?: string
  desc?: string
  type?: 'success' | 'notice' | 'error'
  duration?: number
}

interface SnackbarInstance {
  close: () => void
}
```

## 注意事项

1. 同时只能显示一个snackbar，新的snackbar会替换当前显示的
2. 如果不设置duration或设置为0，snackbar不会自动关闭
3. 组件会自动挂载到document.body，无需手动管理DOM
4. 支持TypeScript，提供完整的类型提示 