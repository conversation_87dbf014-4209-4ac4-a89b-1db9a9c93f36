import { createApp, h } from 'vue'
import SnackbarComponent from './snackbar.vue'
import Varlet from '@varlet/ui'
import '@varlet/ui/es/style'

export interface SnackbarOptions {
  title?: string
  desc?: string
  type?: 'success' | 'notice' | 'error'
  duration?: number
}

export interface SnackbarInstance {
  close: () => void
}

let currentSnackbar: SnackbarInstance | null = null

const createSnackbar = (options: SnackbarOptions): SnackbarInstance => {
  // 如果已经有snackbar在显示，先关闭它
  if (currentSnackbar) {
    currentSnackbar.close()
  }

  // 创建容器
  const container = document.createElement('div')
  document.body.appendChild(container)

  // 创建Vue应用
  const app = createApp({
    render() {
      return h(SnackbarComponent, {
        show: true,
        type: options.type || 'success',
        duration: options.duration || 2000,
        title: options.title || '',
        desc: options.desc || '',
        onClose: () => {
          closeSnackbar()
        }
      })
    }
  })

  // 使用Varlet UI
  app.use(Varlet)

  // 挂载应用
  app.mount(container)

  const closeSnackbar = () => {
    if (currentSnackbar && container.parentNode) {
      try {
        app.unmount()
        document.body.removeChild(container)
      } catch (error) {
        console.warn('Snackbar close error:', error)
      } finally {
        currentSnackbar = null
      }
    }
  }

  // 创建实例
  const instance: SnackbarInstance = {
    close: closeSnackbar
  }

  currentSnackbar = instance

  // 自动关闭
  if (options.duration !== 0) {
    setTimeout(() => {
      closeSnackbar()
    }, options.duration || 2000)
  }

  return instance
}

// 导出便捷方法
export const snackbar = {
  success(title: string, desc?: string, duration?: number) {
    return createSnackbar({ title, desc, type: 'success', duration })
  },
  notice(title: string, desc?: string, duration?: number) {
    return createSnackbar({ title, desc, type: 'notice', duration })
  },
  error(title: string, desc?: string, duration?: number) {
    return createSnackbar({ title, desc, type: 'error', duration })
  },
  show(options: SnackbarOptions) {
    return createSnackbar(options)
  }
}

export default snackbar

/*
使用示例：

// 导入
import { snackbar } from '@/components/snackbar'

// 成功提示
snackbar.success('操作成功', '数据已保存')

// 通知提示
snackbar.notice('系统通知', '有新消息到达')

// 错误提示
snackbar.error('操作失败', '请检查网络连接')

// 自定义配置
snackbar.show({
  title: '自定义标题',
  desc: '自定义描述',
  type: 'success',
  duration: 3000
})

// 手动关闭
const instance = snackbar.success('操作成功')
// 3秒后手动关闭
setTimeout(() => {
  instance.close()
}, 3000)
*/
