<!--通用的弹窗-->
<template>
    <var-popup position="bottom" v-model:show="showRechargePopup" :safe-area="true" :default-style="false"
        @click-overlay="close">
        <div class="recharge-popup">
            <img src="@/assets/recharge/purchase-popup-bg.png" alt="" class="purchase-bg" @click="clickShowDiscount" v-if="showDiscount">
            <div class="recharge-popup-box"  :class="{'show-discount': showDiscount}">
                <!--头部-->
                
                <div class="recharge-popup-box-header">
                    <span class="recharge-popup-box-header-title">Payment Methods</span>
                    <div class="recharge-popup-box-header-right">
                        <div class="recharge-popup-box-header-right-coins">
                            <img src="@/assets/common/coins.png" alt="">
                            <span>100</span>
                        </div>
                        <div class="recharge-popup-box-header-right-method" @click="showRechargeCountryPopup = true">
                            <img src="@/assets/test/pay-method.png" alt=""
                                class="recharge-popup-box-header-right-method-img">
                            <img src="@/assets/live/private-arrow-down.png" alt=""
                                class="recharge-popup-box-header-right-method-arrow">
                        </div>
                    </div>
                </div>
                <!--付款方式-->
                <div class="recharge-popup-box-method">
                    <div class="recharge-popup-box-method-item" v-for="value in 5" :key="value"
                        @click="changePayMethod(value)">
                        <div class="recharge-popup-box-method-item-header">
                            <div class="recharge-popup-box-method-item-header-left">
                                <img src="@/assets/pay/apple.png" alt=""
                                    class="recharge-popup-box-method-item-header-left-img">
                                <span class="recharge-popup-box-method-item-header-left-title">Apple Store</span>
                            </div>
                            <div class="recharge-popup-box-method-item-header-right">
                                <img src="@/assets/pay/arrow-down.png" alt=""
                                    class="recharge-popup-box-method-item-header-arrow"
                                    :class="{ 'active-down': activePayMethod.includes(value) }">
                            </div>
                            <!--折扣-->
                            <div class="recharge-popup-box-method-item-header-discount" v-if="value == 1">
                                {{ '+' + 33 + '%' }}
                            </div>
                        </div>
                        <transition name="ease-in-out">
                            <div class="recharge-popup-box-method-item-content" v-if="activePayMethod.includes(value)">
                                <div class="recharge-popup-box-method-item-content-item" v-for="item in payMethodCoins"
                                    :key="value">
                                    <div class="recharge-content-item-header">
                                        <img src="@/assets/common/coins.png" alt=""
                                            class="recharge-content-item-header-img">
                                        <span class="recharge-content-item-header-title">{{ item.coins }}</span>
                                        <span class="recharge-content-item-header-add">+{{ item.add }}</span>
                                    </div>
                                    <div class="recharge-content-item-content">
                                        <span class="recharge-content-item-content-origin-price">${{ item.originPrice
                                            }}</span>
                                        <span class="recharge-content-item-content-price">${{ item.price }}</span>
                                    </div>
                                </div>
                            </div>
                        </transition>
                    </div>
                </div>
            </div>
        </div>
    </var-popup>
    <recharge-country-popup :show="showRechargeCountryPopup" @update:show="showRechargeCountryPopup = $event" />
</template>

<script lang="ts" setup name="RechargePopup">
import { ref, watch } from 'vue'
import RechargeCountryPopup from './rechargeCountryPopup.vue'
const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    showDiscount: {
        type: Boolean,
        default: true
    }
})
const emit = defineEmits(['update:show','showDiscount'])
const showRechargeCountryPopup = ref(false)
const showRechargePopup = ref(false)
const activePayMethod = ref<number[]>([])
const payMethodCoins = ref([
    {
        coins: 100,
        add: 14,
        originPrice: 4.99,
        price: 3.99,
        discount: 10,
    },
    {
        coins: 200,
        add: 28,
        originPrice: 9.99,
        price: 7.99,
        discount: 10,
    },
    {
        coins: 300,
        add: 42,
        originPrice: 14.99,
        price: 11.99,
        discount: 10,
    },
    {
        coins: 400,
        add: 56,
        originPrice: 19.99,
        price: 15.99,
        discount: 10,
    },
    {
        coins: 500,
        add: 56,
        originPrice: 19.99,
        price: 15.99,
        discount: 33,
    },
])

watch(() => showRechargePopup.value, (newVal: boolean) => {
    emit('update:show', newVal)
})


watch(() => props.show, (newVal: boolean) => {
    showRechargePopup.value = newVal
}, {
    immediate: true
})
/**
 * Close the popup.
 */
const close = () => {
    showRechargePopup.value = false
}
const changePayMethod = (value: number) => {
    if (activePayMethod.value.includes(value)) {
        activePayMethod.value = activePayMethod.value.filter(item => item !== value)
    } else {
        activePayMethod.value.push(value)
    }
}

const clickShowDiscount = () => {
    close()
    emit('showDiscount')
}
</script>
<style lang='scss' scoped>
.recharge-popup {
    width: 100svw;
    max-height: 80svh;
    display: flex;
    flex-direction: column;
    font-family: var(--font-family-urbanist);
    overflow: hidden;
    .purchase-bg{
        width: 100%;
        height: 60px;
    }
    &-box {
        display: flex;
        flex-direction: column;
        overflow: hidden;
        width: 100%;
        background-color: #0A0A1B;
        border-radius: 20px 20px 0 0;
        padding: 11px 15px 30px;
        &.show-discount{
            border-radius: 0;
        }
        &-header {
            display: flex;
            justify-content: space-between;
            align-items: center;

            &-title {
                font-size: 18px;
                font-weight: 700;
                color: #fff;
            }

            &-right {
                display: flex;
                align-items: center;
                gap: 10px;

                &-coins {
                    display: flex;
                    align-items: center;

                    img {
                        width: 16px;
                        height: 16px;
                    }

                    span {
                        font-size: 18px;
                        font-weight: 600;
                        color: #fff;
                    }
                }

                &-method {
                    display: flex;

                    align-items: center;
                    border: 1px solid #FFFFFF66;
                    padding: 5px 10px;
                    border-radius: 33px;
                    gap: 4px;

                    &-img {
                        width: 20px;
                        height: 20px;
                    }

                    &-arrow {
                        width: 24px;
                        height: 24px;
                    }
                }
            }
        }

        &-method {
            flex: 1;
            overflow: auto;

            &-item {
                margin-top: 10px;
                border-radius: 10px;
                background: linear-gradient(0deg, #17162C 0%, #292750 100%);

                &-header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 18px 20px;
                    box-sizing: border-box;
                    position: relative;

                    &-left {
                        display: flex;
                        align-items: center;
                        gap: 10px;

                        &-img {
                            width: 24px;
                            height: 24px;
                        }

                        &-title {
                            font-size: 16px;
                            font-weight: 700;
                            color: #fff;
                        }
                    }

                    &-right {
                        img {
                            width: 24px;
                            height: 24px;
                            transition: all .3s ease;

                            &.active-down {
                                transform: rotate(180deg);
                            }
                        }
                    }
                    &-discount{
                        position: absolute;
                        top: 0;
                        right: 0;
                        background:url('@/assets/pay/discount.png') no-repeat center center;
                        background-size: 100% 100%;
                        width: 34px;
                        height: 18px;
                        padding-right: 3px;
                        box-sizing: border-box;
                        font-size: 10px;
                        line-height: 14px;
                        font-weight: 700;
                        text-align: end;
                        color: #fff;
                    }
                }

                &-content {
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);
                    gap: 10px;
                    padding: 10px 20px;
                    box-sizing: border-box;

                    &-item {
                        display: flex;
                        flex-direction: column;
                        gap: 10px;
                        padding-top: 5px;
                        box-sizing: border-box;
                        background-color: #1E1D38;
                        border-radius: 10px;

                        .recharge-content-item-header {
                            display: flex;
                            flex-direction: column;
                            gap: 2px;
                            align-items: center;

                            &-img {
                                width: 30px;
                                height: 30px;
                            }

                            &-title {
                                font-size: 16px;
                                line-height: 19px;
                                font-weight: 700;
                                color: #fff;
                            }

                            &-add {
                                font-size: 12px;
                                line-height: 14px;
                                font-weight: 400;
                                color: #FF1A9E;
                            }
                        }

                        .recharge-content-item-content {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            gap: 2px;
                            padding: 4px 0;
                            width: 100%;
                            box-sizing: border-box;
                            background-color: rgba($color: #fff, $alpha: .1);
                            border-radius: 0 0 10px 10px;

                            &-origin-price {
                                font-size: 12px;
                                line-height: 14px;
                                font-weight: 400;
                                color: rgba($color: #fff, $alpha: .5);
                            }

                            &-price {
                                font-size: 14px;
                                line-height: 17px;
                                font-weight: 500;
                                color: #fff;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>