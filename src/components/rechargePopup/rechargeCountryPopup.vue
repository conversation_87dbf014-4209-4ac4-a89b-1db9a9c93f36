<!--  -->
<template>
    <div>
        <var-popup position="bottom" v-model:show="showRechargeCountryPopup" @click-overlay="close" :safe-area="true"
            :default-style="false" :overlay-style="{ opacity: 0 }">
            <div class="country-popup">
                <div class="country-popup-content">
                    <div class="country-popup-content-header">
                        Select Country/Region
                        <img src="@/assets/pay/close-popup.png" alt="" class="close-img" @click="close">
                    </div>
                    <!--国家选择List-->
                    <div class="country-popup-content-list">
                        <div class="country-popup-content-list-item" v-for="item in 20" :key="item" :class="{'active': item === 1}">
                            <div class="country-popup-content-list-item-left">
                                <img src="@/assets/test/pay-method.png" alt="">
                                <span>Türkiye</span>
                            </div>
                            <img src="@/assets/pay/select.png" alt="" class="country-popup-content-list-item-right" v-if="item === 1">
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="RechargeCountryPopup">
import { ref, watch } from 'vue'

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
})
const emit = defineEmits(['update:show'])

const showRechargeCountryPopup = ref(false)


watch(() => showRechargeCountryPopup.value, (newVal: boolean) => {
    emit('update:show', newVal)
})


watch(() => props.show, (newVal: boolean) => {
    showRechargeCountryPopup.value = newVal
}, {
    immediate: true
})

/**
 * Close the popup.
 */
const close = () => {
    showRechargeCountryPopup.value = false
}

</script>
<style lang="scss" scoped>
.country-popup {
    width: 100svw;
    max-height: 80svh;
    display: flex;
    font-family: var(--font-family-urbanist);
    overflow: hidden;

    .country-popup-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        background-color: #0A0A1B;
        padding-top: 20px;
        box-sizing: border-box;
        border-radius: 20px 20px 0 0;

        &-header {
            font-size: 18px;
            color: #fff;
            font-weight: 600;
            line-height: 22px;
            text-align: center;
            position: relative;

            .close-img {
                position: absolute;
                width: 24px;
                height: 24px;
                top: 0;
                right: 15px;
            }
        }

        &-list {
            flex: 1;
            overflow: auto;
            margin-top: 13px;
            &-item {
                width: 100%;
                height: 60px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 20px;
                box-sizing: border-box;
                &.active{
                    background-color: #1E1E41;
                }
                &-left {
                    display: flex;
                    align-items: center;
                    gap: 10px;

                    img {
                        width: 20px;
                        height: 20px;
                        border-radius: 50%;
                    }

                    span {
                        font-weight: 700;
                        font-size: 16px;
                        color: #fff;
                    }
                }

                &-right {
                    width: 24px;
                    height: 24px;
                }
            }
        }
    }
}
</style>