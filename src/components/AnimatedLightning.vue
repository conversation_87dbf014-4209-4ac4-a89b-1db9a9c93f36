<template>
    <div class="animated-lightning-container">
        <svg class="lightning-svg" viewBox="0 0 400 100" xmlns="http://www.w3.org/2000/svg">
            <!-- 主闪电路径 -->
            <path class="lightning-path" d="M 20 50 L 80 20 L 120 80 L 180 30 L 220 70 L 280 40 L 320 60 L 380 50"
                stroke="url(#lightningGradient)" stroke-width="3" fill="none" stroke-linecap="round"
                stroke-linejoin="round" />

            <!-- 闪电光晕效果 -->
            <path class="lightning-glow" d="M 20 50 L 80 20 L 120 80 L 180 30 L 220 70 L 280 40 L 320 60 L 380 50"
                stroke="url(#glowGradient)" stroke-width="8" fill="none" stroke-linecap="round" stroke-linejoin="round"
                opacity="0.3" />

            <!-- 渐变定义 -->
            <defs>
                <linearGradient id="lightningGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                    <stop offset="25%" style="stop-color:#ffff00;stop-opacity:1" />
                    <stop offset="50%" style="stop-color:#ffaa00;stop-opacity:1" />
                    <stop offset="75%" style="stop-color:#ffff00;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
                </linearGradient>

                <linearGradient id="glowGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#ffff00;stop-opacity:0.5" />
                    <stop offset="50%" style="stop-color:#ffaa00;stop-opacity:0.8" />
                    <stop offset="100%" style="stop-color:#ffff00;stop-opacity:0.5" />
                </linearGradient>

                <!-- 脉冲效果 -->
                <filter id="pulse">
                    <feGaussianBlur stdDeviation="2" result="coloredBlur" />
                    <feMerge>
                        <feMergeNode in="coloredBlur" />
                        <feMergeNode in="SourceGraphic" />
                    </feMerge>
                </filter>
            </defs>
        </svg>

        <!-- 额外的闪烁点 -->
        <div class="spark-points">
            <div class="spark spark-1"></div>
            <div class="spark spark-2"></div>
            <div class="spark spark-3"></div>
            <div class="spark spark-4"></div>
        </div>
    </div>
</template>

<script setup lang="ts">
// 组件逻辑
</script>

<style scoped>
.animated-lightning-container {
    position: relative;
    width: 100%;
    height: 100px;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.lightning-svg {
    width: 100%;
    height: 100%;
    filter: url(#pulse);
}

.lightning-path {
    animation: lightningFlash 2s ease-in-out infinite;
    filter: drop-shadow(0 0 10px rgba(255, 255, 0, 0.8));
}

.lightning-glow {
    animation: glowPulse 1.5s ease-in-out infinite;
}

/* 主闪电闪烁动画 */
@keyframes lightningFlash {

    0%,
    100% {
        opacity: 0.3;
        stroke-width: 2;
    }

    10%,
    90% {
        opacity: 1;
        stroke-width: 4;
    }

    50% {
        opacity: 0.8;
        stroke-width: 3;
    }
}

/* 光晕脉冲动画 */
@keyframes glowPulse {

    0%,
    100% {
        opacity: 0.1;
        stroke-width: 6;
    }

    50% {
        opacity: 0.4;
        stroke-width: 10;
    }
}

/* 闪烁点样式 */
.spark-points {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.spark {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #ffff00;
    border-radius: 50%;
    box-shadow: 0 0 10px #ffff00, 0 0 20px #ffff00;
    animation: sparkle 1.5s ease-in-out infinite;
}

.spark-1 {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.spark-2 {
    top: 60%;
    left: 40%;
    animation-delay: 0.3s;
}

.spark-3 {
    top: 30%;
    left: 70%;
    animation-delay: 0.6s;
}

.spark-4 {
    top: 70%;
    left: 85%;
    animation-delay: 0.9s;
}

@keyframes sparkle {

    0%,
    100% {
        opacity: 0;
        transform: scale(0.5);
    }

    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .animated-lightning-container {
        height: 60px;
    }

    .lightning-path {
        stroke-width: 2;
    }

    .lightning-glow {
        stroke-width: 4;
    }
}
</style>