<!--  -->
<template>
    <div>
        <var-popup position="bottom" v-model:show="showGiftPopup" @click-overlay="close" :safe-area="true"
            :default-style="false">
            <div class="gift-dialog" :class="{ 'gift-dialog-multi-beam': isMultiBeamLive }">
                <div class="gift-dialog-header">

                    <div class="gift-dialog-header-svip" v-if="activeTab === 1">

                    </div>
                    <div class="gift-dialog-header-common" v-else>
                        <div class="gift-dialog-header-common-left">

                        </div>
                        <div class="gift-dialog-header-common-center">
                            <div class="center-top">
                            </div>
                            <div class="center-bottom">
                                Gift &nbsp;<span>+2</span>&nbsp; EXP
                            </div>
                        </div>
                        <img src="@/assets/common/arrow-right.png" alt="" class="gift-dialog-header-common-right">
                    </div>
                </div>
                <!--多人直播间 显示的主播-->
                <div class="gift-dialog-content-multi-beam" v-if="isMultiBeamLive">
                    <div class="gift-dialog-content-multi-beam-anchor" @click="handleSelectAll(true)"
                        v-if="!isSelectAll">
                        <img src="@/assets/live/gift-all.png" alt="">
                        <span>All</span>
                    </div>
                    <!--中间的部分 flex:1-->
                    <var-menu v-model:show="showSendAnchorSelect" @close="showSendAnchorSelect = false" trigger="click"
                        :offset-y="35" :offset-x="0" :default-style="false" popover-class="git-num-menu">
                        <div class="gift-dialog-content-multi-beam-center" @click.stop="toggleAnchorSelect"
                            v-if="!isSelectAll">
                            <div class="gift-dialog-content-multi-beam-center-left">
                                <div class="gift-dialog-content-multi-beam-center-left-avatar">
                                    <img :src="selectedAnchor?.avatar" alt="">
                                    <div class="gift-dialog-content-multi-beam-center-left-avatar-admin"
                                        v-if="selectedAnchor?.isAdmin">
                                    </div>
                                </div>
                                <span class="gift-dialog-content-multi-beam-center-left-name">{{ selectedAnchor?.name
                                    }}</span>
                            </div>
                            <div class="gift-dialog-content-multi-beam-center-right">
                                <img src="@/assets/common/arrow-down.png" alt="" class="arrow-up"
                                    :class="{ 'arrow-up-active': showSendAnchorSelect }">
                            </div>
                        </div>

                        <div class="gift-dialog-content-multi-beam-center" @click.stop="showSendAnchorSelect = false"
                            v-else>
                            <div class="gift-dialog-content-multi-beam-center-left">
                                <img v-for="anchorItem in multiBeamLiveAnchorList" :src="anchorItem.avatar" alt=""
                                    class="all-select-avatar">
                            </div>
                            <div class="gift-dialog-content-multi-beam-center-right">
                                All hosts
                            </div>
                        </div>
                        <template #menu>
                            <div class="menu-list">
                                <div class="menu-item" @click="handleSelectAll(true)">
                                    <img src="@/assets/common/all-anchor.webp" alt="">
                                    <div class="menu-item-name">All on mic</div>
                                </div>
                                <div class="menu-item" v-for="value in multiBeamLiveAnchorList" :key="value.id"
                                    @click="handleSelectAnchor(value)">
                                    <img :src="value.avatar" alt="">
                                    <div class="menu-item-name">{{ value.name }}</div>
                                </div>
                            </div>

                        </template>
                    </var-menu>
                    <!--右边部分-->
                    <img src="@/assets/live/user-card.png" alt="" class="gift-dialog-content-multi-beam-user-card"
                        @click="handleSelectAdminCard">
                    <div class="gift-dialog-content-multi-beam-cancel" @click="handleSelectAll(false)"
                        v-if="isSelectAll">
                        Cancel
                    </div>
                </div>
                <!--下面是礼物部分-->
                <div class="gift-dialog-content" v-show="!showGiftBackpack">
                    <div class="gift-dialog-content-top">
                        <div class="gift-dialog-content-top-left">
                            <div class="gift-tab-item" v-for="(value, index) in tabsList" :key="value.value"
                                :class="{ 'gift-tab-item-active': activeTab === value.value }"
                                @click="handleTabChange(index)">
                                {{ value.label }}
                                <div class="new-tag" v-if="value.isNew">
                                    New
                                </div>
                            </div>
                        </div>
                        <div class="gift-dialog-content-top-right" @click="openGiftBackpack">
                            <img src="@/assets/gift/gift-backpack.png" alt="">
                        </div>
                    </div>
                    <div class="gift-dialog-content-center">
                        <var-swipe ref="mySwipe" class="swipe-example" :loop="false" :indicator="false"
                            @change="handleSwipeChange">
                            <var-swipe-item v-for="value in tabsList" :key="'swiper' + '_' + value.value"
                                :swipe-item-style="{ height: '100%' }">
                                <var-list :loading-text="''" :finished="value.finished" v-model:loading="value.loading"
                                    @load="loadGiftData" :immediate-check="false">
                                    <div class="gift-dialog-content-center-list">
                                        <div class="gift-dialog-content-center-item" v-for="item in value.dataList"
                                            :key="item.id" @click="handleSendGift(item)">
                                            <div class="gift-img-container">
                                                <!-- 占位符 -->
                                                <div class="gift-img-placeholder" v-if="!item.isLoaded">
                                                    <div class="placeholder-spinner"></div>
                                                </div>
                                                <!-- 真实图片 -->
                                                <img :src="item.iconUrl" :alt="item.name" class="gift-img"
                                                    :class="{ 'gift-img-loaded': item.isLoaded }"
                                                    @load="onImageLoad(item.giftId)" @error="onImageError(item.giftId)">
                                            </div>
                                            <div class="gift-dialog-content-center-item-name">
                                                {{ item.giftName }}
                                            </div>
                                            <div class="gift-dialog-content-center-item-price">
                                                <img src="@/assets/common/coins.png" alt="" class="coins-img">
                                                <span class="coins-num">{{ item.giftPrice }}</span>
                                            </div>
                                        </div>
                                    </div>

                                </var-list>
                            </var-swipe-item>
                        </var-swipe>
                    </div>
                </div>
                <div class="gift-dialog-content" v-show="showGiftBackpack">
                    <div class="gift-backpack-header">
                        <img src="@/assets/anchor/go-back.png" alt="" @click="openGiftBackpack">
                    </div>
                    <div class="no-gift-pack">
                        <img src="@/assets/common/no-gift-pack.webp" alt="">
                        <span>No gifts yet</span>
                    </div>
                </div>
                <!--个人金币部分-->
                <div class="gift-dialog-bottom" v-show="!showGiftBackpack">
                    <div class="bottom-left">
                        <img src="@/assets/common/coins.png" alt="" class="coins-img"
                            @click="changeCoinsNotEnoughPopup">
                        <span class="coins-num" @click="changeCoinsNotEnoughPopup">20</span>
                        <img src="@/assets/common/arrow-right.png" alt="" class="arrow-right">
                        <img src="@/assets/anchor/gift_icon.gif" alt="" class="gift-icon"
                            @click="changeShowDiscountPopup" v-if="!isMultiBeamLive">
                    </div>
                    <div class="bottom-right">
                        <div class="bottom-right-num">
                            <div class="bottom-right-num-item" v-for="value in sendGiftNumList" :key="value"
                                :class="{ 'bottom-right-num-item-active': sendGiftNum === value }"
                                @click="handleSendGiftNum(value)">
                                {{ value }}
                            </div>
                        </div>
                        <button class="send-button"> Send </button>
                        <!-- <var-menu v-model:show="showGitNumMenu" @close="showGitNumMenu = false" same-width
                            trigger="click" offset-y="25" :default-style="false" popover-class="git-num-menu">
                            <div class="bottom-right-num" @click.stop="showGitNumMenu = !showGitNumMenu">
                                {{ sendGiftNum }}
                            </div>
                            <template #menu>
                                <div class="menu-item" v-for="value in gitNumList" :key="value.value"
                                    @click="handleGitNum(value.value)">
                                    {{ value.label }}
                                </div>
                            </template>
</var-menu> -->
                        <!-- <img src="@/assets/common/arrow-up.png" alt="" class="arrow-up"
                            :class="{ 'arrow-up-active': showGitNumMenu }"> -->

                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="ChatGiftPopup">
import { ref, watch, onMounted, nextTick } from 'vue'
interface Props {
    show: boolean,
    isMultiBeamLive?: boolean,
    multiBeamLiveAnchorList?: any[],
    defaultAnchor?: any
}
const emit = defineEmits(['update:show', 'changeShowDiscountPopup', 'changeCoinsNotEnoughPopup', 'handleSelectAdminCard','loadingGiftAnimation'])


const isSelectAll = ref(false)
const showGiftPopup = ref(false)
const showGitNumMenu = ref(false)

const showSendAnchorSelect = ref(false)
const showGiftBackpack = ref(false)
const mySwipe = ref<any>(null)
const sendGiftNumList = ref([1, 10, 50, 99, 199, 'other'])

const selectedAnchor = ref<any>()
const tabsList = ref([{
    label: 'Popular',
    value: 0,
    pageSize: 20,
    pageNum: 1,
    finished: false,
    loading: false,
    dataList: [] as any[]
}, {
    label: 'SVIP',
    value: 1,
    isNew: true,
    pageSize: 20,
    pageNum: 1,
    finished: false,
    loading: false,
    dataList: [] as any[]
}, {
    label: 'Fun',
    value: 2,
    isNew: true,
    pageSize: 20,
    pageNum: 1,
    finished: false,
    loading: false,
    dataList: [] as any[]
}, {
    label: 'Customized',
    value: 3,
    pageSize: 20,
    pageNum: 1,
    finished: false,
    loading: false,
    dataList: [] as any[]

}, {
    label: 'Animals',
    value: 4,
    pageSize: 20,
    pageNum: 1,
    finished: false,
    loading: false,
    dataList: [] as any[]

},
{
    label: 'User lv',
    value: 5,
    pageSize: 20,
    pageNum: 1,
    finished: false,
    loading: false,
    dataList: [] as any[]
}])
const gitNumList = ref([{
    label: 'other',
    value: 0
}, {
    label: '500',
    value: 500
}, {
    label: '200',
    value: 200
},
{
    label: '100',
    value: 100
},
{
    label: '50',
    value: 50
},
{
    label: '30',
    value: 30
},
{
    label: '10',
    value: 10
},
{
    label: '1',
    value: 1
}
])
const activeTab = ref(0)
const props = withDefaults(defineProps<Props>(), {
    show: false,
    isMultiBeamLive: false
})
const sendGiftNum = ref(1)


// 图片加载状态处理
const onImageLoad = (itemId: number) => {
    const currentTab = tabsList.value[activeTab.value]
    const item = currentTab.dataList.find((item: any) => item.giftId === itemId)
    if (item) {
        item.isLoaded = true
        item.isError = false
    }
}

const onImageError = (itemId: number) => {
    const currentTab = tabsList.value[activeTab.value]
    const item = currentTab.dataList.find((item: any) => item.giftId === itemId)
    if (item) {
        item.isError = true
        item.isLoaded = false
    }
}

watch(() => showGiftPopup.value, (newVal: boolean) => {
    emit('update:show', newVal)
})
watch(() => props.defaultAnchor, (newVal: any) => {
    if (newVal) {
        selectedAnchor.value = newVal
    }
})
const handleGitNum = (value: number) => {
    sendGiftNum.value = value
    showGitNumMenu.value = false
}

const loadGiftData = () => {
    if (!tabsList.value[activeTab.value].dataList.length && !tabsList.value[activeTab.value].finished) {

        setTimeout(() => {
            const giftConfig = localStorage.getItem('voiceChatRoomGiftConfigs')
            if (giftConfig) {
                const giftConfigArr = JSON.parse(giftConfig).giftConfig
                giftConfigArr.forEach((item: any) => {
                    if (item.giftSubType === tabsList.value[activeTab.value].label) {
                        tabsList.value[activeTab.value].dataList.push(item)
                    }
                })
            }
            tabsList.value[activeTab.value].finished = true
            tabsList.value[activeTab.value].loading = false
            console.log(tabsList.value[activeTab.value].dataList)
            // console.log(activeTab.value)
            // tabsList.value[activeTab.value].dataList = tabsList.value[activeTab.value].dataList.concat(newData)
            // tabsList.value[activeTab.value].finished = true
            // tabsList.value[activeTab.value].loading = false

            // console.log(tabsList.value[activeTab.value].dataList)
        }, 1000)
    }
}

const handleSwipeChange = (index: number) => {
    activeTab.value = index
    loadGiftData()
}

const handleTabChange = (index: number) => {
    activeTab.value = index
    mySwipe.value?.to(index)
}

const openGiftBackpack = () => {
    showGiftBackpack.value = !showGiftBackpack.value
}
//////多人直播相关
/**
 * 
 * @param value 
 */
const handleSelectAll = (value: boolean) => {
    isSelectAll.value = value
    showSendAnchorSelect.value = false
}

const handleSelectAdminCard = () => {
    emit('handleSelectAdminCard')
}

const toggleAnchorSelect = () => {
    showSendAnchorSelect.value = !showSendAnchorSelect.value
}

const handleSelectAnchor = (anchorItem: any) => {
    selectedAnchor.value = anchorItem
    // 模拟选择主播
    console.log('Selected anchor with ID:', anchorItem)
    showSendAnchorSelect.value = false
}

const handleSendGift = (item: any) => {
    if (item.svgUrl) {
        emit('loadingGiftAnimation', item.svgUrl)
    }
}

watch(() => props.show, (newVal: boolean) => {
    showGiftPopup.value = newVal
    if (newVal) {
        // tabsList.value[activeTab.value].finished = false
        // tabsList.value[activeTab.value].loading = false
        // tabsList.value[activeTab.value].dataList = []
        // loadGiftData()
    }
}, {
    immediate: true
})

/**
 * Close the popup.
 */
const close = () => {
    showGiftPopup.value = false
}

const changeShowDiscountPopup = () => {
    emit('changeShowDiscountPopup')
}

const changeCoinsNotEnoughPopup = () => {
    emit('changeCoinsNotEnoughPopup')
}

const handleSendGiftNum = (value: number | string) => {
    if (typeof value === 'number') {
        sendGiftNum.value = value
    } else {
        console.log('1111')
    }
}


onMounted(() => {

    const giftConfig = localStorage.getItem('voiceChatRoomGiftConfigs')
    if (giftConfig) {
        const giftConfigArr = JSON.parse(giftConfig).giftConfig

        giftConfigArr.forEach((item: any) => {
            if (item.giftSubType === 'Popular') {
                tabsList.value[0].dataList.push(item)
            }
        })
    }
})
</script>
<style lang="scss" scoped>
.gift-dialog {
    width: 100%;
    height: 394px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    font-family: var(--font-family-urbanist);

    &.gift-dialog-multi-beam {
        height: 446px;
    }

    &-header {
        width: 100%;

        &-common {
            width: 100%;
            height: 50px;
            background-color: #1E1D38;
            border-radius: 16px 16px 0 0;
            padding: 0 15px;
            display: flex;
            align-items: center;

            &-left {
                width: 50px;
                height: 20px;
                background: url('@/assets/common/vip-level.png') no-repeat center center;
                background-size: 100% 100%;
                margin-right: 5px;
            }

            &-center {
                flex: 1;
                margin-right: 10px;

                .center-top {
                    width: 100%;
                    height: 6px;
                    background: rgba($color: #fff, $alpha: .2);
                    border-radius: 3px;
                }

                .center-bottom {
                    margin-top: 7px;
                    font-size: 12px;
                    font-weight: 600;
                    color: rgba($color: #fff, $alpha: .5);
                    line-height: 14px;

                    span {
                        color: #FFBD10;
                    }
                }
            }

            &-right {
                width: 24px;
                height: 24px;
            }
        }

        &-svip {
            width: 100%;
            height: 60px;
            background: url('@/assets/vip/svip.png') no-repeat center center;
            background-size: 100% 100%;
        }
    }

    .gift-dialog-content-multi-beam {
        width: 100%;
        height: 52px;
        background: #1E1D38;
        padding: 0 15px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        gap: 8px;

        .gift-dialog-content-multi-beam-anchor {
            height: 32px;
            display: flex;
            align-items: center;
            gap: 2px;
            border: 1px solid #FFFFFF;
            border-radius: 40px;
            padding: 7px 12px;

            img {
                width: 16px;
                height: 16px;
            }

            span {
                font-size: 15px;
                font-weight: 700;
                color: #fff;
                line-height: 18px;
            }


        }

        .var-menu {
            flex: 1;
            height: 32px;
            overflow: hidden;
        }

        .gift-dialog-content-multi-beam-center {
            width: 100%;
            height: 100%;
            background-color: #2A294A;
            border-radius: 16px;
            display: flex;
            align-items: center;
            padding: 0px 10px 0px 1px;
            justify-content: space-between;

            &-left {
                flex: 1;
                display: flex;
                align-items: center;
                overflow: hidden;

                .all-select-avatar {
                    width: 28px;
                    height: 28px;
                    border-radius: 50%;
                    object-fit: cover;
                    object-position: top;

                    &:not(:first-child) {
                        margin-left: -8px;
                    }
                }

                &-avatar {
                    width: 28px;
                    height: 28px;
                    position: relative;

                    img {
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                        object-fit: cover;
                        object-position: top;
                    }

                    &-admin {
                        width: 26px;
                        height: 11px;
                        position: absolute;
                        bottom: -2px;
                        left: 0;
                        background: url('@/assets/common/admin.png') no-repeat center center;
                        background-size: 100% 100%;
                    }
                }

                &-name {
                    flex: 1;
                    font-size: 14px;
                    color: #fff;
                    font-weight: 700;
                    line-height: 17px;
                    margin-left: 5px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }

            &-right {
                font-size: 14px;
                color: #fff;
                font-weight: 700;
                line-height: 17px;

                .arrow-up {
                    width: 20px;
                    height: 20px;
                    transition: all .3s ease-in-out;

                    &.arrow-up-active {
                        transform: rotate(180deg);
                    }
                }
            }
        }

        .gift-dialog-content-multi-beam-user-card {
            width: 40px;
            height: 32px;
        }

        .gift-dialog-content-multi-beam-center {
            flex: 1;
            height: 32px;
            border: 1px solid rgba($color: #fff, $alpha: 0.3);
            border-radius: 40px;
        }

        .gift-dialog-content-multi-beam-cancel {
            font-size: 15px;
            height: 32px;
            padding: 0 13px;
            font-size: 14px;
            font-weight: 700;
            color: #fff;
            line-height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba($color: #fff, $alpha: .1);
            border-radius: 40px;
        }
    }

    &-content {
        padding: 0 15px 0;
        box-sizing: border-box;
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        background-color: #1E1D38;

        .gift-backpack-header {
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;

            img {
                width: 30px;
                height: 30px;
            }
        }

        .no-gift-pack {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;

            img {
                width: 200px;
                height: 200px;
            }

            span {
                margin-top: 10px;
                font-size: 20px;
                font-weight: 600;
                color: #fff;
            }
        }

        &-top {
            width: 100%;

            display: flex;
            align-items: center;
            justify-content: space-between;

            &-left {

                flex: 1;
                display: flex;
                align-items: center;
                overflow-x: scroll;
                font-size: 15px;
                font-weight: 700;
                color: rgba($color: #fff, $alpha: .5);
                gap: 20px;

                .gift-tab-item {
                    flex-shrink: 0;
                    position: relative;
                    height: 40px;
                    line-height: 40px;

                    &.gift-tab-item-active {
                        color: #fff;
                    }

                    .new-tag {
                        position: absolute;
                        padding: 0 4px;
                        background-color: $common-color;
                        border-radius: 4px 4px 4px 0;
                        font-size: 10px;
                        font-weight: 700;
                        line-height: 12px;
                        color: #fff;
                        top: 2px;
                        left: 12px;
                        z-index: 100;
                    }
                }
            }

            &-right {
                width: 24px;
                height: 24px;

                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }

        &-center {
            width: 100%;
            flex: 1;
            overflow: auto;

            .var-swipe {
                width: 100%;
                height: 100%;

                .var-swipe-item {
                    overflow-y: auto;

                    .var-list {
                        width: 100%;
                        height: 100%;

                        .gift-dialog-content-center-list {
                            width: 100%;
                            height: 100%;
                            display: grid;
                            grid-template-columns: repeat(4, 1fr);
                            gap: 10px;

                            .gift-dialog-content-center-item {
                                width: 100%;
                                height: 100px;
                                display: flex;
                                flex-direction: column;
                                align-items: center;

                                .gift-img-container {
                                    position: relative;
                                    width: 60px;
                                    height: 60px;
                                    border-radius: 8px;
                                    overflow: hidden;
                                    background-color: #2A294A;
                                }

                                .gift-img {
                                    width: 100%;
                                    height: 100%;
                                    object-fit: cover;
                                    opacity: 0;
                                    transition: opacity 0.3s ease-in-out;

                                    &.gift-img-loaded {
                                        opacity: 1;
                                    }
                                }

                                .gift-img-placeholder {
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                    width: 100%;
                                    height: 100%;
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                    background-color: #2A294A;
                                    border-radius: 8px;
                                    z-index: 1;
                                }

                                .placeholder-spinner {
                                    width: 20px;
                                    height: 20px;
                                    border: 2px solid rgba($color: #fff, $alpha: .3);
                                    border-top: 2px solid #fff;
                                    border-radius: 50%;
                                    animation: spin 1s linear infinite;
                                }

                                @keyframes spin {
                                    0% {
                                        transform: rotate(0deg);
                                    }

                                    100% {
                                        transform: rotate(360deg);
                                    }
                                }

                                .gift-dialog-content-center-item-name {
                                    margin-top: 4px;
                                    font-size: 12px;
                                    font-weight: 600;
                                    line-height: 14px;
                                    color: #fff;
                                }

                                .gift-dialog-content-center-item-price {
                                    margin-top: 5px;
                                    display: flex;
                                    align-items: center;
                                    gap: 2px;

                                    .coins-img {
                                        width: 12px;
                                        height: 12px;
                                    }

                                    .coins-num {
                                        font-size: 10px;
                                        font-weight: 700;
                                        line-height: 12px;
                                        color: rgba($color: #fff, $alpha: .7);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    &-bottom {
        padding: 5px 15px 29px;
        background-color: #1E1D38;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;

        .bottom-left {
            display: flex;
            align-items: center;

            .coins-img {
                width: 20px;
                height: 20px;
                margin-right: 3px;
            }

            .coins-num {
                font-size: 16px;
                color: #fff;
                font-weight: 700px;
                margin-right: 2px;
            }

            .arrow-right {
                width: 20px;
                height: 20px;
                margin-right: 2px;
            }

            .gift-icon {
                width: 78px;
                height: 32px;
            }
        }

        .bottom-right {
            height: 30px;
            border: 1px solid rgba($color: #fff, $alpha: .2);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-left: 1px;
            box-sizing: border-box;

            .bottom-right-num {
                max-width: 150px;
                overflow-x: auto;
                height: 100%;
                display: flex;
                align-items: center;
                color: #fff;
                gap: 5px;

                &-item {
                    flex-shrink: 0;
                    line-height: 24px;
                    width: 27px;
                    height: 24px;
                    text-align: center;
                    border-radius: 30px;

                    &.bottom-right-num-item-active {
                        background: #FFFFFF;
                        color: #6435FF;
                    }
                }
            }

            .send-button {
                margin-left: 5px;
                height: 100%;
                padding: 0 16px;
                box-sizing: border-box;
                border-radius: 39px;
                border: none;
                background: linear-gradient(90deg, #692AFF 0%, #28A8FF 100%);
                font-size: 12px;
                color: #fff;
            }
        }
    }
}
</style>
<style lang="scss">
.git-num-menu {
    background-color: transparent;

    .menu-list {
        display: flex;
        flex-direction: column;
        background-color: rgba(51, 50, 49, .86667);
        border-radius: 16px;
        width: max-content;
    }

    .menu-item {
        width: 147px;
        height: 36px;
        font-size: 10px;
        line-height: 24px;
        color: #E2D5D1;
        font-family: var(--font-family-type2);
        text-align: center;
        display: flex;
        align-items: center;
        padding-left: 10px;
        box-sizing: border-box;

        img {
            width: 22px;
            height: 22px;
            border-radius: 50%;
            object-fit: cover;
        }

        .menu-item-name {
            width: 105px;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            padding-left: 5px;
            font-family: var(--font-family-type3);
            color: #fff;
            text-align: left;
        }
    }
}
</style>