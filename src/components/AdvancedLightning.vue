<template>
    <div class="advanced-lightning-container" :style="containerStyle">
        <svg class="lightning-svg" :viewBox="viewBox" xmlns="http://www.w3.org/2000/svg">
            <!-- 背景光晕 -->
            <defs>
                <radialGradient id="backgroundGlow" cx="50%" cy="50%" r="50%">
                    <stop offset="0%" style="stop-color:#ffff00;stop-opacity:0.1" />
                    <stop offset="100%" style="stop-color:#000000;stop-opacity:0" />
                </radialGradient>

                <linearGradient id="lightningGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                    <stop offset="20%" style="stop-color:#ffff00;stop-opacity:1" />
                    <stop offset="40%" style="stop-color:#ffaa00;stop-opacity:1" />
                    <stop offset="60%" style="stop-color:#ff6600;stop-opacity:1" />
                    <stop offset="80%" style="stop-color:#ffff00;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
                </linearGradient>

                <filter id="lightningFilter">
                    <feGaussianBlur stdDeviation="1" result="coloredBlur" />
                    <feMerge>
                        <feMergeNode in="coloredBlur" />
                        <feMergeNode in="SourceGraphic" />
                    </feMerge>
                </filter>

                <filter id="glowFilter">
                    <feGaussianBlur stdDeviation="3" result="glow" />
                    <feMerge>
                        <feMergeNode in="glow" />
                        <feMergeNode in="SourceGraphic" />
                    </feMerge>
                </filter>
            </defs>

            <!-- 背景光晕 -->
            <circle cx="200" cy="50" r="80" fill="url(#backgroundGlow)" class="background-glow" />

            <!-- 主闪电路径 -->
            <path class="lightning-main" :d="lightningPath" stroke="url(#lightningGradient)" stroke-width="4"
                fill="none" stroke-linecap="round" stroke-linejoin="round" filter="url(#lightningFilter)" />

            <!-- 闪电光晕 -->
            <path class="lightning-glow" :d="lightningPath" stroke="url(#lightningGradient)" stroke-width="12"
                fill="none" stroke-linecap="round" stroke-linejoin="round" opacity="0.3" filter="url(#glowFilter)" />

            <!-- 动态分支闪电 -->
            <path v-for="(branch, index) in branchPaths" :key="`branch-${index}`"
                :class="`lightning-branch branch-${index}`" :d="branch" stroke="url(#lightningGradient)"
                stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round" opacity="0.7" />
        </svg>

        <!-- 粒子效果 -->
        <div class="particles-container">
            <div v-for="particle in particles" :key="particle.id" class="particle" :style="particle.style"></div>
        </div>

        <!-- 控制按钮 -->
        <div class="controls" v-if="showControls">
            <button @click="toggleAnimation" class="control-btn">
                {{ isAnimating ? '暂停' : '播放' }}
            </button>
            <button @click="changeSpeed" class="control-btn">
                速度: {{ speedLevel }}
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface Props {
    width?: string
    height?: string
    speed?: number
    showControls?: boolean
    autoPlay?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    width: '100%',
    height: '120px',
    speed: 1,
    showControls: false,
    autoPlay: true
})

// 响应式数据
const isAnimating = ref(props.autoPlay)
const speedLevel = ref(1)
const particleId = ref(0)

// 计算属性
const containerStyle = computed(() => ({
    width: props.width,
    height: props.height
}))

const viewBox = computed(() => '0 0 400 100')

const lightningPath = computed(() => {
    const basePath = 'M 20 50 L 80 20 L 120 80 L 180 30 L 220 70 L 280 40 L 320 60 L 380 50'
    return basePath
})

const branchPaths = computed(() => [
    'M 80 20 L 60 10 L 40 15',
    'M 120 80 L 140 90 L 160 85',
    'M 220 70 L 240 60 L 260 65',
    'M 280 40 L 300 30 L 320 35'
])

// 粒子系统
const particles = ref<Array<{
    id: number
    style: {
        left: string
        top: string
        animationDelay: string
        animationDuration: string
    }
}>>([])

// 方法
const toggleAnimation = () => {
    isAnimating.value = !isAnimating.value
}

const changeSpeed = () => {
    speedLevel.value = (speedLevel.value % 3) + 1
}

const createParticle = () => {
    if (!isAnimating.value) return

    const particle = {
        id: particleId.value++,
        style: {
            left: Math.random() * 100 + '%',
            top: Math.random() * 100 + '%',
            animationDelay: Math.random() * 2 + 's',
            animationDuration: (1 + Math.random()) + 's'
        }
    }

    particles.value.push(particle)

    // 限制粒子数量
    if (particles.value.length > 20) {
        particles.value.shift()
    }
}

// 生命周期
let particleInterval: number | null = null

onMounted(() => {
    if (props.autoPlay) {
        particleInterval = setInterval(createParticle, 200)
    }
})

onUnmounted(() => {
    if (particleInterval) {
        clearInterval(particleInterval)
    }
})
</script>

<style scoped>
.advanced-lightning-container {
    position: relative;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 8px;
}

.lightning-svg {
    width: 100%;
    height: 100%;
}

.background-glow {
    animation: backgroundPulse 3s ease-in-out infinite;
}

.lightning-main {
    animation: lightningFlash 2s ease-in-out infinite;
    filter: drop-shadow(0 0 15px rgba(255, 255, 0, 0.8));
}

.lightning-glow {
    animation: glowPulse 1.5s ease-in-out infinite;
}

.lightning-branch {
    animation: branchFlash 1.8s ease-in-out infinite;
}

.branch-0 {
    animation-delay: 0.2s;
}

.branch-1 {
    animation-delay: 0.4s;
}

.branch-2 {
    animation-delay: 0.6s;
}

.branch-3 {
    animation-delay: 0.8s;
}

/* 动画定义 */
@keyframes lightningFlash {

    0%,
    100% {
        opacity: 0.4;
        stroke-width: 3;
    }

    25% {
        opacity: 1;
        stroke-width: 5;
    }

    50% {
        opacity: 0.8;
        stroke-width: 4;
    }

    75% {
        opacity: 1;
        stroke-width: 6;
    }
}

@keyframes glowPulse {

    0%,
    100% {
        opacity: 0.2;
        stroke-width: 10;
    }

    50% {
        opacity: 0.5;
        stroke-width: 15;
    }
}

@keyframes branchFlash {

    0%,
    100% {
        opacity: 0.3;
        stroke-width: 1;
    }

    50% {
        opacity: 0.8;
        stroke-width: 3;
    }
}

@keyframes backgroundPulse {

    0%,
    100% {
        opacity: 0.1;
        transform: scale(1);
    }

    50% {
        opacity: 0.3;
        transform: scale(1.1);
    }
}

/* 粒子样式 */
.particles-container {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: #ffff00;
    border-radius: 50%;
    box-shadow: 0 0 8px #ffff00, 0 0 16px #ffff00;
    animation: particleFloat 2s ease-out forwards;
}

@keyframes particleFloat {
    0% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }

    100% {
        opacity: 0;
        transform: scale(0.5) translateY(-20px);
    }
}

/* 控制按钮 */
.controls {
    position: absolute;
    bottom: 10px;
    right: 10px;
    display: flex;
    gap: 8px;
}

.control-btn {
    background: rgba(255, 255, 0, 0.2);
    border: 1px solid rgba(255, 255, 0, 0.5);
    color: #ffff00;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(255, 255, 0, 0.3);
    border-color: rgba(255, 255, 0, 0.8);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .advanced-lightning-container {
        height: 80px;
    }

    .lightning-main {
        stroke-width: 2;
    }

    .lightning-glow {
        stroke-width: 8;
    }

    .lightning-branch {
        stroke-width: 1;
    }
}
</style>