<!--  -->
<template>
    <div>
        <var-popup position="bottom" v-model:show="showPrivateChatPopup" @click-overlay="close" :safe-area="true"
            :default-style="false" :overlay-style="{
               
            }">
            <div class="private-chat-popup">
                <div class="private-chat-popup-content">
                    <div class="private-chat-popup-content-header">
                        <LazyImage :src="img1" class="private-chat-popup-content-header-anchor" />
                        <LazyImage :src="img2" class="private-chat-popup-content-header-user" />
                        <img src="@/assets/anchor/heart.png" alt="" class="private-chat-popup-content-header-heart">
                    </div>
                    <div class="private-chat-popup-content-title">
                        Private chat
                    </div>
                    <div class="private-chat-popup-content-desc">
                        5 minutes for free, afterwards 20 coins/min
                    </div>
                    <div class="private-chat-popup-content-btn">
                        <img src="@/assets/live/private-gift.png" alt="" class="private-chat-popup-content-btn-gift">
                        <span class="private-chat-popup-content-btn-text">Start the private chat</span>
                        <div class="private-chat-popup-content-btn-price">
                            <img src="@/assets/common/coins.png" alt="">
                            <span>200</span>
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="PrivateChatPopup">
import { ref, watch } from 'vue'
import LazyImage from '../LazyImage.vue';
import img1 from '@/assets/home/<USER>'
import img2 from '@/assets/home/<USER>'
const props = defineProps<Props>()
interface Props {
    show: boolean,
    anchorInfo: any
}
const emit = defineEmits(['update:show'])

const showPrivateChatPopup = ref(false)


watch(() => showPrivateChatPopup.value, (newVal: boolean) => {
    emit('update:show', newVal)
})


watch(() => props.show, (newVal: boolean) => {
    showPrivateChatPopup.value = newVal
}, {
    immediate: true
})
/**
 * Close the popup.
 */
const close = () => {
    showPrivateChatPopup.value = false
}

</script>
<style lang="scss" scoped>
.private-chat-popup {
    width: 100%;
    overflow: hidden;
    font-family: var(--font-family-urbanist);
    background: linear-gradient(152deg, #FECFFF 2.25%, rgba(255, 255, 255, 1) 70.01%);
    border-radius: 16px 16px 0 0;

    .private-chat-popup-content {
        width: 100%;
        height: 331px;
        padding: 20px 27px 0px;
        background: url('@/assets/anchor/private-chat-bg.png') no-repeat center center;
        background-size: 100% 100%;
        box-sizing: border-box;
        border-radius: 16px 16px 0 0;
        display: flex;
        flex-direction: column;
        &-header{
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            &-anchor,&-user{
                width: 100px;
                height: 100px;
                border-radius: 50%;
                border: 1px solid #fff;
                position: relative;
            }
            &-anchor{
                right: -5px;
            }
            &-user{
                left: -5px;
            }
            &-heart{
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                margin: auto;
                width: 48px;
                height: 48px;
            }
        }
        &-title{
            font-size: 18px;
            line-height: 22px;
            font-weight: 700;
            color: #000;
            text-align: center;
            margin-top: 40px;
        }
        &-desc{
            margin-top:5px;
            font-size: 14px;
            line-height: 17px;
            font-weight: 500;
            color: rgba($color: #000000, $alpha: .5);
            text-align: center;
        }
        &-btn{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 24px;
            padding: 10px;
            background: linear-gradient(90deg, #C756F6 0%, #F768C5 100%);
            border-radius: 40px;
            &-price{
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 3px;
                font-size: 14px;
                font-weight: 700;
                color: #fff;
                img{
                    width: 16px;
                    height: 16px;
                }
            }
            &-gift{
                width: 40px;
                height: 40px;
            }
            &-text{
                font-size: 16px;
                font-weight: 700;
                color: #fff;
            }
        }
    }
}
</style>