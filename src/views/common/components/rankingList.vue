<!--  -->
<template>
    <div class="popular-content-list">
        <div class="popular-content-list-item" v-for="item in 100" :key="item">
            <div class="popular-content-list-item-num">
                {{ item + 3 > 10 ? item + 3 : '0' + (item + 3) }}
            </div>
            <div class="popular-content-list-item-center">
                <img src="@/assets/home/<USER>" alt="" class="popular-content-list-item-avatar" />
                <div class="center-right">
                    <div class="center-right-name">
                        Visitor-323232
                    </div>
                    <img src="@/assets/rankings/ranking-tag.png" alt="" class="ranking-tag">
                    <div class="center-right-about">
                        IM NEW HERE ❤️ I CAN SURPRISIM NEW HERE ❤️ I CAN SURPRISIM NEW
                        HERE ❤️ I CAN SURPRIS
                    </div>
                </div>
            </div>
            <div class="popular-content-list-item-right">
                <img src="@/assets/rankings/follow.png" alt="" class="follow-img" />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup name="RankingList">
import { ref } from 'vue'
const props = defineProps({
    list: {
        type: Array,
        default: () => []
    }
})
</script>
<style lang="scss" scoped>
.popular-content-list {
    margin-top: -5px;
    padding: 20px 15px;
    box-sizing: border-box;
    flex: 1;
    background: url('@/assets/rankings/commmon-other-ranking.png') no-repeat;
    background-size: 100% auto;
    background-position: top;

    .popular-content-list-item {
        margin-top: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .popular-content-list-item-num {
            font-size: 15px;
            font-weight: 700;
            line-height: 18px;
            color: #FDF6AE;
            margin-right: 16px;
        }

        .popular-content-list-item-center {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 10px;
            overflow: hidden;

            .popular-content-list-item-avatar {
                flex-shrink: 0;
                width: 44px;
                height: 44px;
                border-radius: 50%;
                object-fit: cover;
            }

            .center-right {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 2px;
                overflow: hidden;

                .center-right-name {
                    font-size: 15px;
                    font-weight: 700;
                    line-height: 18px;
                    color: #fff;
                }

                .ranking-tag {
                    height: 14px;
                    object-fit: contain;
                    object-position: left;
                }

                .center-right-about {
                    font-size: 12px;
                    font-weight: 600;
                    line-height: 14px;
                    color: rgba($color: #fff, $alpha: .7);
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    width: 100%;
                }
            }
        }

        .popular-content-list-item-right {
            margin-left: 40px;

            .follow-img {
                width: 24px;
                height: 20px;
            }
        }
    }
}
</style>