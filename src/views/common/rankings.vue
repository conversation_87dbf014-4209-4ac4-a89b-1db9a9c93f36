<!-- app-container -->
<template>
    <div class="app-container ranking-page">
        <div class="ranking-page-header" :style="{ background: `rgba(12,12,16,${opacity})` }">
            <div class="ranking-page-header-left" @click="goBack">
                <img src="@/assets/common/go-back.png" alt="">
            </div>
            <div class="ranking-page-header-right">
                <div class="ranking-page-header-right-tab-item" v-for="item in tabList" :key="item.index"
                    :class="{ active: activeTab === item.index }" @click="changeTab(item.index)">
                    {{ item.name }}
                </div>
            </div>
        </div>
        <!--整个部分的day 和 weekly切换-->
        <div class="ranking-page-switch">
            <div class="ranking-page-switch-box">
                <div class="ranking-page-switch-box-item" :class="{ active: activeSwitch === 0 }"
                    @click="changeSwitch(0)">
                    Daily
                </div>
                <div class="ranking-page-switch-box-item" :class="{ active: activeSwitch === 1 }"
                    @click="changeSwitch(1)">
                    Weekly
                </div>
                <div class="transition-box" :style="{ transform: `translateX(${activeSwitch * 100}%)` }">

                </div>
            </div>
        </div>
        <!--下面部分-->
        <div class="ranking-page-content">
            <var-swipe ref="rankingSwipe" class="swipe-example" :indicator="false" @change="changeSwipe">
                <!--popular-->
                <var-swipe-item>
                    <PullRefresh v-model="refreshLoading[0]" @refresh="onRefresh(0)" :threshold="60"
                        :success-duration="1500">
                        <div class="popular-content common-content">
                            <div class="top-three">
                                <div class="top-three-box">
                                    <!--第二名-->
                                    <div class="top-three-box-item top-three-box-item-2">
                                        <div class="top-three-box-item-top">
                                            <img src="@/assets/rankings/common-2.png" alt="" class="common-2">
                                            <img src="@/assets/home/<USER>" alt="" class="ranking-avatar">
                                        </div>
                                        <!---是否关注-->
                                        <img src="@/assets/rankings/follow.png" alt="" class="follow-img">
                                        <div class="top-three-box-item-name">
                                            DUNYA🌍
                                        </div>
                                        <!--下面是标签-->
                                        <img src="@/assets/rankings/ranking-tag.png" alt="" class="ranking-tag">
                                    </div>
                                    <!--第一名-->
                                    <div class="top-three-box-item top-three-box-item-1">
                                        <div class="top-three-box-item-top">
                                            <img src="@/assets/rankings/common-1.png" alt="" class="common-2">
                                            <img src="@/assets/home/<USER>" alt="" class="ranking-avatar">
                                        </div>
                                        <!---是否关注-->
                                        <img src="@/assets/rankings/follow.png" alt="" class="follow-img">
                                        <div class="top-three-box-item-name">
                                            DUNYA🌍
                                        </div>
                                        <!--下面是标签-->
                                        <img src="@/assets/rankings/ranking-tag.png" alt="" class="ranking-tag">
                                    </div>
                                    <!--第三名-->
                                    <div class="top-three-box-item top-three-box-item-2">
                                        <div class="top-three-box-item-top">
                                            <img src="@/assets/rankings/common-3.png" alt="" class="common-2">
                                            <img src="@/assets/home/<USER>" alt="" class="ranking-avatar">
                                        </div>
                                        <!---是否关注-->
                                        <img src="@/assets/rankings/follow.png" alt="" class="follow-img">
                                        <div class="top-three-box-item-name">
                                            DUNYA🌍
                                        </div>
                                        <!--下面是标签-->
                                        <img src="@/assets/rankings/ranking-tag.png" alt="" class="ranking-tag">
                                    </div>
                                </div>
                            </div>
                            <RankingList :list="[]" />
                        </div>
                    </PullRefresh>
                </var-swipe-item>
                <!--deluxe-->
                <var-swipe-item>
                    <PullRefresh v-model="refreshLoading[1]" @refresh="onRefresh(1)" :threshold="60"
                        :success-duration="1500">
                        <div class="popular-content common-content">
                            <div class="top-three">
                                <div class="top-three-box">
                                    <!--第二名-->
                                    <div class="top-three-box-item top-three-box-item-2">
                                        <div class="top-three-box-item-top">
                                            <img src="@/assets/rankings/common-2.png" alt="" class="common-2">
                                            <img src="@/assets/home/<USER>" alt="" class="ranking-avatar">
                                        </div>
                                        <!---是否关注-->
                                        <img src="@/assets/rankings/follow.png" alt="" class="follow-img">
                                        <div class="top-three-box-item-name">
                                            DUNYA🌍
                                        </div>
                                        <!--下面是标签-->
                                        <img src="@/assets/rankings/ranking-tag.png" alt="" class="ranking-tag">
                                    </div>
                                    <!--第一名-->
                                    <div class="top-three-box-item top-three-box-item-1">
                                        <div class="top-three-box-item-top">
                                            <img src="@/assets/rankings/common-1.png" alt="" class="common-2">
                                            <img src="@/assets/home/<USER>" alt="" class="ranking-avatar">
                                        </div>
                                        <!---是否关注-->
                                        <img src="@/assets/rankings/follow.png" alt="" class="follow-img">
                                        <div class="top-three-box-item-name">
                                            DUNYA🌍
                                        </div>
                                        <!--下面是标签-->
                                        <img src="@/assets/rankings/ranking-tag.png" alt="" class="ranking-tag">
                                    </div>
                                    <!--第三名-->
                                    <div class="top-three-box-item top-three-box-item-2">
                                        <div class="top-three-box-item-top">
                                            <img src="@/assets/rankings/common-3.png" alt="" class="common-2">
                                            <img src="@/assets/home/<USER>" alt="" class="ranking-avatar">
                                        </div>
                                        <!---是否关注-->
                                        <img src="@/assets/rankings/follow.png" alt="" class="follow-img">
                                        <div class="top-three-box-item-name">
                                            DUNYA🌍
                                        </div>
                                        <!--下面是标签-->
                                        <img src="@/assets/rankings/ranking-tag.png" alt="" class="ranking-tag">
                                    </div>
                                </div>
                            </div>
                            <RankingList :list="[]" />
                        </div>
                    </PullRefresh>
                </var-swipe-item>
                <!--intimacy-->
                <var-swipe-item>
                    <PullRefresh v-model="refreshLoading[2]" @refresh="onRefresh(2)" :threshold="60"
                        :success-duration="1500">
                        <div class="intimacy-content">
                            <div class="intimacy-content-header">
                                <div class="intimacy-content-header-left">
                                    <div class="intimacy-avatar">
                                        <img src="@/assets/rankings/intimacy-avatar-bg.png" alt=""
                                            class="intimacy-avatar-bg">
                                        <img src="@/assets/home/<USER>" alt="" class="intimacy-avatar-img">
                                    </div>
                                    <div class="intimacy-content-header-name">
                                        HB🍰Berry💗
                                    </div>
                                </div>
                                <span class="intimacy-content-header-center">2737122 </span>
                                <div class="intimacy-content-header-left">
                                    <div class="intimacy-avatar">
                                        <img src="@/assets/rankings/intimacy-avatar-bg.png" alt=""
                                            class="intimacy-avatar-bg">
                                        <img src="@/assets/home/<USER>" alt="" class="intimacy-avatar-img">
                                    </div>
                                    <div class="intimacy-content-header-name">
                                        Mystery Man
                                    </div>
                                </div>
                            </div>
                            <div class="intimacy-content-list">
                                <div class="intimacy-content-list-item" v-for="item in 100">
                                    <div class="intimacy-content-list-item-num">
                                        {{ item + 3 > 10 ? item + 3 : '0' + (item + 3) }}
                                    </div>
                                    <div class="intimacy-content-list-item-center">
                                        <img src="@/assets/home/<USER>" alt=""
                                            class="intimacy-content-list-girl-avatar" />
                                        <img src="@/assets/home/<USER>" alt=""
                                            class="intimacy-content-list-gray-avatar" />
                                    </div>
                                    <div class="intimacy-content-list-item-right">
                                        <div class="intimacy-content-list-item-right-name">
                                            DUNYA🌍&Here4Dunya
                                        </div>
                                        <div class="intimacy-content-list-item-right-tag">
                                            <img src="@/assets/rankings/ranking-ecg.png" alt="" class="ranking-tag">
                                            <span>{{ Math.floor(Math.random() * 1000000) }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </PullRefresh>
                </var-swipe-item>
            </var-swipe>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import PullRefresh from '@/components/PullRefresh.vue'
import RankingList from './components/rankingList.vue'
const router = useRouter()

const refreshLoading = ref([false, false, false])

const tabList = ref([
    {
        name: 'Popular',
        index: 0
    },
    {
        name: 'Deluxe',
        index: 1
    },
    {
        name: 'Intimacy',
        index: 2
    }
])
const goBack = () => {
    router.back()
}
const rankingSwipe = ref<any>(null)
const activeTab = ref(0)
const activeSwitch = ref(0)

const opacity = ref(0)
const changeTab = (index: number) => {
    activeTab.value = index
    rankingSwipe.value.to(index)
}
const changeSwitch = (index: number) => {
    activeSwitch.value = index
}
const changeSwipe = (index: number) => {
    console.log(index)
    activeTab.value = index
}
const onRefresh = (index: number) => {
    refreshLoading.value[index] = true
    setTimeout(() => {
        refreshLoading.value[index] = false
    }, 1500)
}
const scrollEvent = (event?: Event) => {
    const scrollTop = (event?.target as HTMLElement)?.scrollTop
    opacity.value = scrollTop / 200 > 1 ? 1 : scrollTop / 200



}
const findAndAttachScrollListener = () => {
    const element = document.querySelector('.pull-refresh-content-wrapper') as HTMLElement
    if (element) {
        // 检查元素是否有滚动能力
        if (element.scrollHeight > element.clientHeight ||
            getComputedStyle(element).overflowY === 'auto' ||
            getComputedStyle(element).overflowY === 'scroll') {

            element.addEventListener('scroll', scrollEvent)
            return true
        }
    }

    return false
}

onMounted(() => {
    // 延迟查找，确保组件完全渲染
    nextTick(() => {
        setTimeout(() => {
            if (!findAndAttachScrollListener()) {
                console.log('No scrollable element found, using window scroll')
                window.addEventListener('scroll', scrollEvent)
            }
        }, 100)
    })
})

onUnmounted(() => {
    // 清理滚动事件监听器
    window.removeEventListener('scroll', scrollEvent)
})
</script>
<style lang="scss" scoped>
.ranking-page {
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .ranking-page-header {
        width: 100%;
        height: 44px;
        padding: 0 15px;
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        align-items: center;
        z-index: 10;
        position: fixed;
        top: env(safe-area-inset-top);
        left: 0;

        &-left {
            img {
                width: 24px;
                height: 24px;
            }
        }

        &-right {
            margin-left: 25px;
            flex: 1;
            display: flex;
            align-items: center;
            gap: 20px;
            font-size: 18px;
            font-weight: 700;
            line-height: 22px;

            &-tab-item {
                color: rgba($color: #fff, $alpha: .5);
                position: relative;
                cursor: pointer;
                transition: color 0.3s ease;

                &.active {
                    color: #fff;

                    &::before {
                        content: '';
                        position: absolute;
                        left: 0;
                        right: 0;
                        margin: 0 auto;
                        bottom: -4px;
                        width: 10px;
                        height: 3px;
                        border-radius: 2px;
                        background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                    }
                }
            }
        }
    }

    .ranking-page-switch {
        position: fixed;
        top: calc(env(safe-area-inset-top) + 56px);
        width: 100%;
        display: flex;
        justify-content: center;
        z-index: 10;

        .ranking-page-switch-box {
            width: 150px;
            height: 30px;
            background-color: rgba(30, 29, 56, .7);
            border-radius: 30px;
            padding: 2px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            position: relative;

            &-item {
                flex: 1;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                font-weight: 700;
                color: rgba(255, 255, 255, 0.5);
                position: relative;
                z-index: 2;

                &.active {
                    color: #fff;
                }
            }

            .transition-box {
                position: absolute;
                left: 0;
                top: 0;
                width: 50%;
                height: 100%;
                background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                border-radius: 30px;
                transition: transform 0.3s ease;
                z-index: 1;
            }
        }
    }

    .ranking-page-content {
        flex: 1;
        background-color: #0f0f11;
        overflow: auto;

        .var-swipe {
            width: 100%;
            height: 100%;

            .common-content {
                width: 100%;
                height: 100%;
                padding-top: calc(env(safe-area-inset-top) + 128px);
                box-sizing: border-box;
                background: url('@/assets/rankings/bg.png') no-repeat;
                background-size: 100% auto;
                display: flex;
                flex-direction: column;

                .top-three {
                    padding: 0 17px;
                    box-sizing: border-box;

                    &-box {
                        width: 100%;
                        height: 200px;
                        background: url('@/assets/rankings/top-three-bg.png') no-repeat;
                        background-size: 100% auto;
                        background-position: bottom;
                        display: flex;
                        justify-content: space-between;
                        gap: 18px;
                        padding: 0 13px;
                        box-sizing: border-box;

                        &-item {
                            display: flex;
                            flex-direction: column;
                            align-items: center;

                            .top-three-box-item-top {
                                position: relative;
                                display: flex;
                                justify-content: center;
                                align-items: center;

                                .common-2 {
                                    position: absolute;
                                    left: 0;
                                    top: 0;
                                    width: 100%;
                                    height: 100%;
                                }

                                .ranking-avatar {
                                    object-fit: cover;
                                    border-radius: 50%;
                                }


                            }

                            &.top-three-box-item-2 {
                                margin-top: 31px;

                                .top-three-box-item-top {
                                    width: 90px;
                                    height: 90px;

                                    .common-2 {
                                        position: absolute;
                                        left: 0;
                                        top: 0;
                                        width: 100%;
                                        height: 100%;
                                    }

                                    .ranking-avatar {
                                        width: 70px;
                                        height: 70px;
                                    }
                                }

                            }

                            &.top-three-box-item-1 {

                                .top-three-box-item-top {
                                    width: 100px;
                                    height: 100px;

                                    .ranking-avatar {
                                        width: 80px;
                                        height: 80px;
                                    }
                                }
                            }



                            .follow-img {
                                margin-top: 2px;
                                width: 24px;
                                height: 20px;
                            }

                            .top-three-box-item-name {
                                margin-top: 4px;
                                font-size: 15px;
                                font-weight: 700;
                                line-height: 18px;
                                color: #fff;
                            }

                            .ranking-tag {
                                margin-top: 4px;
                                width: 49px;
                                height: 14px;
                            }
                        }
                    }

                }
            }

            .intimacy-content {
                width: 100%;
                height: 100%;
                padding-top: calc(env(safe-area-inset-top) + 128px);
                box-sizing: border-box;
                background: url('@/assets/rankings/intimacy-bg.png') no-repeat;
                background-size: 100% auto;
                display: flex;
                flex-direction: column;

                .intimacy-content-header {
                    margin-top: 43px;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    &-left {
                        .intimacy-avatar {
                            width: 110px;
                            height: 110px;
                            position: relative;
                            display: flex;
                            justify-content: center;
                            align-items: center;

                            .intimacy-avatar-bg {
                                position: absolute;
                                width: 100%;
                                height: 100%;

                            }

                            .intimacy-avatar-img {
                                width: 80px;
                                height: 80px;
                                object-fit: cover;
                                object-position: top;
                                border-radius: 50%;
                            }
                        }

                        .intimacy-content-header-name {
                            margin-top: 23px;
                            font-size: 15px;
                            font-weight: 700;
                            line-height: 18px;
                            color: #fff;
                        }
                    }

                    .intimacy-content-header-center {
                        font-size: 14px;
                        font-weight: 500;
                        line-height: 17px;
                        color: #FFF316;
                    }

                }

                .intimacy-content-list {
                    padding: 20px 15px;
                    box-sizing: border-box;
                    flex: 1;
                    background: url('@/assets/rankings/commmon-other-ranking.png') no-repeat;
                    background-size: 100% auto;
                    background-position: top;

                    .intimacy-content-list-item {
                        margin-top: 20px;
                        display: flex;
                        align-items: center;
                        gap: 10px;

                        .intimacy-content-list-item-num {
                            font-size: 15px;
                            font-weight: 700;
                            line-height: 18px;
                            color: #FDF6AE;
                            margin-right: 9px;
                        }

                        .intimacy-content-list-item-center {
                            display: flex;
                            align-items: center;
                           
                            img {
                                width: 44px;
                                height: 44px;
                                object-fit: cover;
                                object-position: top;
                                border-radius: 50%;
                            }

                            .intimacy-content-list-gray-avatar {
                                margin-left: -10px;
                            }
                        }
                        .intimacy-content-list-item-right{
                            .intimacy-content-list-item-right-name{
                                font-size: 15px;
                                font-weight: 700;
                                line-height: 18px;
                                color: #fff;
                            }
                            .intimacy-content-list-item-right-tag{
                                margin-top: 2px;
                                display: flex;
                                align-items: center;
                                gap: 3px;
                                img {
                                    width: 14px;
                                    height: 14px;
                                }
                                span{
                                    font-size: 12px;
                                    font-weight: 600;
                                    line-height: 14px;
                                    color: $common-color
                                }
                            }
                        }
                    }
                }
            }
        }
    }

}
</style>