<template>
  <div class="test-lazy-image">
    <h1>LazyImage 宽高测试</h1>
    
    <div class="test-section">
      <h2>不同尺寸测试</h2>
      <div class="size-tests">
        <div class="test-item">
          <h3>60px x 60px</h3>
          <LazyImage 
            src="https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1741571199goddess.gif"
            :placeholder-src="positonImg"
            alt="Test"
            width="60px"
            height="60px"
          />
        </div>
        
        <div class="test-item">
          <h3>100px x 100px</h3>
          <LazyImage 
            src="https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1720168900serpentfinal.gif"
            :placeholder-src="positonImg"
            alt="Test"
            width="100px"
            height="100px"
          />
        </div>
        
        <div class="test-item">
          <h3>150px x 150px</h3>
          <LazyImage 
            src="https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1741571199goddess.gif"
            :placeholder-src="positonImg"
            alt="Test"
            width="150px"
            height="150px"
          />
        </div>
        
        <div class="test-item">
          <h3>200px x 100px</h3>
          <LazyImage 
            src="https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1720168900serpentfinal.gif"
            :placeholder-src="positonImg"
            alt="Test"
            width="200px"
            height="100px"
          />
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <h2>在gift-list-item中的效果</h2>
      <div class="gift-list">
        <div class="gift-list-item" v-for="item in 5" :key="item">
          <LazyImage 
            src="https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1741571199goddess.gif"
            :placeholder-src="positonImg"
            alt="Gift"
            width="60px"
            height="60px"
          />
          <div class="gift-name">Racing Car</div>
          <span class="gift-num">X1</span>
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <h2>调试信息</h2>
      <DebugLazyImage 
        src="https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1741571199goddess.gif"
        :placeholder-src="positonImg"
        alt="Debug"
        width="60px"
        height="60px"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import LazyImage from '@/components/LazyImage.vue'
import DebugLazyImage from '@/components/DebugLazyImage.vue'
import positonImg from '@/assets/common/position.png'
</script>

<style scoped>
.test-lazy-image {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #1a1a1a;
  min-height: 100vh;
  color: #fff;
}

.test-lazy-image h1 {
  color: #ffff00;
  text-align: center;
  margin-bottom: 30px;
}

.test-section {
  margin-bottom: 50px;
  padding: 20px;
  background: #2a2a2a;
  border-radius: 12px;
  border: 1px solid #333;
}

.test-section h2 {
  color: #ffff00;
  margin-bottom: 20px;
}

.size-tests {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.test-item {
  background: #333;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #444;
  text-align: center;
}

.test-item h3 {
  color: #ffff00;
  margin-bottom: 15px;
}

/* 模拟gift-list-item的样式 */
.gift-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

.gift-list-item {
  width: 100%;
  height: 100%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  background: linear-gradient(180deg, rgba(27, 11, 57, 0.1) 0%, #3E2669 100%);
  border-radius: 10px;
  padding: 7px 10px;
  box-sizing: border-box;
}

.gift-name {
  width: 100%;
  margin-top: 2px;
  font-size: 12px;
  font-weight: 600;
  line-height: 14px;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.gift-num {
  margin-top: 2px;
  font-size: 13px;
  font-style: italic;
  font-weight: 800;
  color: #fff;
  line-height: 16px;
}

@media (max-width: 768px) {
  .test-lazy-image {
    padding: 10px;
  }
  
  .size-tests {
    grid-template-columns: 1fr;
  }
  
  .gift-list {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style> 