<!-- 钱包 -->
<template>
    <div class="app-container recharge-container">
        <div class="recharge-header">
            <div class="recharge-header-left">
                <span>Recharge</span>
            </div>
            <div class="recharge-header-right">
                <span class="recharge-header-right-text">My balance</span>
                <img src="@/assets/common/coins.png" alt="" class="recharge-header-right-img">
                <span class="recharge-header-right-num">{{ 0 }}</span>
            </div>
        </div>
        <!--svip-->
        <div class="recharge-svip">
            <div class="recharge-svip-top">
                <span class="recharge-svip-top-text">Svip Package</span>
                <span class="recharge-svip-top-more">
                    More
                    <img src="@/assets/common/more.png" alt="" class="recharge-svip-top-more-img">
                </span>
            </div>
            <!--svip-coins-->
            <div class="recharge-svip-coins">
                <div class="recharge-svip-coins-left">
                    <div class="recharge-svip-coins-left-top">
                        170 coins & Free msgs
                    </div>
                    <div class="recharge-svip-coins-left-bottom">
                        Claim free coins
                    </div>
                </div>
                <div class="recharge-svip-coins-right">
                    <div class="recharge-svip-coins-right-button">
                        $19.99/Mo
                    </div>
                </div>
            </div>

        </div>
        <!---占位图片-->
        <img src="@/assets/recharge/purchase-bg.png" alt="" class="recharge-placeholder">
        <!--下面的充值List-->
        <div class="recharge-popup-box-header">
            <span class="recharge-popup-box-header-title">Payment Methods</span>
            <div class="recharge-popup-box-header-right">
                <div class="recharge-popup-box-header-right-coins">
                    <img src="@/assets/common/coins.png" alt="">
                    <span>100</span>
                </div>
                <div class="recharge-popup-box-header-right-method" @click="showRechargeCountryPopup = true">
                    <img src="@/assets/test/pay-method.png" alt="" class="recharge-popup-box-header-right-method-img">
                    <img src="@/assets/live/private-arrow-down.png" alt=""
                        class="recharge-popup-box-header-right-method-arrow">
                </div>
            </div>
        </div>
        <!--付款方式-->
        <div class="recharge-popup-box-method">
            <div class="recharge-popup-box-method-item" v-for="value in 5" :key="value" @click="changePayMethod(value)">
                <div class="recharge-popup-box-method-item-header">
                    <div class="recharge-popup-box-method-item-header-left">
                        <img src="@/assets/pay/apple.png" alt="" class="recharge-popup-box-method-item-header-left-img">
                        <span class="recharge-popup-box-method-item-header-left-title">Apple Store</span>
                    </div>
                    <div class="recharge-popup-box-method-item-header-right">
                        <img src="@/assets/pay/arrow-down.png" alt=""
                            class="recharge-popup-box-method-item-header-arrow"
                            :class="{ 'active-down': activePayMethod.includes(value) }">
                    </div>
                    <!--折扣-->
                    <div class="recharge-popup-box-method-item-header-discount" v-if="value == 1">
                        {{ '+' + 33 + '%' }}
                    </div>
                </div>
                <transition name="ease-in-out">
                    <div class="recharge-popup-box-method-item-content" v-if="activePayMethod.includes(value)">
                        <div class="recharge-popup-box-method-item-content-item" v-for="item in payMethodCoins"
                            :key="value">
                            <div class="recharge-content-item-header">
                                <img src="@/assets/common/coins.png" alt="" class="recharge-content-item-header-img">
                                <span class="recharge-content-item-header-title">{{ item.coins }}</span>
                                <span class="recharge-content-item-header-add">+{{ item.add }}</span>
                            </div>
                            <div class="recharge-content-item-content">
                                <span class="recharge-content-item-content-origin-price">${{ item.originPrice
                                }}</span>
                                <span class="recharge-content-item-content-price">${{ item.price }}</span>
                            </div>
                        </div>
                    </div>
                </transition>
            </div>
        </div>
        <!--connect-us-->
        <div class="connect-us">
            <img src="@/assets/common/connect-us.png" alt="" class="connect-us-img">
            <span class="connect-us-text">If you encounter payment issues, please </span>
            <span class="connect-us-text-link">contact us.</span>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
const showRechargeCountryPopup = ref(false)
const activePayMethod = ref<number[]>([])
const payMethodCoins = ref([
    {
        coins: 100,
        add: 14,
        originPrice: 4.99,
        price: 3.99,
        discount: 10,
    },
])
const changePayMethod = (value: number) => {
    if (activePayMethod.value.includes(value)) {
        activePayMethod.value = activePayMethod.value.filter(item => item !== value)
    } else {
        activePayMethod.value.push(value)
    }
}
</script>
<style lang="scss" scoped>
.recharge-container {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    padding-top: calc(env(--safe-area-top) + 44px);
    padding-bottom: calc(env(--safe-area-bottom));
    padding: 0 16px;
    box-sizing: border-box;
    font-family: var(--font-family-urbanist);

    .recharge-header {
        width: 100%;
        padding: 11px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .recharge-header-left {
            font-size: 18px;
            font-weight: 600;
            line-height: 21px;
            color: #fff;
        }

        .recharge-header-right {
            display: flex;
            align-items: center;

            &-text {
                font-size: 14px;
                font-weight: 500;
                color: rgba($color: #fff, $alpha: .5);
                margin-right: 6px;

            }

            &-img {
                width: 16px;
                height: 16px;
                margin-right: 4px;
            }

            &-num {
                font-size: 18px;
                font-weight: 600;
                color: #fff;
            }
        }
    }

    .recharge-svip {
        margin-top: 3px;
        background: #282625;
        border-radius: 10px;

        .recharge-svip-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 9px 15px 0 31px;

            &-text {
                font-size: 16px;
                font-weight: 900;
                color: #fff;
                line-height: 19px;
                font-style: italic;
            }

            &-more {
                display: flex;
                align-items: center;
                font-size: 12px;
                font-weight: 600;
                color: rgba($color: #fff, $alpha: .5);
                line-height: 14px;

                &-img {
                    width: 18px;
                    height: 18px;
                    margin-left: 2px;
                }
            }
        }

        .recharge-svip-coins {
            margin-top: 8px;
            padding: 12px 15px 9px 12px;
            background: linear-gradient(90deg, #FEEECE 0%, #FFFFED 24.04%, #FBF1D6 43.75%, #FFFFE3 62.02%, #F8D9A9 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &-left {
                &-top {
                    font-size: 18px;
                    font-weight: 700;
                    color: #000;
                    line-height: 22px;
                }

                &-bottom {
                    margin-top: 3px;
                    font-size: 12px;
                    font-weight: 600;
                    color: #000;
                    line-height: 14px;
                    font-style: italic;
                }
            }

            &-right {
                &-button {
                    padding: 7px 10px;
                    background-color: #1A170E;
                    border-radius: 16px;
                    color: #F9DE9B;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 17px;
                }
            }
        }
    }

    .recharge-placeholder {
        margin-top: 20px;
        width: 100%;
        border-radius: 10px;
    }

    .recharge-popup-box-header {
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &-title {
            font-size: 18px;
            font-weight: 700;
            color: #fff;
        }

        &-right {
            display: flex;
            align-items: center;
            gap: 10px;

            &-coins {
                display: flex;
                align-items: center;

                img {
                    width: 16px;
                    height: 16px;
                }

                span {
                    font-size: 18px;
                    font-weight: 600;
                    color: #fff;
                }
            }

            &-method {
                display: flex;

                align-items: center;
                border: 1px solid #FFFFFF66;
                padding: 5px 10px;
                border-radius: 33px;
                gap: 4px;

                &-img {
                    width: 20px;
                    height: 20px;
                }

                &-arrow {
                    width: 24px;
                    height: 24px;
                }
            }
        }
    }

    .recharge-popup-box-method {
        flex: 1;
        overflow: auto;

        &-item {
            margin-top: 10px;
            border-radius: 10px;
            background: linear-gradient(0deg, #17162C 0%, #292750 100%);

            &-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 18px 20px;
                box-sizing: border-box;
                position: relative;

                &-left {
                    display: flex;
                    align-items: center;
                    gap: 10px;

                    &-img {
                        width: 24px;
                        height: 24px;
                    }

                    &-title {
                        font-size: 16px;
                        font-weight: 700;
                        color: #fff;
                    }
                }

                &-right {
                    img {
                        width: 24px;
                        height: 24px;
                        transition: all .3s ease;

                        &.active-down {
                            transform: rotate(180deg);
                        }
                    }
                }

                &-discount {
                    position: absolute;
                    top: 0;
                    right: 0;
                    background: url('@/assets/pay/discount.png') no-repeat center center;
                    background-size: 100% 100%;
                    width: 34px;
                    height: 18px;
                    padding-right: 3px;
                    box-sizing: border-box;
                    font-size: 10px;
                    line-height: 14px;
                    font-weight: 700;
                    text-align: end;
                    color: #fff;
                }
            }

            &-content {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 10px;
                padding: 10px 20px;
                box-sizing: border-box;

                &-item {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                    padding-top: 5px;
                    box-sizing: border-box;
                    background-color: #1E1D38;
                    border-radius: 10px;

                    .recharge-content-item-header {
                        display: flex;
                        flex-direction: column;
                        gap: 2px;
                        align-items: center;

                        &-img {
                            width: 30px;
                            height: 30px;
                        }

                        &-title {
                            font-size: 16px;
                            line-height: 19px;
                            font-weight: 700;
                            color: #fff;
                        }

                        &-add {
                            font-size: 12px;
                            line-height: 14px;
                            font-weight: 400;
                            color: #FF1A9E;
                        }
                    }

                    .recharge-content-item-content {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 2px;
                        padding: 4px 0;
                        width: 100%;
                        box-sizing: border-box;
                        background-color: rgba($color: #fff, $alpha: .1);
                        border-radius: 0 0 10px 10px;

                        &-origin-price {
                            font-size: 12px;
                            line-height: 14px;
                            font-weight: 400;
                            color: rgba($color: #fff, $alpha: .5);
                        }

                        &-price {
                            font-size: 14px;
                            line-height: 17px;
                            font-weight: 500;
                            color: #fff;
                        }
                    }
                }
            }
        }
    }

    .connect-us {
        position: fixed;
        bottom: calc(48px + env(safe-area-inset-bottom));
        left: 0;
        right: 0;
        display: flex;
        align-items: center;
        background-color: #1B1B34;
        padding: 3px 0px 3px 15px;
        font-size: 12px;
        font-weight: 600;
        color: rgba($color: #fff, $alpha: .5);
        line-height: 14px;

        &-img {
            width: 24px;
            height: 24px;
            margin-right: 5px;
        }

        &-text-link {
            margin-left: 4px;
            color: #fff;
        }
    }
}
</style>