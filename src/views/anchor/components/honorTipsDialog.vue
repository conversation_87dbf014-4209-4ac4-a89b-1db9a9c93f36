<!-- 选择性别弹窗 -->
<template>
    <div>
        <var-popup position="center" v-model:show="showHonorTipsDialog" :close-on-click-overlay="false" :safe-area="true"
            :default-style="false">
            <div class="honor-tip">
                <div class="honor-tip-box">
                    <img src="@/assets/common/close-tip.png" alt="" class="close-img" @click="close">
                    <div class="honor-tip-box-title">
                        Honor Medals
                    </div>
                    <div class="why-chose">
                        <img src="@/assets/common/why-chose.png" alt="">
                        <span>What are honor Medals?</span>
                    </div>
                    <div class="why-chose-desc">
                        Medals are symbols of distinguished honors that reflect the achievements you’ve earned in
                        leaderboards, events, and more.
                        Titles will also be displayed on your profile page.
                    </div>
                    <img src="@/assets/anchor/example-1.png" alt="" class="example-img">
                    <img src="@/assets/anchor/example-2.png" alt="" class="example-img"></img>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">
export default {
    name: 'honorTipsDialog',
    props: {
        show: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            showHonorTipsDialog: this.show
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.showHonorTipsDialog = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        close() {
            this.showHonorTipsDialog = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
    }
}
</script>
<style lang='scss' scoped>
.honor-tip {
    font-family: var(--font-family-urbanist);

    .honor-tip-box {
        width: 340px;
        height: 370px;
        overflow: auto;
        background-color: #fff;
        border-radius: 10px;
        position: relative;
        padding: 30px 20px 0;
        box-sizing: border-box;

        .close-img {
            position: absolute;
            right: 15px;
            top: 15px;
            width: 12px;
            height: 12px;
        }

        .honor-tip-box-title {
            font-size: 18px;
            font-weight: 700;
            color: #000;
            text-align: center;
            line-height: 22px;
        }

        .why-chose {
            margin-top: 13px;
            display: flex;
            align-items: center;
            gap: 4px;

            img {
                width: 16px;
                height: 16px;
            }

            span {

                font-weight: 500;
                font-size: 14px;
                color: #7B3AF7;

            }
        }
        .why-chose-desc {
            margin-top: 5px;
            font-weight: 600;
            font-size: 13px;
            line-height: 16px;
            color: #000;
        }
        .example-img{
            width: 100%;
            margin-top: 10px;
        }

    }
}
</style>