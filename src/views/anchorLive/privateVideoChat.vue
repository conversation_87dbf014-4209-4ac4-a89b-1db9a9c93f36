<!-- 私密视频聊天 -->
<template>
    <div class="app-container private-video-chat">
        <div class="private-video-chat-box private-video-chat-box-pending" v-if="liveRoomStatus === 'Pending'">

        </div>
        <!--不管什么时候都在-->
        <div class="private-video-chat-header">
            <div class="private-video-chat-header-left">
                <img src="@/assets/home/<USER>" alt="主播头像" class="left-img">
                <div class="live-name-num">
                    <span class="live-name">{{ '<PERSON> Cooper' }}</span>
                    <span class="live-time">10:00</span>
                </div>
                <!--未关注时显示的图标-->
                <img src="@/assets/live/private-live-unfollow.png" alt="关注" class="unfollow-img" @click="">
            </div>
            <div class="close-live-room" @click="handleCloseLive">
                <img src="@/assets/common/close-live.png" alt="关闭直播">
            </div>
        </div>
        <!--中间是视频的部分 自己的视频-->
        <div class="private-video-chat-user" v-dragDom>
            <video src="https://media.w3.org/2010/05/sintel/trailer.mp4" autoplay muted loop class="live-video"></video>
            <div class="camera-close">
                <img src="@/assets/live/camera-close.png" alt="">
                <span>Camera Close</span>
            </div>
        </div>
        <!--中间的部分-->
        <div class="private-video-chat-center">
            <div class="private-video-chat-center-top">

            </div>
            <div class="private-video-chat-center-bottom">
                <PrivateChatBox :messageList="messageList" />
                <div class="private-video-chat-center-bottom-recharge">
                    <div class="recharge-box">
                        <div class="recharge-box-text">
                            Recharge
                        </div>
                    </div>
                    <div class="wish-gift-panel-tip" @click.stop="console.log(111)">

                        <span class="electric-text">The call will end in 180 seconds.
                            Please recharge as soon as possible.</span>
                        <img src="@/assets/live/electric-close.png" alt="" class="electric-close"
                            @click.stop="console.log(222)">
                    </div>
                </div>
            </div>
        </div>
        <!--下面的部分 礼物以及聊天-->
        <div class="private-video-chat-bottom">
            <div class="private-video-chat-bottom-gift-list">
                <div class="private-video-chat-bottom-gift-list-item" v-for="item in 10">
                    <LazyImage :src="gift1" alt="" class="private-video-chat-bottom-gift-list-item-img" />
                    <div class="private-video-chat-bottom-gift-list-item-text">
                        <img src="@/assets/common/coins.png" alt="">
                        <span>100</span>
                    </div>
                </div>
            </div>
            <!--发消息部分-->
            <div class="bottom-message">
                <PrivateChatMessage v-model="messageText" :showTranslate="showTranslate"
                    @update:showTranslate="changeShowTranslate" @show-gift="showChatGiftPopup = true" />
            </div>
        </div>
        <PrivateRemindPopup :show="showPrivateRemindPopup" @update:show="showPrivateRemindPopup = $event" />
        <ClosePrivateChatPopup :show="showClosePrivateChatPopup" @update:show="showClosePrivateChatPopup = $event" />
        <chatGiftPopup :show="showChatGiftPopup" @update:show="showChatGiftPopup = $event"
            @changeShowDiscountPopup="changeShowDiscountPopup" @changeCoinsNotEnoughPopup="changeCoinsNotEnoughPopup" />
        <discountPopup :show="showDiscountPopup" @update:show="showDiscountPopup = $event" />

        <EvaluatePrivateAnchor :show="showEvaluateAnchor" @update:show="showEvaluateAnchor = $event" />
        <OrderEndSendGiftPopup :show="showOrderEndSendGiftPopup" @update:show="showOrderEndSendGiftPopup = $event" />
        <!--充值弹窗-->
        <RechargePopup :show="showRechargePopup"></RechargePopup>
    </div>
</template>

<script lang="ts" setup name="PrivateVideoChat">
import { ref } from 'vue'
import PrivateRemindPopup from './components/privateVideoComponents/privateRemindPopup.vue'
import PrivateChatBox from './components/privateVideoComponents/privateChatBox.vue'
import PrivateChatMessage from './components/privateVideoComponents/privateChatMessage.vue'
import LazyImage from '@/components/LazyImage.vue'
import gift1 from '@/assets/test/gift.png'
import snackbar from '@/components/snackbar'
import ClosePrivateChatPopup from './components/privateVideoComponents/closePrivateChatPopup.vue'
import chatGiftPopup from '@/components/chatGiftPopup/chatGiftPopup.vue'
import discountPopup from '@/components/discountPopup/discountPopup.vue'
import EvaluatePrivateAnchor from './components/privateVideoComponents/evaluatePrivateAnchor.vue'
import OrderEndSendGiftPopup from './components/privateVideoComponents/orderEndSendGiftPopup.vue'
import RechargePopup from '@/components/rechargePopup/rechargePopup.vue'
const anchorInfo = ref({})
const userInfo = ref({}) //用户信息
const liveRoomType = ref('') //私密聊天
const liveRoomStatus = ref('Pending') //私密聊天状态
const showPrivateRemindPopup = ref(false) //私密提醒
const showTranslate = ref(false) //翻译
const showClosePrivateChatPopup = ref(false) //关闭私密聊天
const showChatGiftPopup = ref(false) //聊天礼物
const showDiscountPopup = ref(false) //折扣
const showEvaluateAnchor = ref(false) //评价主播
const showOrderEndSendGiftPopup = ref(false) //订单结束送礼物

const showRechargePopup = ref(true) //充值弹窗
const messageText = ref('') //消息内容
const messageList = ref([
    {
        id: Math.random(),
        userName: '11111',
        message: '11111',
        messageType: '1',
        userType: 1,
        vipType: 0,
    },
    {
        id: Math.random(),
        userName: '22222',
        message: 'Just a test',
        messageType: '1',
        userType: 2,
        vipType: 0,
    },
    {
        id: Math.random(),
        userName: '33333',
        message: 'Join Room',
        userType: 1,
        messageType: '2',
        vipType: 0,
    },
    {
        id: Math.random(),
        userName: '44sfafsdfasfa',
        message: 'Join Room',
        messageType: '2',
        vipType: 1,
        vipLevel: 1
    },
    {
        id: Math.random(),
        userName: '55555',
        message: 'Join Room',
        messageType: '2',
        vipType: 2,
        userType: 1,
        vipLevel: 21
    },
    {
        id: Math.random(),
        userName: '66666',
        message: 'Join Room',
        messageType: '2',
        vipType: 2,
        userType: 1,
        vipLevel: 12
    }
])

const changeShowTranslate = (show: boolean) => {
    showTranslate.value = show
    snackbar.notice('Notification', 'Translation turned on', 2000)
}
/**
 * 关闭私密聊天
 */
const handleCloseLive = () => {
    showClosePrivateChatPopup.value = true
}

/**
 * 显示折扣
 */
const changeShowDiscountPopup = () => {
    showDiscountPopup.value = true
}

/**
 * 显示金币不足
 */
const changeCoinsNotEnoughPopup = () => {
    // showCoinsNotEnoughPopup.value = trues
}


</script>
<style lang='scss' scoped>
.private-video-chat {
    width: 100%;
    height: 100%;
    flex-direction: column;
    overflow: hidden;
    background-color: #010101;
    position: relative;
    font-family: var(--font-family-urbanist);

    .private-video-chat-box {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background: url('@/assets/test/bg.png') no-repeat center center;
        background-size: 100% 100%;

        &-pending {
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: #343434B2;
                backdrop-filter: blur(30px)
            }
        }
    }

    .private-video-chat-header {
        margin-top: 11px;
        width: 100%;
        height: 44px;
        position: relative;
        padding: 0 15px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        &-left {
            padding: 6px;
            box-sizing: border-box;
            background: rgba($color: #000000, $alpha: .15);
            border-radius: 37px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);

            .left-img {
                width: 30px;
                height: 30px;
                border-radius: 50%;
                object-fit: cover;
                object-position: top;
                border: 2px solid rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;

                &:hover {
                    border-color: rgba(255, 255, 255, 0.5);
                    transform: scale(1.05);
                }
            }

            .live-name-num {
                display: flex;
                flex-direction: column;
                justify-content: center;
                margin-left: 6px;
                margin-top: -4px;

                .live-name {
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 17px;
                    color: #fff;
                }

                .live-time {
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 14px;
                    color: rgba($color: #fff, $alpha: .7);
                }
            }

            .unfollow-img {
                width: 34px;
                height: 30px;
                margin-inline-start: 4px;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    transform: scale(1.1);
                }
            }
        }

        .close-live-room {
            width: 24px;
            height: 24px;
            cursor: pointer;

            img {
                width: 100%;
                height: 100%;
            }
        }
    }

    .private-video-chat-user {
        position: absolute;
        right: 17px;
        top: calc(env(safe-area-inset-top) + 70px);
        width: 130px;
        height: 210px;
        border-radius: 12px;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #3C3C3CCC;
            backdrop-filter: blur(20px);
            z-index: 0;
        }

        .live-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: top;
        }

        .camera-close {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1;

            img {
                width: 40px;
                height: 40px;
            }

            span {
                margin-top: 7px;
                font-size: 14px;
                font-weight: 500;
                color: rgba($color: #fff, $alpha: .7);
                line-height: 17px;
            }
        }
    }

    .private-video-chat-center {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        padding: 0 15px;
        box-sizing: border-box;

        .private-video-chat-center-top {
            width: 100%;
            height: 80vw;
        }

        .private-video-chat-center-bottom {
            width: 100%;
            flex: 1;
            display: flex;
            justify-content: space-between;
            gap: 20px;

            .private-video-chat-center-bottom-recharge {
                flex-shrink: 0;
                width: 80px;
                position: relative;
                display: flex;
                flex-direction: column;
                justify-content: flex-end;

                .recharge-box {
                    width: 100%;
                    height: 80px;
                    position: relative;
                    background: url('@/assets/live/private-recharge.png') no-repeat center center;
                    background-size: 100% 100%;

                    .recharge-box-text {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        width: 100%;
                        background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                        box-shadow: 0px 0px 4px 1px #FFFFFF inset;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding: 4px 10px;
                        box-sizing: border-box;
                        font-size: 14px;
                        font-weight: 900;
                        color: #fff;
                        line-height: 17px;
                        border-radius: 30px;
                    }
                }

                .wish-gift-panel-tip {
                    position: absolute;
                    bottom: 80px;
                    right: 0;
                    max-width: 250px;
                    width: -moz-max-content;
                    width: max-content;
                    padding: 10px 40px 10px 10px;
                    box-sizing: border-box;
                    border-radius: 6px;
                    background: rgba(246, 17, 112, 0.5);
                    display: flex;
                    gap: 2px;

                    &::before {
                        content: '';
                        position: absolute;
                        bottom: -6px;
                        right: 31px;
                        width: 0;
                        height: 0;
                        border-left: 6px solid transparent;
                        border-right: 6px solid transparent;
                        border-top: 6px solid rgba(246, 17, 112, .5);
                    }

                    .electric-close {
                        position: absolute;
                        right: 10px;
                        top: 10px;
                        width: 8px;
                    }

                    .electric-text {
                        font-size: 14px;
                        font-weight: 500;
                        line-height: 17px;
                        color: #fff;
                    }
                }
            }
        }
    }

    .private-video-chat-bottom {
        margin-top: 21px;
        padding-bottom: calc(env(safe-area-inset-bottom) + 15px);
        padding-inline: 15px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        gap: 10px;

        .private-video-chat-bottom-gift-list {
            display: flex;
            gap: 10px;
            overflow-x: auto;
            padding: 5px 0;

            // 自定义滚动条样式
            &::-webkit-scrollbar {
                height: 4px;
            }

            &::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 2px;
            }

            &::-webkit-scrollbar-thumb {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 2px;

                &:hover {
                    background: rgba(255, 255, 255, 0.5);
                }
            }

            .private-video-chat-bottom-gift-list-item {
                width: 50px;
                height: 64px;
                flex-shrink: 0;
                display: flex;
                flex-direction: column;
                align-items: center;

                &-img {
                    width: 100%;
                    height: 50px;
                    object-fit: cover;
                    border-radius: 8px;
                }

                &-text {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 2px;
                    margin-top: 4px;
                    padding: 2px 6px;
                    // background: rgba(0, 0, 0, 0.6);
                    border-radius: 10px;
                    backdrop-filter: blur(10px);

                    img {
                        flex-shrink: 0;
                        width: 12px;
                        height: 12px;
                    }

                    span {
                        font-size: 11px;
                        font-weight: 600;
                        line-height: 14px;
                        color: #fff;
                        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
                    }
                }
            }
        }
    }
}
</style>