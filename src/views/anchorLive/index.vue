<template>
    <div class="live-container">
        <swiper :direction="'vertical'" :slides-per-view="1" :space-between="0" :mousewheel="true" :keyboard="true"
            :loop="false" :centered-slides="true" :initial-slide="1" @swiper="onSwiper" @slideChange="onSlideChange"
            class="live-swiper">

            <swiper-slide v-for="(live, index) in visibleLiveList" :key="`${live.id}-${live.uniqueKey}`"
                class="live-slide">
                <PremiumContent :live="live" v-if="live.isPremium" @want-join="handleWantJoin" />
                <div class="live-slide-bg" :style="{ backgroundImage: `url(${live.coverUrl})` }"
                    v-if="!live.pkData && !live.isPremium">
                    <div class="live-slide-overlay"></div>
                    <div class="live-slide-content">
                        <video src="https://media.w3.org/2010/05/sintel/trailer.mp4" autoplay muted loop
                            class="live-video"></video>
                    </div>
                </div>
                <div :ref="`vapContainer_${index}`" v-if="live.pkData" class="vap-container"></div>
                <div class="live-slide-bg"
                    :style="{ backgroundImage: live.coverUrl ? `url(${live.coverUrl})` : `url(${vcrbBg})` }"
                    v-if="live.pkData">

                </div>
                <div class="live-item" v-if="activeSlideIndex === index">
                    <!-- 关闭按钮 -->
                    <div class="close-live-room" @click="handleCloseLive">
                        <img src="@/assets/common/close-live.png" alt="关闭直播">
                    </div>
                    <!-- 内嵌swiper用于清屏功能 -->
                    <swiper v-bind="innerSwiperConfig" @swiper="(swiper) => onInnerSwiper(swiper, live.id)"
                        @slideChange="onInnerSlideChange" class="inner-live-swiper" :key="`inner-swiper-${live.id}`">
                        <swiper-slide class="inner-live-slide">
                            <!-- 有内容的页面 -->
                            <div class="live-content">
                                <!-- 头部信息 -->
                                <LiveHeader :live="live" @follow="handleFollowAnchor"
                                    @show-live-ranking-popup="showLiveRankingPopup = true"
                                    @open-user-info-popup="changeUserInfoPopup" />

                                <!--未满礼物-->
                                <LiveGiftProgress :progress="progress" :show-tip="showNotFullGiftTip"
                                    @toggle-tip="showNotFullGiftTip = !showNotFullGiftTip"
                                    @send-gift="handleSendGift" />

                                <div class="live-flex">
                                    <!--中间礼物部分-->
                                    <div class="live-flex-top">
                                        <div class="wish-gift-layer">
                                            <div class="wish-gift-panel" @click="changeShowReminderPopup"
                                                :class="{ 'is-active': showReminderPopup }">
                                                <img src="https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1704698327whale_2024.gif"
                                                    alt="期待礼物" class="wish-gift-panel-img">
                                                <div class="wish-gift-panel-coins">
                                                    <img src="@/assets/common/coins.png" alt="金币">
                                                    <span>15000</span>
                                                </div>
                                                <!--期待礼物进度-->
                                                <div class="wish-gift-panel-progress">
                                                    <div class="wish-gift-panel-progress-item"></div>
                                                    <div class="wish-gift-panel-progress-item-text">
                                                        <span>0/1</span>
                                                    </div>
                                                </div>
                                                <div class="wish-gift-panel-tip" v-if="showWishGiftTip"
                                                    @click.stop="console.log(111)">
                                                    <img src="@/assets/live/electric-light.png" alt=""
                                                        class="electric-light">
                                                    <span class="electric-text">New feature- Fulfill her
                                                        wish with one click!</span>
                                                    <img src="@/assets/live/electric-close.png" alt=""
                                                        class="electric-close" @click.stop="console.log(222)">
                                                </div>
                                            </div>
                                        </div>
                                        <!---中间PK 的数据-->
                                        <div class="pk-box" v-if="live.pkData">
                                            <div class="pk-video">
                                                <div class="pk-video-item" :data-anchor="live.pkData.homePlayer">
                                                    <video src="https://media.w3.org/2010/05/sintel/trailer.mp4"
                                                        autoplay muted loop class="live-video"></video>
                                                </div>
                                                <!--PK 的另一个人-->
                                                <div class="pk-video-item" :data-anchor="live.pkData.awayPlayer">
                                                    <video src="https://media.w3.org/2010/05/sintel/trailer.mp4"
                                                        autoplay muted loop class="live-video"></video>
                                                    <div class="pk-video-item-header">
                                                        <!-- <div class="pk-video-item-header-left">

                                                        </div>
                                                        <div class="pk-video-item-header-right"> -->
                                                        <img :src="live.pkData.awayPlayer.hostAvatar" alt=""
                                                            class="anchor-avatar">
                                                        <span class="anchor-name">{{ live.pkData.awayPlayer.hostName
                                                            }}</span>
                                                        <!-- </div> -->

                                                    </div>
                                                    <div class="opposite">
                                                        <span class="opposite-text">Opposite</span>
                                                        <img src="@/assets/live/opposite-right.png" alt=""
                                                            class="opposite-icon">
                                                    </div>
                                                </div>
                                                <div class="pk-video-footer">
                                                    <img src="@/assets/live/vs.png" alt="" class="pk-icon">
                                                    <span class="pk-text" :class="{ 'is-countdown': isCountdown }">{{
                                                        pkTimeText }}</span>
                                                </div>
                                            </div>
                                            <!--下面是进度条-->
                                            <div class="pk-progress">
                                                <div class="pk-progress-home">0</div>
                                                <div class="pk-progress-away">0</div>
                                            </div>
                                        </div>
                                        <!-- 礼物动画区域 -->
                                        <div class="gift-animation-container">
                                            <GiftAnimation :ref="`giftAnimationRef_${live.id}`" />
                                        </div>
                                    </div>
                                    <div class="live-flex-bottom">
                                        <!-- 这里可以添加更多内容 -->
                                        <!--聊天内容-->
                                        <liveChatBox :messageList="messageList" />
                                        <!--活动内容-->
                                        <liveActiveComponents :showPrivateChat="false"
                                            @open-private-chat-popup="openPrivateChatPopupFunc" />
                                    </div>
                                    <!--发消息部分-->
                                    <LiveMessageBox v-model="messageText" :show-say-hi="showSayHi"
                                        :show-send-gift="showSendGift" @send="handleSendMessage"
                                        @show-tools="changeShowRoomTools" @show-coins="changeShowCoinsNotEnoughPopup" />
                                </div>


                            </div>
                        </swiper-slide>
                        <swiper-slide class="inner-live-slide">
                            <!-- 清屏页面 -->
                            <div class="live-clear-screen" @click="returnToNormal(live.id)">
                            </div>
                        </swiper-slide>
                    </swiper>
                    <!--新手礼物倒计时-->
                    <div class="new-gift-countdown" @click="changeShowDiscountPopup">
                        <div class="new-gift-countdown-time">
                            {{ newGiftCountdown }}
                        </div>
                    </div>
                    <!-- 礼物图标 -->
                    <div class="send-gift-icon" @click="changeShowChatGiftPopup">
                        <img src="@/assets/anchor/live-gift-icon.png" alt="发送礼物">
                    </div>

                    <!-- VAP调试按钮 -->
                    <div class="vap-debug-btn" @click="debugVapPlayback" v-if="live.pkData">
                        VAP调试
                    </div>

                    <!-- VAP重新播放按钮 -->
                    <div class="vap-replay-btn" @click="initVapPlayer(activeSlideIndex)" v-if="live.pkData">
                        重新播放VAP
                    </div>
                </div>
            </swiper-slide>
        </swiper>
    </div>

    <!-- 弹窗组件 -->
    <LivePopups v-bind="popupStates" :anchor-info="anchorInfo" :user-info="userInfo" :live-room-type="liveRoomType"
        @update:follow="showFollowAnchorPopup = $event" @update:report-anchor="showReportAnchorDialog = $event"
        @update:discount="showDiscountPopup = $event" @update:reminder="showReminderPopup = $event"
        @update:tools="showRoomTools = $event" @update:coins="showCoinsNotEnoughPopup = $event"
        @update:chat-gift="showChatGiftPopup = $event" @change-discount="changeShowDiscountPopup"
        @change-coins="changeShowCoinsNotEnoughPopup" @update:switch-live-room-tip="showSwitchLiveRoomTip = $event"
        @update:live-ranking="showLiveRankingPopup = $event" @update:user-info="showUserInfoPopup = $event"
        @update:user-info-anchor="userInfo.isAnchor = $event" @update:live-room-tip="showLiveRoomTip = $event"
        @update:honor-medals="showHonorMedalsDialog = $event" @update:gift-wall="showGiftWallDialog = $event"
        @update:level="showLevelPopup = $event" />
    <!-- 私聊弹窗 -->
    <PrivateChatPopup :show="showPrivateChatPopup" :anchor-info="anchorInfo"
        @update:show="showPrivateChatPopup = $event" />
</template>

<script lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Mousewheel, Keyboard, Autoplay } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/mousewheel'
import 'swiper/css/keyboard'
import snackbar from '@/components/snackbar'
import vcrbBg from '@/assets/live/vcrb-bg.png'
import LiveHeader from './components/LiveHeader.vue'
import LiveGiftProgress from './components/LiveGiftProgress.vue'
import liveChatBox from './components/liveChatBox.vue'
import liveActiveComponents from './components/liveActiveComponents.vue'
import LiveMessageBox from './components/LiveMessageBox.vue'
import LivePopups from './components/LivePopups.vue'
import PrivateChatPopup from '@/components/privateChatPopup/privateChatPopup.vue'
import GiftAnimation from '@/components/GiftAnimation.vue'
import PremiumContent from './components/premiumContent.vue'
import Vap from 'video-animation-player'
import vs3Data from '@/test/vs3.json'
export default {
    name: 'AnchorLiveModule',
    components: {
        Swiper,
        SwiperSlide,
        LiveHeader,
        LiveGiftProgress,
        LiveMessageBox,
        LivePopups,
        liveChatBox,
        liveActiveComponents,
        PrivateChatPopup,
        GiftAnimation,
        PremiumContent
    },

    data() {
        return {
            showWishGiftTip: false,
            vcrbBg: vcrbBg,
            anchorInfo: {
                id: 1,
                name: "Jane Cooper",
                avatar: "https://picsum.photos/1000/500?random=1",
                desc: "A gentleman is everything that is good. 😍",
                follow: false
            },
            userInfo: {
                isAnchor: false,
            },
            showLevelPopup: false, //等级弹窗
            showPrivateChatPopup: false, //私聊弹窗
            showReportAnchorDialog: false, //举报弹窗
            showLiveRoomTip: false, //切换直播间弹窗
            showHonorMedalsDialog: false, //勋章弹窗
            showGiftWallDialog: false, //礼物墙弹窗
            liveRoomType: 'leave',
            showSwitchLiveRoomTip: false, //怎么切换直播间弹窗提示
            showRoomTools: false, //更多设置弹窗
            showFollowAnchorPopup: false, //关注主播弹窗
            showDiscountPopup: false, //折扣弹窗
            showCoinsNotEnoughPopup: false, //金币不足弹窗
            showReminderPopup: false, //reminder 提示
            showChatGiftPopup: false, //聊天礼物弹窗
            showLiveRankingPopup: false, //直播榜排行榜弹窗
            showUserInfoPopup: false, //用户信息弹窗
            currentIndex: 1, // 永远在中间位置
            visibleLiveList: [] as any[], // 永远只有3个元素
            activeSlideIndex: 1, // 当前激活的slide索引
            innerSwiperInstances: {} as any, // 内层swiper实例
            innerActiveSlideIndex: 0, // 内层swiper当前激活的slide索引
            messageText: '', // 消息文本
            showSayHi: false, // 显示say-hi提示
            showSendGift: false, // 显示send-gift提示
            giftIdCounter: 0, // 礼物ID计数器
            swiperInstance: null as any,
            showNotFullGiftTip: false,
            // 数据管理
            allLiveList: [] as any[], // 所有已加载的数据
            currentDataIndex: 0, // 当前在 allLiveList 中的位置
            progress: 200,
            // 初始数据
            initialData: [],
            // 加载状态
            hasMoreUp: true,
            hasMoreDown: true,
            isLoadingUp: false,
            isLoadingDown: false,
            newGiftCountdown: "00:00:00",
            // 数据ID管理
            nextId: 4, // 下一个新数据的ID
            prevId: 0,  // 上一个新数据的ID
            // 礼物动画数据
            giftAnimations: [] as any[],
            messageList: [
                {
                    id: Math.random(),
                    userName: '11111',
                    message: '11111',
                    messageType: '1',
                    userType: 1,
                    vipType: 0,
                },
                {
                    id: Math.random(),
                    userName: '22222',
                    message: 'Just a test',
                    messageType: '1',
                    userType: 2,
                    vipType: 0,
                },
                {
                    id: Math.random(),
                    userName: '33333',
                    message: 'Join Room',
                    userType: 1,
                    messageType: '2',
                    vipType: 0,
                },
                {
                    id: Math.random(),
                    userName: '44sfafsdfasfa',
                    message: 'Join Room',
                    messageType: '2',
                    vipType: 1,
                    vipLevel: 1
                },
                {
                    id: Math.random(),
                    userName: '55555',
                    message: 'Join Room',
                    messageType: '2',
                    vipType: 2,
                    userType: 1,
                    vipLevel: 21
                },
                {
                    id: Math.random(),
                    userName: '66666',
                    message: 'Join Room',
                    messageType: '2',
                    vipType: 2,
                    userType: 1,
                    vipLevel: 12
                }
            ],
            pkTime: 20,
            pkTimeText: '02:32',
            isCountdown: false,
            vap: null as any
        }
    },
    mounted() {
        this.loadLiveData() // 使用新的数据加载方法
        this.startNewGiftCountdown()

        // 延迟5秒后自动开始礼物演示
        setTimeout(() => {
            this.testGiftDemo()
        }, 5000)
    },

    beforeUnmount() {
        // 清理VAP实例
        if (this.vap) {
            try {
                this.vap.destroy()
                this.vap = null
            } catch (e) {
                console.warn('清理VAP实例失败:', e)
            }
        }
    },
    methods: {

        startNewGiftCountdown() {
            var time = 36000

            const interval = setInterval(() => {
                time--
                const hours = Math.floor(time / 3600)
                const minutes = Math.floor((time % 3600) / 60)
                const seconds = time % 60
                this.newGiftCountdown = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
                if (time <= 0) {
                    clearInterval(interval)
                }
            }, 1000)

        },

        changeShowDiscountPopup() {
            this.showDiscountPopup = !this.showDiscountPopup
        },

        changeShowReminderPopup() {
            this.showReminderPopup = !this.showReminderPopup
        },
        changeShowRoomTools() {
            this.showRoomTools = !this.showRoomTools
        },
        changeShowCoinsNotEnoughPopup() {
            this.showCoinsNotEnoughPopup = !this.showCoinsNotEnoughPopup
        },
        changeShowChatGiftPopup() {
            this.showChatGiftPopup = !this.showChatGiftPopup
        },

        changeUserInfoPopup() {
            this.showUserInfoPopup = true
        },
        initLiveData() {
            // 初始化数据：将初始数据复制到 allLiveList
            this.allLiveList = [...this.initialData]
            this.currentDataIndex = 1 // 从中间开始
            this.activeSlideIndex = 1 // 确保activeSlideIndex也被初始化

            // 初始化可见列表（永远只有3个）
            this.updateVisibleLiveList()
            console.log('初始化直播数据，当前位置:', this.currentDataIndex)
        },

        updateVisibleLiveList() {
            const totalLength = this.allLiveList.length
            if (totalLength === 0) return

            // 确保有足够的数据（至少3个）
            if (totalLength < 3) {
                console.warn('数据不足3个，无法正常显示')
                return
            }

            // 计算三个位置的数据索引
            let prevIndex = this.currentDataIndex - 1
            let currentIndex = this.currentDataIndex
            let nextIndex = this.currentDataIndex + 1

            // 边界处理
            if (prevIndex < 0) {
                prevIndex = 0
                // 如果到达顶部，尝试加载更多数据
                this.loadMoreLives('up')
            }
            if (nextIndex >= totalLength) {
                nextIndex = totalLength - 1
                // 如果到达底部，尝试加载更多数据
                this.loadMoreLives('down')
            }

            // 更新可见列表，添加唯一key确保Vue正确更新
            this.visibleLiveList = [
                { ...this.allLiveList[prevIndex], uniqueKey: `${this.allLiveList[prevIndex].id}-${Date.now()}-prev` },
                { ...this.allLiveList[currentIndex], uniqueKey: `${this.allLiveList[currentIndex].id}-${Date.now()}-current` },
                { ...this.allLiveList[nextIndex], uniqueKey: `${this.allLiveList[nextIndex].id}-${Date.now()}-next` }
            ]

            console.log('更新可见列表:', {
                prevIndex,
                currentIndex,
                nextIndex,
                currentDataIndex: this.currentDataIndex,
                totalLength
            })
            // 组件渲染完成后的回调
            this.$nextTick(() => {
                // 可以在这里添加需要在组件渲染完成后执行的代码
                if (this.visibleLiveList[currentIndex].pkData) {
                    this.pkTime = 20
                    this.pkTimeCountdown()

                    // 延迟一点时间确保DOM完全渲染
                    setTimeout(() => {
                        this.initVapPlayer(currentIndex)
                    }, 100)
                }
            })

        },
        pkTimeCountdown() {
            const interval = setInterval(() => {
                this.pkTime--
                const minutes = Math.floor(this.pkTime / 60) < 10 ? `0${Math.floor(this.pkTime / 60)}` : Math.floor(this.pkTime / 60)
                const seconds = this.pkTime % 60 < 10 ? `0${this.pkTime % 60}` : this.pkTime % 60
                this.pkTimeText = minutes + ':' + seconds
                if (this.pkTime < 10) {
                    this.isCountdown = true
                    this.pkTimeText = this.pkTime.toString()
                }
                if (this.pkTime <= 0) {
                    clearInterval(interval)
                    this.isCountdown = false
                }
            }, 1000)
        },
        onSwiper(swiper: any) {
            console.log('Swiper初始化完成', swiper)
            this.swiperInstance = swiper
            // 确保activeSlideIndex被正确初始化
            this.activeSlideIndex = swiper.activeIndex || 1
        },

        onSlideChange(swiper: any) {
            const slideIndex = swiper.activeIndex
            console.log('Swiper切换到索引:', slideIndex)

            // 更新当前激活的slide索引
            this.activeSlideIndex = slideIndex

            // 根据滑动方向更新数据索引
            if (slideIndex === 0) {
                // 向上滑动到第一个slide
                this.currentDataIndex = Math.max(0, this.currentDataIndex - 1)
                console.log('向上滑动，数据索引:', this.currentDataIndex)
            } else if (slideIndex === 2) {
                // 向下滑动到第三个slide
                this.currentDataIndex = Math.min(this.allLiveList.length - 1, this.currentDataIndex + 1)
                console.log('向下滑动，数据索引:', this.currentDataIndex)
            }

            // 更新可见列表
            this.updateVisibleLiveList()

            // 重置swiper到中间位置（索引1）
            this.$nextTick(() => {
                if (this.swiperInstance && slideIndex !== 1) {
                    this.swiperInstance.slideTo(1, 0) // 0ms动画，瞬间切换
                    this.currentIndex = 1
                    // 重置activeSlideIndex到中间位置
                    this.activeSlideIndex = 1
                }
            })

            // 暂停其他视频，播放当前视频
            this.pauseAllVideos()
            this.playCurrentVideo()
        },

        onInnerSwiper(swiper: any, liveId?: number) {
            console.log('Inner Swiper初始化完成', swiper)
            if (liveId) {
                this.innerSwiperInstances[liveId] = swiper
            }
        },

        onInnerSlideChange(swiper: any) {
            const slideIndex = swiper.activeIndex
            console.log('Inner Swiper切换到索引:', slideIndex)

            // 更新内嵌swiper的激活索引
            this.innerActiveSlideIndex = slideIndex
        },

        // 清屏功能方法
        clearScreen(liveId?: number) {
            const swiper = liveId ? this.innerSwiperInstances[liveId] : null
            if (swiper) {
                swiper.slideTo(1, 300) // 滑动到清屏页面，300ms动画
                this.innerActiveSlideIndex = 1
            }
        },

        // 返回正常模式
        returnToNormal(liveId?: number) {
            const swiper = liveId ? this.innerSwiperInstances[liveId] : null
            if (swiper) {
                swiper.slideTo(0, 300) // 滑动到正常页面，300ms动画
                this.innerActiveSlideIndex = 0
            }
        },

        pauseAllVideos() {
            const videos = document.querySelectorAll('.live-video') as NodeListOf<HTMLVideoElement>
            videos.forEach(video => {
                video.pause()
            })
        },

        playCurrentVideo() {
            const currentVideo = document.querySelector(`.live-slide:nth-child(${this.currentIndex + 1}) .live-video`) as HTMLVideoElement
            if (currentVideo) {
                currentVideo.play().catch(err => {
                    console.log('自动播放失败:', err)
                })
            }
        },

        onVideoLoaded(event: Event) {
            const video = event.target as HTMLVideoElement
            console.log('视频加载完成:', video.src)
        },

        onVideoError(event: Event) {
            const video = event.target as HTMLVideoElement
            console.error('视频加载失败:', video.src)
        },

        loadMoreLives(direction: 'up' | 'down') {
            // 检查是否正在加载
            const isLoading = direction === 'up' ? this.isLoadingUp : this.isLoadingDown
            if (isLoading) {
                console.log(`${direction}方向正在加载中，跳过请求`)
                return
            }

            // 检查是否还有更多数据
            const hasMore = direction === 'up' ? this.hasMoreUp : this.hasMoreDown
            if (!hasMore) {
                console.log(`${direction}方向没有更多数据了`)
                return
            }

            // 检查是否需要加载（距离边界还有足够数据时不加载）
            if (direction === 'up' && this.currentDataIndex > 2) {
                return // 距离顶部还有足够数据
            }
            if (direction === 'down' && this.currentDataIndex < this.allLiveList.length - 3) {
                return // 距离底部还有足够数据
            }

            // 设置加载状态
            if (direction === 'up') {
                this.isLoadingUp = true
            } else {
                this.isLoadingDown = true
            }

            console.log(`开始${direction === 'down' ? '向下' : '向上'}加载数据`)

            // 模拟异步加载更多直播数据
            setTimeout(() => {
                const newLives = []
                const batchSize = 3 // 每次加载3个

                for (let i = 0; i < batchSize; i++) {
                    if (direction === 'down') {
                        newLives.push({
                            id: this.nextId++,
                            isPremium: true,
                            videoUrl: `https://example.com/live${this.nextId - 1}.mp4`,
                            coverUrl: `https://picsum.photos/300/200?random=${this.nextId - 1}`
                        })
                    } else {
                        newLives.push({
                            id: this.prevId--,
                            videoUrl: `https://example.com/live${this.prevId + 1}.mp4`,
                            coverUrl: `https://picsum.photos/300/200?random=${Math.abs(this.prevId + 1)}`
                        })
                    }
                }

                if (direction === 'down') {
                    // 向下加载：添加到列表末尾
                    this.allLiveList.push(...newLives)

                    // 模拟数据耗尽（加载到ID 20就没了）
                    if (this.nextId > 20) {
                        this.hasMoreDown = false
                        console.log('向下数据已加载完毕')
                    }
                } else {
                    // 向上加载：添加到列表开头
                    this.allLiveList.unshift(...newLives.reverse())
                    // 更新当前数据索引（因为在前面插入了数据）
                    this.currentDataIndex += newLives.length

                    // 模拟数据耗尽（加载到ID -10就没了）
                    if (this.prevId < -10) {
                        this.hasMoreUp = false
                        console.log('向上数据已加载完毕')
                    }
                }

                // 重置加载状态
                if (direction === 'up') {
                    this.isLoadingUp = false
                } else {
                    this.isLoadingDown = false
                }

                console.log(`${direction === 'down' ? '向下' : '向上'}加载完成，新增: ${newLives.length} 个，总数据: ${this.allLiveList.length}`)

                // 更新可见列表
                this.updateVisibleLiveList()
            }, 1000) // 模拟1秒加载时间
        },

        handleCloseLive() {
            this.clearScreen(this.visibleLiveList[this.activeSlideIndex].id);
        },

        handleFollowAnchor(liveId: number) {
            // 模拟关注逻辑
            const live = this.allLiveList.find(item => item.id === liveId);
            if (live) {
                live.follow = !live.follow;
                this.anchorInfo.follow = live.follow;
                this.showFollowAnchorPopup = true;
            }
        },

        handleSendGift() {
            // 模拟送礼物逻辑
            this.showNotFullGiftTip = false;
            this.showCoinsNotEnoughPopup = true;
        },

        handleSendMessage() {
            if (this.messageText.trim()) {
                console.log('发送消息:', this.messageText);
                this.messageText = '';
                this.showReminderPopup = true;
            }
        },

        // 优化数据加载
        async loadLiveData() {
            try {
                // 模拟API调用
                const response = await this.fetchLiveData();
                this.allLiveList = response as any[];
                this.updateVisibleLiveList();

            } catch (error) {
                console.error('加载直播数据失败:', error);
            }
        },

        // 模拟API调用
        fetchLiveData(): Promise<any[]> {
            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve([
                        {
                            id: 1,
                            anchorName: "Jane Cooper",
                            anchorAvatar: "https://picsum.photos/100/100?random=1",
                            viewCount: 1234,
                            onlineCount: 567,
                            onlineUsers: 10,
                            videoUrl: 'https://www.douyin.com/aweme/v1/play/?video_id=v0300fg10000c4b2hqjc77uab1jgt7og&line=0&file_id=8d35ca35a26649548388d7910162d67a&sign=ed509bf1cbbb555b18e3d1354ab451d3&is_play_url=1&source=PackSourceEnum_PUBLISH',
                            coverUrl: 'https://ilivegirl.s3.amazonaws.com/rekognition/36e80b024dd7a7b0522aae96e396ed6d20250801175620-medium.jpg',
                            follow: false,
                            pkData: {
                                "homePlayer": {
                                    "roomId": 2018706,
                                    "roomCover": "https://d36zjp6dbikxo9.cloudfront.net/rekognition/49b5d3219a127d76d0376c16e247541720250715185646.jpg",
                                    "agoraRoomId": "*********",
                                    "hostAccountId": ********,
                                    "hostName": "Ferrari رو حك ",
                                    "hostAvatar": "https://d36zjp6dbikxo9.cloudfront.net/rekognition/5ca2f23aab34fe659400385bbdaa880920250715185624.jpg",
                                    "roomStatus": 6,
                                    "wins": 3,
                                    "lastMonthIncomeRank": 0
                                },
                                "awayPlayer": {
                                    "roomId": 2018752,
                                    "roomCover": "https://ilivegirl.s3.amazonaws.com/main/56a3699c183da6137f7837d61da9cfa320250807184327.jpg",
                                    "agoraRoomId": "*********",
                                    "hostAccountId": ********,
                                    "hostName": "Lina القمر ❤️",
                                    "hostAvatar": "https://d36zjp6dbikxo9.cloudfront.net/rekognition/7786572f4560d1619bb29346af92a78620250412165805.jpg",
                                    "roomStatus": 6,
                                    "wins": -5,
                                    "lastMonthIncomeRank": 0
                                },
                                "homePoint": {
                                    "hostAccountId": ********,
                                    "points": 0,
                                    "topFanAvatars": []
                                },
                                "awayPoint": {
                                    "hostAccountId": ********,
                                    "points": 0,
                                    "topFanAvatars": []
                                },
                                "pkTime": *********,
                                "pkMaxDuration": 240,
                                "pkLeftTime": 11,
                                "winner": {
                                    "hostAccountId": 0,
                                    "wins": 0
                                }
                            }
                        },
                        {
                            id: 2,
                            anchorName: "John Doe",
                            anchorAvatar: "https://picsum.photos/100/100?random=2",
                            viewCount: 2345,
                            onlineCount: 789,
                            onlineUsers: 15,
                            videoUrl: 'https://www.douyin.com/aweme/v1/play/?video_id=v0200f0f0000bm29e50858limi4ovvg0&line=0&file_id=1ca3018457374fd8bfe020dd98112b3f&sign=0a49e48c13dd281b66d0e233c673ef5f&is_play_url=1&source=PackSourceEnum_PUBLISH',
                            coverUrl: 'https://ilivegirl.s3.amazonaws.com/rekognition/36e80b024dd7a7b0522aae96e396ed6d20250801175620-medium.jpg',
                            follow: true,
                            isPremium: true,
                        },
                        {
                            id: 3,
                            anchorName: "Alice Smith",
                            anchorAvatar: "https://picsum.photos/100/100?random=3",
                            viewCount: 3456,
                            onlineCount: 890,
                            onlineUsers: 20,
                            videoUrl: 'https://www.douyin.com/aweme/v1/play/?video_id=v0300f780000bj7drdon0160phos9cqg&line=0&file_id=e7bf37657a4f4418a472245a8cbe5195&sign=1c1655d9f81c9dc53a3ce087fd46a742&is_play_url=1&source=PackSourceEnum_PUBLISH',
                            coverUrl: 'https://ilivegirl.s3.amazonaws.com/rekognition/36e80b024dd7a7b0522aae96e396ed6d20250801175620-medium.jpg',
                            follow: false,
                            isPremium: false,
                        }
                    ]);
                }, 500);
            });
        },

        // 添加礼物动画
        addGiftAnimation(userName: string, giftName: string, giftUrl: string, vipType: number) {
            // 获取当前激活的live
            const currentLive = this.visibleLiveList[this.activeSlideIndex];
            if (!currentLive) {
                console.error('当前激活的live未找到');
                return;
            }

            // 使用动态ref名称获取礼物动画组件
            const refName = `giftAnimationRef_${currentLive.id}`;
            const giftAnimationRef = (this.$refs as any)[refName];

            // 如果ref是数组，取第一个元素
            let component = giftAnimationRef;
            if (Array.isArray(giftAnimationRef) && giftAnimationRef.length > 0) {
                component = giftAnimationRef[0];
            }

            if (component && typeof component.addGiftAnimation === 'function') {
                component.addGiftAnimation(userName, giftName, giftUrl, vipType);
            } else {
                console.error('GiftAnimation组件方法不可用');
            }
        },
        // 打开私人聊天弹窗
        openPrivateChatPopupFunc() {
            this.showPrivateChatPopup = true;
        },

        // 测试礼物动画 - 演示用
        testGiftDemo() {
            const gifts = [
                { userName: '张三', giftName: '玫瑰花', giftUrl: 'https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1693288687Teddybear2.png', vipType: 1 },
                { userName: '李四', giftName: '钻石', giftUrl: 'https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1694772305rose.png', vipType: 2 },
                { userName: '王五', giftName: '跑车', giftUrl: 'https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1694772609Hi.png', vipType: 0 },
                { userName: '赵六', giftName: '别墅', giftUrl: 'https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/17301117351730110177B-Day_Cake.png', vipType: 2 }
            ];

            gifts.forEach((gift, index) => {
                setTimeout(() => {
                    this.addGiftAnimation(gift.userName, gift.giftName, gift.giftUrl, gift.vipType);
                }, index * 1000);
            });

            // 测试同人同礼物合并效果
            setTimeout(() => {
                this.addGiftAnimation('张三', '玫瑰花', 'https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1693288687Teddybear2.png', 1);
            }, 5000);

            setTimeout(() => {
                this.addGiftAnimation('张三', '玫瑰花', 'https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1693288687Teddybear2.png', 1);
            }, 6000);
        },

        /**
         * 点击礼物解锁直播间
         */
        handleWantJoin() {
            this.showCoinsNotEnoughPopup = true;
        },

        /**
         * 初始化VAP播放器
         */
        initVapPlayer(slideIndex: number) {
            // 方法1: 使用动态ref名称
            const vapContainerRefName = `vapContainer_${slideIndex}`
            let vapContainer = (this.$refs as any)[vapContainerRefName] as HTMLElement | HTMLElement[]

            // 处理ref可能是数组的情况
            if (Array.isArray(vapContainer)) {
                vapContainer = vapContainer[0] as HTMLElement
            }

            // 方法2: 如果ref方式失败，尝试直接查询DOM
            if (!vapContainer || !('appendChild' in vapContainer)) {
                console.warn('通过ref获取容器失败，尝试DOM查询')
                const containers = document.querySelectorAll('.vap-container')
                if (containers.length > slideIndex) {
                    vapContainer = containers[slideIndex] as HTMLElement
                    console.log('通过DOM查询找到容器:', vapContainer)
                }
            }

            console.log('VAP容器引用名:', vapContainerRefName)
            console.log('VAP容器原始值:', (this.$refs as any)[vapContainerRefName])
            console.log('VAP容器处理后:', vapContainer)
            console.log('容器类型:', typeof vapContainer)
            console.log('是否为DOM元素:', vapContainer && typeof vapContainer === 'object' && 'appendChild' in vapContainer)

            if (vapContainer && typeof vapContainer === 'object' && 'appendChild' in vapContainer) {
                console.log('找到有效的VAP容器:', vapContainer)
                console.log('容器尺寸:', {
                    clientWidth: vapContainer.clientWidth,
                    clientHeight: vapContainer.clientHeight,
                    offsetWidth: vapContainer.offsetWidth,
                    offsetHeight: vapContainer.offsetHeight
                })

                // 清理之前的VAP实例
                if (this.vap) {
                    try {
                        this.vap.destroy()
                    } catch (e) {
                        console.warn('清理VAP实例失败:', e)
                    }
                }

                // 确保容器有尺寸
                if (vapContainer.clientWidth === 0 || vapContainer.clientHeight === 0) {
                    console.warn('VAP容器尺寸为0，设置默认尺寸')
                    vapContainer.style.width = '375px'
                    vapContainer.style.height = '375px'
                }

                // 根据正确的参数顺序：src(视频地址)、config(配置文件地址)、container(dom容器)
                const src = 'https://iulia.iwlive.club/webapp/cdn/vap/vs/vs3.mp4'
                // 如果config需要是URL地址而不是对象，我们可能需要提供配置文件的URL
                // 但目前我们先尝试使用配置对象，如果不行再改为URL
                const config = vs3Data
                const container = vapContainer as HTMLElement

                console.log('VAP参数:', {
                    src: src,
                    config: typeof config === 'object' ? 'config object' : config,
                    container: container,
                    configType: typeof config
                })
                console.log('容器appendChild方法:', typeof container.appendChild)
                console.log('配置对象内容:', config)

                try {
                    // 使用正确的参数顺序：src, config, container
                    this.vap = new Vap(src, config, container)
                    console.log('VAP实例创建成功:', this.vap)
                } catch (configError) {
                    console.warn('使用配置对象创建VAP失败，尝试使用配置文件URL:', configError)

                    // 如果配置对象不工作，尝试使用配置文件的URL
                    try {
                        // 尝试几个可能的配置文件路径
                        const possibleConfigUrls = [
                            '/vs3.json', // public目录下的文件
                            './vs3.json',
                            '/src/test/vs3.json',
                            'https://iulia.iwlive.club/webapp/cdn/vap/vs/vs3.json'
                        ]

                        let vapCreated = false
                        for (const configUrl of possibleConfigUrls) {
                            try {
                                console.log('尝试使用配置文件URL:', configUrl)
                                this.vap = new Vap(src, configUrl, container)
                                console.log('使用配置文件URL创建VAP成功:', this.vap)
                                vapCreated = true
                                break
                            } catch (e) {
                                console.warn(`配置文件URL ${configUrl} 失败:`, e)
                            }
                        }

                        if (!vapCreated) {
                            throw new Error('所有配置文件URL都失败了')
                        }
                    } catch (urlError) {
                        console.error('使用配置文件URL也失败:', urlError)
                        throw urlError
                    }
                }

                if (this.vap) {
                    // VAP可能不支持事件监听，直接播放
                    console.log('VAP实例方法:', Object.getOwnPropertyNames(this.vap))
                    console.log('VAP实例原型方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(this.vap)))

                    // 尝试添加事件监听（如果支持的话）
                    if (typeof this.vap.on === 'function') {
                        this.vap.on('loadStart', () => {
                            console.log('VAP开始加载')
                        })

                        this.vap.on('loadComplete', () => {
                            console.log('VAP加载完成')
                        })

                        this.vap.on('playStart', () => {
                            console.log('VAP开始播放')
                        })

                        this.vap.on('playComplete', () => {
                            console.log('VAP播放完成')
                        })

                        this.vap.on('error', (error: any) => {
                            console.error('VAP播放错误:', error)
                        })
                    } else {
                        console.log('VAP不支持事件监听')
                    }

                    // 开始播放
                    if (typeof this.vap.play === 'function') {
                        this.vap.play()
                        console.log('VAP播放命令已发送')
                    } else {
                        console.error('VAP实例没有play方法')
                    }
                } else {
                    console.error('VAP容器无效:', {
                        refName: vapContainerRefName,
                        container: vapContainer,
                        allRefs: this.$refs,
                        isHTMLElement: vapContainer && typeof vapContainer === 'object' && 'appendChild' in vapContainer
                    })
                }
            }
        },

        /**
         * 调试VAP播放问题
         */
        debugVapPlayback() {
            console.log('=== VAP 调试信息 ===')
            console.log('VAP实例:', this.vap)
            console.log('当前数据索引:', this.currentDataIndex)
            console.log('可见列表:', this.visibleLiveList)
            console.log('激活的slide索引:', this.activeSlideIndex)

            const currentLive = this.visibleLiveList[this.activeSlideIndex]
            console.log('当前直播数据:', currentLive)
            console.log('是否有PK数据:', !!currentLive?.pkData)

            if (currentLive?.pkData) {
                const vapContainerRefName = `vapContainer_${this.activeSlideIndex}`
                const vapContainer = (this.$refs as any)[vapContainerRefName]
                console.log('VAP容器引用名:', vapContainerRefName)
                console.log('VAP容器元素:', vapContainer)
                console.log('容器尺寸:', vapContainer ? {
                    width: vapContainer.clientWidth,
                    height: vapContainer.clientHeight,
                    offsetWidth: vapContainer.offsetWidth,
                    offsetHeight: vapContainer.offsetHeight
                } : 'N/A')
            }

            console.log('vs3Data配置:', vs3Data)
            console.log('==================')
        }
    },
    computed: {
        innerSwiperConfig() {
            return {
                direction: 'horizontal' as const,
                slidesPerView: 1,
                spaceBetween: 0,
                loop: false,
                centeredSlides: true,
                initialSlide: 0,
                allowTouchMove: true,
                touchRatio: 1,
                touchAngle: 45
            };
        },
        popupStates() {
            return {
                showFollowAnchorPopup: this.showFollowAnchorPopup,
                showDiscountPopup: this.showDiscountPopup,
                showReminderPopup: this.showReminderPopup,
                showRoomTools: this.showRoomTools,
                showCoinsNotEnoughPopup: this.showCoinsNotEnoughPopup,
                showChatGiftPopup: this.showChatGiftPopup,
                liveRoomType: this.liveRoomType,
                anchorInfo: this.anchorInfo,
                userInfo: this.userInfo,
                showSwitchLiveRoomTip: this.showSwitchLiveRoomTip,
                showLiveRankingPopup: this.showLiveRankingPopup,
                showUserInfoPopup: this.showUserInfoPopup,
                showLiveRoomTip: this.showLiveRoomTip,
                showHonorMedalsDialog: this.showHonorMedalsDialog,
                showGiftWallDialog: this.showGiftWallDialog,
                showReportAnchorDialog: this.showReportAnchorDialog,
                showLevelPopup: this.showLevelPopup,
            };
        }
    }
}
</script>

<style lang="scss" scoped>
.live-container {
    width: 100%;
    height: 100vh;
    position: relative;
    background: #000;
    overflow: hidden;
    font-family: var(--font-family-urbanist);
}

.live-swiper {
    width: 100%;
    height: 100%;
}

.live-slide {
    width: 100%;
    height: 100vh;
    position: relative;

    .vap-container {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 3;
        background-color: transparent;
        overflow: hidden;

        // VAP 视频元素样式
        video {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        // VAP canvas 元素样式
        canvas {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }


    .live-slide-bg {
        z-index: 1;
        position: absolute;
        top: 0;
        left: 0;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        background-attachment: fixed;
        width: 100%;
        height: 100%;
        transition: transform 0.3s ease;

        &:hover {
            transform: scale(1.05);
        }

        &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            backdrop-filter: blur(8px);
            transform: translateZ(0);
            background-size: cover;
            display: inherit;
            background-color: #0006;
        }
    }

    .live-slide-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 2;
    }
}

.live-item {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
}

// 关闭按钮 - 相对于live-item定位
.close-live-room {
    position: absolute;
    width: 24px;
    height: 24px;
    right: 15px;
    top: calc(env(safe-area-inset-top) + 20px);
    z-index: 9999;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;

    img {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
    }
}

.send-gift-icon {
    position: absolute;
    width: 32px;
    height: 32px;
    right: 15px;
    bottom: 33px;
    z-index: 9999;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
        background: rgba(0, 0, 0, 0.6);
        transform: scale(1.1);
    }

    img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        box-sizing: border-box;
    }
}

// VAP调试按钮
.vap-debug-btn {
    position: absolute;
    right: 15px;
    bottom: 80px;
    z-index: 9999;
    cursor: pointer;
    padding: 8px 12px;
    background: rgba(255, 0, 0, 0.7);
    color: white;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.vap-replay-btn {
    position: absolute;
    right: 15px;
    bottom: 120px;
    z-index: 9999;
    cursor: pointer;
    padding: 8px 12px;
    background: rgba(0, 255, 0, 0.7);
    color: white;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

// 内嵌swiper样式
.inner-live-swiper {
    width: 100%;
    height: 100%;
}

.inner-live-slide {
    width: 100%;
    height: 100%;
    position: relative;
}

.live-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

// 清屏模式样式
.live-clear-screen {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    z-index: 10;
    animation: fadeIn 0.3s ease-in-out;
}

.live-flex {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 0 0 31px;
    box-sizing: border-box;
    overflow: hidden;

    .live-flex-top {
        height: 100vw;
        box-sizing: border-box;
        position: relative;
        z-index: 5;

        //PK的数据
        .pk-box {
            width: 100%;
            height: 100%;
            margin-top: 8px;

            .pk-video {
                width: 100%;
                height: 84.266667vw;
                display: flex;
                position: relative;

                .pk-video-item {
                    flex: 1;
                    height: 100%;
                    position: relative;

                    .live-video {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                        object-position: center center;
                        background-color: #fff;
                    }

                    .pk-video-item-header {
                        position: absolute;
                        top: 10px;
                        right: 10px;
                        width: 75px;
                        height: 26px;
                        display: flex;
                        align-items: center;
                        gap: 4px;
                        background-color: #0000004D;
                        border-radius: 30px;
                        overflow: hidden;

                        .anchor-avatar {
                            width: 22px;
                            height: 22px;
                            border-radius: 50%;
                            object-fit: cover;

                        }

                        .anchor-name {
                            flex: 1;
                            font-size: 12px;
                            color: #fff;
                            font-weight: 600;
                            line-height: 14px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                    }

                    .opposite {
                        position: absolute;
                        bottom: 0;
                        right: 0;
                        top: 0;
                        margin: auto;
                        display: flex;
                        align-items: center;
                        padding: 5px 10px 5px 5px;
                        height: max-content;
                        background-color: #0000004D;
                        gap: 3px;
                        border-radius: 20px 0 0 20px;

                        .opposite-text {
                            font-size: 12px;
                            font-weight: 600;
                            color: #fff;
                            line-height: 14px;
                        }

                        .opposite-icon {
                            width: 12px;
                            height: 12px;
                        }
                    }
                }

                .pk-video-footer {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    margin: 0 auto;
                    width: 80px;
                    height: 20px;
                    background: url('@/assets/live/pk-time-bg.png') no-repeat;
                    background-size: 100% 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 3px;

                    .pk-icon {
                        width: 19px;
                        height: 11px;
                    }

                    .pk-text {
                        font-size: 12px;
                        font-weight: 600;
                        color: #fff;
                        line-height: 14px;

                        &.is-countdown {
                            animation: countdown 1s ease-in infinite;
                        }

                        @keyframes countdown {
                            0% {
                                transform: scale(1);
                                transform-origin: 0 100%;
                            }

                            30% {
                                transform: scale(1.3);
                                transform-origin: 0 100%;
                            }

                            60% {
                                transform: scale(1);
                                transform-origin: 0 100%;
                            }
                        }
                    }
                }

                .pk-progress {
                    width: 100%;
                    display: flex;
                    height: 14px;
                    align-items: center;
                    font-size: 12px;
                    font-weight: 600;
                    color: #000;
                    line-height: 15px;

                    .pk-progress-home {
                        width: 50%;
                        height: 100%;
                        background-color: #E6396E;
                        padding-inline-start: 16px;
                    }

                    .pk-progress-away {
                        width: 50%;
                        height: 100%;
                        background-color: #4090F6;
                        text-align: end;
                        padding-inline-end: 16px;
                    }
                }
            }
        }

        .wish-gift-layer {
            position: absolute;
            flex-shrink: 0;
            flex-grow: 1;
            margin: 13px 12px;

            .wish-gift-panel {
                width: 69px;
                min-height: 92px;
                padding-top: 10px;
                box-sizing: border-box;
                height: auto;
                background: rgba(0, 0, 0, .2);
                border-radius: 9px;
                opacity: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                position: relative;

                &.is-active {
                    animation: scale 0.5s ease;
                }

                @keyframes scale {
                    0% {
                        transform: scale(1);
                    }

                    50% {
                        transform: scale(1.2);
                    }

                    100% {
                        transform: scale(1);
                    }
                }

                .wish-gift-panel-img {
                    width: 34px;
                    height: 34px;
                    object-fit: contain;
                }

                .wish-gift-panel-coins {
                    margin-top: 3px;
                    display: flex;
                    align-items: center;
                    gap: 2px;

                    img {
                        width: 12px;
                        height: 12px;
                    }

                    span {
                        font-size: 12px;
                        font-weight: 600;
                        line-height: 14px;
                        color: #fff;
                    }
                }

                .wish-gift-panel-progress {
                    margin-top: 10px;
                    display: flex;
                    align-items: center;
                    gap: 5px;

                    .wish-gift-panel-progress-item {
                        width: 40px;
                        height: 4px;
                        background-color: rgba($color: #fff, $alpha: .3);
                        border-radius: 2px;
                    }

                    .wish-gift-panel-progress-item-text {
                        font-size: 9px;
                        font-weight: 700;
                        line-height: 11px;
                        color: #fff;
                    }
                }

                .wish-gift-panel-tip {
                    position: absolute;
                    top: calc(100% + 10px);
                    left: 0;
                    max-width: 150px;
                    width: max-content;
                    padding: 7px 25px 7px 8px;
                    box-sizing: border-box;
                    border-radius: 6px;
                    background: rgba(246, 17, 112, .5);
                    display: flex;
                    gap: 2px;

                    &::before {
                        content: '';
                        position: absolute;
                        top: -6px;
                        left: 10px;
                        width: 0;
                        height: 0;
                        border-left: 6px solid transparent;
                        border-right: 6px solid transparent;
                        border-bottom: 6px solid rgba(246, 17, 112, .5);
                    }

                    .electric-light {
                        width: 12px;
                        height: 12px;
                    }

                    .electric-close {
                        position: absolute;
                        right: 5px;
                        top: 5px;
                        width: 8px;
                    }

                    .electric-text {
                        font-size: 10px;
                        font-weight: 600;
                        line-height: 12px;
                        color: #fff;
                    }
                }
            }
        }

        // 礼物动画容器
        .gift-animation-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 999; // 提高z-index确保在最上层
            overflow: hidden;
        }

        @keyframes slideInFromLeft {
            0% {
                transform: translateX(-100%);
                opacity: 0;
            }

            100% {
                transform: translateX(0);
                opacity: 1;
            }
        }

        // 礼物滑出动画
        .gift-slide-enter-active {
            transition: all 0.5s ease-out;
        }

        .gift-slide-leave-active {
            transition: all 0.3s ease-in;
        }

        .gift-slide-enter-from {
            transform: translateX(-100%);
            opacity: 0;
        }

        .gift-slide-enter-to {
            transform: translateX(0);
            opacity: 1;
        }

        .gift-slide-leave-from {
            transform: translateX(0);
            opacity: 1;
        }

        .gift-slide-leave-to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .live-flex-bottom {
        flex: 1;
        display: flex;
        gap: 13px;
        padding: 0 0 0 15px;
        overflow: hidden;
    }
}

// slide-up transition
.slide-up-enter-active,
.slide-up-leave-active {
    transition: all 0.5s ease-out;
}

.slide-up-enter-from {
    opacity: 0;
    transform: translateY(100%);
}

.slide-up-enter-to {
    opacity: 1;
    transform: translateY(0);
}

.slide-up-leave-from {
    opacity: 1;
    transform: translateY(0);
}

.slide-up-leave-to {
    opacity: 0;
    transform: translateY(100%);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.new-gift-countdown {
    position: fixed;
    top: calc(env(safe-area-inset-top) + 100px);
    right: 15px;
    width: 50px;
    height: 50px;
    background: url('@/assets/anchor/new-gift-bg.png') no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    align-items: flex-end;
    z-index: 10;

    .new-gift-countdown-time {
        width: 100%;
        padding: 2px;
        font-size: 10px;
        font-weight: 600;
        color: #FF4651;
        text-align: center;
        background-color: #FFDDDF;
        border-radius: 20px;
    }
}

.live-slide-content {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 5;

    .live-video {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
}
</style>
