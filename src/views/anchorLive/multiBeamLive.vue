<template>
    <!-- 多人直播间主容器 -->
    <div class="live-container" @click="showMulTip = false; showNotFullGiftTip = false">
        <!-- 垂直滑动的主Swiper，用于切换不同的直播间 -->
        <swiper :direction="'vertical'" :slides-per-view="1" :space-between="0" :mousewheel="true" :keyboard="true"
            :loop="false" :centered-slides="true" :initial-slide="1" @swiper="onSwiper" @slideChange="onSlideChange"
            class="live-swiper">
            <!-- 每个直播间的滑动页面 -->
            <swiper-slide v-for="(live, index) in visibleLiveList" :key="`${live.id}-${live.uniqueKey}`"
                class="live-slide">
                <!-- 直播间背景图 -->
                <div class="live-slide-bg"
                    :style="{ backgroundImage: live.coverUrl ? `url(${live.coverUrl})` : `url(${vcrbBg})` }">
                </div>
                <!-- 直播间内容，只有当前激活的slide才显示 -->
                <div class="live-item" v-if="activeSlideIndex === index">
                    <!-- 关闭直播间按钮 -->
                    <div class="close-live-room" @click="handleCloseLive">
                        <img src="@/assets/common/close-live.png" alt="关闭直播">
                    </div>
                    <!-- 内嵌swiper用于清屏功能，可以在正常直播和清屏模式之间切换 -->
                    <swiper v-bind="innerSwiperConfig" @swiper="(swiper) => onInnerSwiper(swiper, live.id)"
                        @slideChange="onInnerSlideChange" class="inner-live-swiper" :key="`inner-swiper-${live.id}`">
                        <!-- 正常直播页面 -->
                        <swiper-slide class="inner-live-slide">
                            <div class="live-content">
                                <!-- 直播间头部信息组件 -->
                                <LiveHeader :live="live" @follow="handleFollowAnchor"
                                    @show-live-ranking-popup="showLiveRankingPopup = true"
                                    @open-user-info-popup="changeUserInfoPopup" />
                                <!-- 多人直播礼物进度条组件 -->
                                <mulbeamGiftProgress :progress="progress" :show-tip="showNotFullGiftTip"
                                    :showMulTip="showMulTip" @toggle-tip="showNotFullGiftTip = !showNotFullGiftTip"
                                    @toggle-mul-tip="showMulTip = !showMulTip" @send-gift="handleSendGift" />

                                <!-- 直播间主体内容区域 -->
                                <div class="live-flex">
                                    <!-- 上半部分：视频面板和礼物动画 -->
                                    <div class="live-flex-top">
                                        <!-- 视频面板层，包含主持人和主播座位 -->
                                        <div class="video-panel-layer">
                                            <!-- 主持人和老板的位置 -->
                                            <div class="video-panel-layer-top">
                                                <!-- 主持人座位 -->
                                                <div class="video-panel-layer-top-admin"
                                                    @click="handleSelectAnchor(anchorInfo)">
                                                    <mulAnchorLiveSeat :anchorSeatDetail="anchorSeatList[0]" :index="0"
                                                        :isAdmin="true" />
                                                </div>
                                                <!-- 老板座位 -->
                                                <div class="video-panel-layer-top-boss" @click="changeShowBecomePopup">
                                                    <!-- 老板座位背景 -->
                                                    <div class="mul-boss-content"></div>
                                                    <img src="@/assets/live/mul-boss-bg.png" alt="" class="mul-boss-bg">
                                                    <img src="@/assets/live/boss-seat.png" alt="" class="mul-boss-seat">
                                                    <span class="mul-boss-join">Join</span>
                                                    <!-- 老板座位价格信息 -->
                                                    <div class="mul-boss-price">
                                                        <span>5</span>
                                                        <img src="@/assets/common/coins.png" alt="">
                                                        <span>/min</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- 主播座位区域，使用网格布局排列 -->
                                            <div class="video-panel-layer-bottom">
                                                <mulAnchorLiveSeat v-for="(item, index) in anchorSeatList"
                                                    :key="`anchor-seat-${index}`" :anchorSeatDetail="item"
                                                    :index="index" @goPrivateChat="goPrivateChat"
                                                    @handleSelectAnchor="handleSelectAnchor" />
                                            </div>
                                        </div>
                                        <!-- 礼物动画容器，覆盖在视频面板上方 -->
                                        <div class="gift-animation-container">
                                            <GiftAnimation :ref="`giftAnimationRef_${live.id}`" />
                                        </div>
                                    </div>
                                    <!-- 下半部分：聊天区域和活动组件 -->
                                    <div class="live-flex-bottom">
                                        <!-- 聊天内容组件 -->
                                        <liveChatBox :messageList="messageList" />
                                        <!-- 活动内容组件 -->
                                        <liveActiveComponents :showPrivateChat="false" />
                                    </div>
                                    <!-- 发送消息和礼物的底部工具栏 -->
                                    <mulibeamLivemessageBox v-model="messageText" :show-say-hi="showSayHi"
                                        :show-send-gift="showSendGift" @send="handleSendMessage"
                                        @show-tools="changeShowRoomTools" @show-coins="changeShowCoinsNotEnoughPopup"
                                        @show-become-boss="changeShowBecomePopup" />
                                </div>
                            </div>
                        </swiper-slide>
                        <!-- 清屏页面，点击可以返回正常模式 -->
                        <swiper-slide class="inner-live-slide">
                            <div class="live-clear-screen" @click="returnToNormal(live.id)">
                            </div>
                        </swiper-slide>
                    </swiper>
                    <!-- 发送礼物图标，点击打开礼物弹窗 -->
                    <div class="send-gift-icon" @click="changeShowChatGiftPopup">
                        <img src="@/assets/anchor/live-gift-icon.png" alt="发送礼物">
                    </div>
                </div>
            </swiper-slide>
        </swiper>
        <div id="canvas" ref="canvas" style="width: 100%; height: 100%;" v-if="showGiftAnimation"></div>
    </div>

    <!-- 各种弹窗组件 -->
    <LivePopups v-bind="popupStates" :anchor-info="anchorInfo" :user-info="userInfo" :live-room-type="liveRoomType"
        :isMultiBeamLive="true" :multiBeamLiveAnchorList="multiBeamLiveAnchorList" :defaultAnchor="defaultAnchor"
        @update:follow="showFollowAnchorPopup = $event" @update:report-anchor="showReportAnchorDialog = $event"
        @update:reminder="showReminderPopup = $event" @update:tools="showRoomTools = $event"
        @update:coins="showCoinsNotEnoughPopup = $event" @update:chat-gift="showChatGiftPopup = $event"
        @change-coins="changeShowCoinsNotEnoughPopup" @update:switch-live-room-tip="showSwitchLiveRoomTip = $event"
        @update:live-ranking="showLiveRankingPopup = $event" @update:user-info="showUserInfoPopup = $event"
        @update:user-info-anchor="userInfo.isAnchor = $event" @update:live-room-tip="showLiveRoomTip = $event"
        @update:honor-medals="showHonorMedalsDialog = $event" @update:gift-wall="showGiftWallDialog = $event"
        @update:level="showLevelPopup = $event" @handleSelectAdminCard="changeUserInfoPopup" @loadingGiftAnimation="loadingGiftAnimation" />

    <!-- 私聊弹窗组件 -->
    <GoPrivateChatPopup :show="showGoPrivatePopup" :anchor-info="selectAnchorSeatDetail"
        @update:show="showGoPrivatePopup = $event" />

    <!-- 成为老板提示弹窗 -->
    <BecomeBossTipPopup :show="showBecomeTipPopup" @update:show="showBecomeTipPopup = $event"
        @changeShowJoinAsBossPopup="changeShowJoinAsBossPopup" />

    <!-- 加入老板弹窗 -->
    <JoinAsBossPopup :show="showJoinAsBossPopup" @update:show="showJoinAsBossPopup = $event" />
</template>
<!--多人直播间-->
<script lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Mousewheel, Keyboard, Autoplay } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/mousewheel'
import 'swiper/css/keyboard'
import snackbar from '@/components/snackbar'
import LiveHeader from './components/LiveHeader.vue'
import mulbeamGiftProgress from './components/multibeamLiveComponents/mulbeamGiftProgress.vue'
import liveChatBox from './components/liveChatBox.vue'
import liveActiveComponents from './components/liveActiveComponents.vue'
import mulibeamLivemessageBox from './components/multibeamLiveComponents/mulibeamLivemessageBox.vue'
import LivePopups from './components/LivePopups.vue'
import GiftAnimation from '@/components/GiftAnimation.vue'
import mulAnchorLiveSeat from './components/multibeamLiveComponents/mulAnchorLiveSeat.vue'
import GoPrivateChatPopup from './components/multibeamLiveComponents/goPrivateChatPopup.vue'
import vcrbBg from '@/assets/live/vcrb-bg.png'
import BecomeBossTipPopup from './components/multibeamLiveComponents/becomeBossTipPopup.vue'
import JoinAsBossPopup from './components/multibeamLiveComponents/joinAsBossPopup.vue'
import SVGA from 'svgaplayerweb'
export default {
    name: 'AnchorLiveModule',
    components: {
        Swiper,
        SwiperSlide,
        LiveHeader,
        mulbeamGiftProgress,
        mulibeamLivemessageBox,
        LivePopups,
        liveChatBox,
        liveActiveComponents,
        GiftAnimation, // 礼物动画组件
        mulAnchorLiveSeat, // 主播座位组件
        GoPrivateChatPopup, // 私聊弹窗组件
        BecomeBossTipPopup, // 成为老板提示弹窗组件
        JoinAsBossPopup // 加入老板弹窗组件
    },

    data() {
        return {
            vcrbBg: vcrbBg, // 默认背景图
            showGiftAnimation: false,
            // 主播座位列表数据
            anchorSeatList: [
                {
                    id: 11,
                    name: '😘 Pinky是啥',
                    online: false,
                    awaitTime: 90,
                    isWaiting: true,
                    video: false,
                    voice: false,
                    isSpeaking: false,
                    avatar: 'https://d36zjp6dbikxo9.cloudfront.net/rekognition/d3adb2e526adfabdf94db8da2fe33a4020250508031201.jpg'
                },
                {
                    id: 12,
                    name: 'MurrMurr🖤',
                    online: true,
                    awaitTime: 120,
                    isWaiting: false,
                    video: true,
                    voice: false,
                    isSpeaking: false,
                    avatar: 'https://d36zjp6dbikxo9.cloudfront.net/rekognition/7efc4888da13f6c76fb4118563d072a720250726135623.jpg'
                },
                {
                    id: 13,
                    name: 'Sissy Trans🫦',
                    online: true,
                    awaitTime: 90,
                    isWaiting: true,
                    video: false,
                    voice: false,
                    isSpeaking: false,
                    avatar: 'https://d36zjp6dbikxo9.cloudfront.net/rekognition/d3adb2e526adfabdf94db8da2fe33a4020250508031201.jpg'
                },
                {

                },
                {

                },
                {

                }
            ],

            // 弹窗状态管理
            showBecomeTipPopup: false, // 成为老板提示弹窗
            selectAnchorSeatDetail: {}, // 选中的主播座位详情
            anchorInfo: { // 主持人信息
                isAdmin: true,
                id: 1,
                name: "Jane Cooper",
                avatar: "https://d36zjp6dbikxo9.cloudfront.net/rekognition/d3adb2e526adfabdf94db8da2fe33a4020250508031201.jpg",
                desc: "A gentleman is everything that is good. 😍",
                follow: false
            },
            defaultAnchor: {}, // 默认选中的主播
            userInfo: { // 用户信息
                isAnchor: false,
            },

            // 各种弹窗显示状态
            showGoPrivatePopup: false, // 私聊弹窗
            showJoinAsBossPopup: false, // 加入老板弹窗
            showMulTip: false, // 多人直播提示
            showLevelPopup: false, // 等级弹窗
            showReportAnchorDialog: false, // 举报弹窗
            showLiveRoomTip: false, // 切换直播间弹窗
            showHonorMedalsDialog: false, // 勋章弹窗
            showGiftWallDialog: false, // 礼物墙弹窗
            liveRoomType: 'leave', // 直播间类型
            showSwitchLiveRoomTip: false, // 怎么切换直播间弹窗提示
            showRoomTools: false, // 更多设置弹窗
            showFollowAnchorPopup: false, // 关注主播弹窗
            showCoinsNotEnoughPopup: false, // 金币不足弹窗
            showReminderPopup: false, // reminder 提示
            showChatGiftPopup: false, // 聊天礼物弹窗
            showLiveRankingPopup: false, // 直播榜排行榜弹窗
            showUserInfoPopup: false, // 用户信息弹窗

            // Swiper相关状态
            currentIndex: 1, // 永远在中间位置
            visibleLiveList: [] as any[], // 永远只有3个元素
            activeSlideIndex: 1, // 当前激活的slide索引
            innerSwiperInstances: {} as any, // 内层swiper实例
            innerActiveSlideIndex: 0, // 内层swiper当前激活的slide索引

            // 消息和礼物相关
            messageText: '', // 消息文本
            showSayHi: false, // 显示say-hi提示
            showSendGift: false, // 显示send-gift提示
            giftIdCounter: 0, // 礼物ID计数器
            swiperInstance: null as any, // 主swiper实例
            showNotFullGiftTip: false, // 礼物不足提示

            // 数据管理
            allLiveList: [] as any[], // 所有已加载的数据
            currentDataIndex: 0, // 当前在 allLiveList 中的位置
            progress: 200, // 礼物进度

            // 初始数据
            initialData: [
                {
                    id: 1,
                    anchorName: '11111',
                    videoUrl: 'https://www.douyin.com/aweme/v1/play/?video_id=v0300fg10000c4b2hqjc77uab1jgt7og&line=0&file_id=8d35ca35a26649548388d7910162d67a&sign=ed509bf1cbbb555b18e3d1354ab451d3&is_play_url=1&source=PackSourceEnum_PUBLISH',
                    coverUrl: ''
                },
                {
                    id: 2,
                    anchorName: '2222',
                    videoUrl: 'https://www.douyin.com/aweme/v1/play/?video_id=v0200f0f0000bm29e50858limi4ovvg0&line=0&file_id=1ca3018457374fd8bfe020dd98112b3f&sign=0a49e48c13dd281b66d0e233c673ef5f&is_play_url=1&source=PackSourceEnum_PUBLISH',
                    coverUrl: 'https://picsum.photos/1000/500?random=2'
                },
                {
                    id: 3,
                    videoUrl: 'https://www.douyin.com/aweme/v1/play/?video_id=v0300f780000bj7drdon0160phos9cqg&line=0&file_id=e7bf37657a4f4418a472245a8cbe5195&sign=1c1655d9f81c9dc53a3ce087fd46a742&is_play_url=1&source=PackSourceEnum_PUBLISH',
                    coverUrl: 'https://picsum.photos/1000/500?random=3'
                }
            ],

            // 加载状态管理
            hasMoreUp: true, // 是否还有更多向上数据
            hasMoreDown: true, // 是否还有更多向下数据
            isLoadingUp: false, // 是否正在向上加载
            isLoadingDown: false, // 是否正在向下加载
            newGiftCountdown: "00:00:00", // 新礼物倒计时

            // 数据ID管理
            nextId: 4, // 下一个新数据的ID
            prevId: 0,  // 上一个新数据的ID

            // 礼物动画数据
            giftAnimations: [] as any[],

            // 聊天消息列表
            messageList: [
                {
                    id: Math.random(),
                    userName: '11111',
                    message: '11111',
                    messageType: '1',
                    userType: 1,
                    vipType: 0,
                },
                {
                    id: Math.random(),
                    userName: '22222',
                    message: 'Just a test',
                    messageType: '1',
                    userType: 2,
                    vipType: 0,
                },
                {
                    id: Math.random(),
                    userName: '33333',
                    message: 'Join Room',
                    userType: 1,
                    messageType: '2',
                    vipType: 0,
                },
                {
                    id: Math.random(),
                    userName: '44sfafsdfasfa',
                    message: 'Join Room',
                    messageType: '2',
                    vipType: 1,
                    vipLevel: 1
                },
                {
                    id: Math.random(),
                    userName: '55555',
                    message: 'Join Room',
                    messageType: '2',
                    vipType: 2,
                    userType: 1,
                    vipLevel: 21
                },
                {
                    id: Math.random(),
                    userName: '66666',
                    message: 'Join Room',
                    messageType: '2',
                    vipType: 2,
                    userType: 1,
                    vipLevel: 12
                }
            ]
        }
    },

    mounted() {
        this.loadLiveData() // 使用新的数据加载方法

        // 延迟5秒后自动开始礼物演示
        setTimeout(() => {
            this.testGiftDemo()
            
        }, 5000)
    },

    methods: {
        async loadingGiftAnimation(svgUrl:string) {
            this.showGiftAnimation = true

            this.$nextTick(async () => {
                const player = new SVGA.Player(this.$refs.canvas as HTMLCanvasElement)
                const parser = new SVGA.Parser()
                parser.load(svgUrl, function (videoItem) {
                    player.loops = 1
                    player.clearsAfterStop = true
                    player.setVideoItem(videoItem);
                    player.startAnimation();
                },)
                console.log('parser',parser)
            })
          
        },
        /**
         * 切换提醒弹窗显示状态
         */
        changeShowReminderPopup() {
            this.showReminderPopup = !this.showReminderPopup
        },

        /**
         * 切换房间工具弹窗显示状态
         */
        changeShowRoomTools() {
            this.showRoomTools = !this.showRoomTools
        },

        /**
         * 切换金币不足弹窗显示状态
         */
        changeShowCoinsNotEnoughPopup() {
            this.showCoinsNotEnoughPopup = !this.showCoinsNotEnoughPopup
        },

        /**
         * 切换聊天礼物弹窗，并设置默认主持人
         */
        changeShowChatGiftPopup() {
            this.defaultAnchor = this.anchorInfo
            this.showChatGiftPopup = !this.showChatGiftPopup
        },

        /**
         * 切换用户信息弹窗显示状态
         */
        changeUserInfoPopup() {
            this.showUserInfoPopup = true
        },

        /**
         * 初始化直播数据
         * 将初始数据复制到 allLiveList 并设置初始位置
         */
        initLiveData() {
            // 初始化数据：将初始数据复制到 allLiveList
            this.allLiveList = [...this.initialData]
            this.currentDataIndex = 1 // 从中间开始
            this.activeSlideIndex = 1 // 确保activeSlideIndex也被初始化

            // 初始化可见列表（永远只有3个）
            this.updateVisibleLiveList()
            console.log('初始化直播数据，当前位置:', this.currentDataIndex)
        },

        /**
         * 更新可见的直播列表
         * 确保永远只显示3个slide，并处理边界情况
         */
        updateVisibleLiveList() {
            const totalLength = this.allLiveList.length
            if (totalLength === 0) return

            // 确保有足够的数据（至少3个）
            if (totalLength < 3) {
                console.warn('数据不足3个，无法正常显示')
                return
            }

            // 计算三个位置的数据索引
            let prevIndex = this.currentDataIndex - 1
            let currentIndex = this.currentDataIndex
            let nextIndex = this.currentDataIndex + 1

            // 边界处理
            if (prevIndex < 0) {
                prevIndex = 0
                // 如果到达顶部，尝试加载更多数据
                this.loadMoreLives('up')
            }
            if (nextIndex >= totalLength) {
                nextIndex = totalLength - 1
                // 如果到达底部，尝试加载更多数据
                this.loadMoreLives('down')
            }

            // 更新可见列表，添加唯一key确保Vue正确更新
            this.visibleLiveList = [
                { ...this.allLiveList[prevIndex], uniqueKey: `${this.allLiveList[prevIndex].id}-${Date.now()}-prev` },
                { ...this.allLiveList[currentIndex], uniqueKey: `${this.allLiveList[currentIndex].id}-${Date.now()}-current` },
                { ...this.allLiveList[nextIndex], uniqueKey: `${this.allLiveList[nextIndex].id}-${Date.now()}-next` }
            ]

            console.log('更新可见列表:', {
                prevIndex,
                currentIndex,
                nextIndex,
                currentDataIndex: this.currentDataIndex,
                totalLength
            })
            // 组件渲染完成后的回调
            this.$nextTick(() => {
                // 可以在这里添加需要在组件渲染完成后执行的代码
            })
        },

        /**
         * 主Swiper初始化完成回调
         * @param swiper Swiper实例
         */
        onSwiper(swiper: any) {
            console.log('Swiper初始化完成', swiper)
            this.swiperInstance = swiper
            // 确保activeSlideIndex被正确初始化
            this.activeSlideIndex = swiper.activeIndex || 1
        },

        /**
         * 主Swiper滑动切换回调
         * 处理上下滑动切换直播间的逻辑
         * @param swiper Swiper实例
         */
        onSlideChange(swiper: any) {
            const slideIndex = swiper.activeIndex
            console.log('Swiper切换到索引:', slideIndex)

            // 更新当前激活的slide索引
            this.activeSlideIndex = slideIndex

            // 根据滑动方向更新数据索引
            if (slideIndex === 0) {
                // 向上滑动到第一个slide
                this.currentDataIndex = Math.max(0, this.currentDataIndex - 1)
                console.log('向上滑动，数据索引:', this.currentDataIndex)
            } else if (slideIndex === 2) {
                // 向下滑动到第三个slide
                this.currentDataIndex = Math.min(this.allLiveList.length - 1, this.currentDataIndex + 1)
                console.log('向下滑动，数据索引:', this.currentDataIndex)
            }

            // 更新可见列表
            this.updateVisibleLiveList()

            // 重置swiper到中间位置（索引1）
            this.$nextTick(() => {
                if (this.swiperInstance && slideIndex !== 1) {
                    this.swiperInstance.slideTo(1, 0) // 0ms动画，瞬间切换
                    this.currentIndex = 1
                    // 重置activeSlideIndex到中间位置
                    this.activeSlideIndex = 1
                }
            })

            // 暂停其他视频，播放当前视频
            this.pauseAllVideos()
            this.playCurrentVideo()
        },

        /**
         * 内层Swiper初始化完成回调
         * 用于清屏功能的swiper
         * @param swiper Swiper实例
         * @param liveId 直播间ID
         */
        onInnerSwiper(swiper: any, liveId?: number) {
            console.log('Inner Swiper初始化完成', swiper)
            if (liveId) {
                this.innerSwiperInstances[liveId] = swiper
            }
        },

        /**
         * 内层Swiper滑动切换回调
         * @param swiper Swiper实例
         */
        onInnerSlideChange(swiper: any) {
            const slideIndex = swiper.activeIndex
            console.log('Inner Swiper切换到索引:', slideIndex)

            // 更新内嵌swiper的激活索引
            this.innerActiveSlideIndex = slideIndex
        },

        /**
         * 清屏功能方法
         * 切换到清屏页面
         * @param liveId 直播间ID
         */
        clearScreen(liveId?: number) {
            const swiper = liveId ? this.innerSwiperInstances[liveId] : null
            if (swiper) {
                swiper.slideTo(1, 300) // 滑动到清屏页面，300ms动画
                this.innerActiveSlideIndex = 1
            }
        },

        /**
         * 返回正常模式
         * 从清屏页面返回到正常直播页面
         * @param liveId 直播间ID
         */
        returnToNormal(liveId?: number) {
            const swiper = liveId ? this.innerSwiperInstances[liveId] : null
            if (swiper) {
                swiper.slideTo(0, 300) // 滑动到正常页面，300ms动画
                this.innerActiveSlideIndex = 0
            }
        },

        /**
         * 暂停所有视频播放
         */
        pauseAllVideos() {
            const videos = document.querySelectorAll('.live-video') as NodeListOf<HTMLVideoElement>
            videos.forEach(video => {
                video.pause()
            })
        },

        /**
         * 播放当前视频
         */
        playCurrentVideo() {
            const currentVideo = document.querySelector(`.live-slide:nth-child(${this.currentIndex + 1}) .live-video`) as HTMLVideoElement
            if (currentVideo) {
                currentVideo.play().catch(err => {
                    console.log('自动播放失败:', err)
                })
            }
        },

        /**
         * 视频加载完成回调
         * @param event 加载事件
         */
        onVideoLoaded(event: Event) {
            const video = event.target as HTMLVideoElement
            console.log('视频加载完成:', video.src)
        },

        /**
         * 视频加载失败回调
         * @param event 错误事件
         */
        onVideoError(event: Event) {
            const video = event.target as HTMLVideoElement
            console.error('视频加载失败:', video.src)
        },

        /**
         * 加载更多直播数据
         * 支持向上和向下两个方向的无限滚动加载
         * @param direction 加载方向：'up' | 'down'
         */
        loadMoreLives(direction: 'up' | 'down') {
            // 检查是否正在加载
            const isLoading = direction === 'up' ? this.isLoadingUp : this.isLoadingDown
            if (isLoading) {
                console.log(`${direction}方向正在加载中，跳过请求`)
                return
            }

            // 检查是否还有更多数据
            const hasMore = direction === 'up' ? this.hasMoreUp : this.hasMoreDown
            if (!hasMore) {
                console.log(`${direction}方向没有更多数据了`)
                return
            }

            // 检查是否需要加载（距离边界还有足够数据时不加载）
            if (direction === 'up' && this.currentDataIndex > 2) {
                return // 距离顶部还有足够数据
            }
            if (direction === 'down' && this.currentDataIndex < this.allLiveList.length - 3) {
                return // 距离底部还有足够数据
            }

            // 设置加载状态
            if (direction === 'up') {
                this.isLoadingUp = true
            } else {
                this.isLoadingDown = true
            }

            console.log(`开始${direction === 'down' ? '向下' : '向上'}加载数据`)

            // 模拟异步加载更多直播数据
            setTimeout(() => {
                const newLives = []
                const batchSize = 3 // 每次加载3个

                for (let i = 0; i < batchSize; i++) {
                    if (direction === 'down') {
                        newLives.push({
                            id: this.nextId++,
                            videoUrl: `https://example.com/live${this.nextId - 1}.mp4`,
                            coverUrl: ``
                        })
                    } else {
                        newLives.push({
                            id: this.prevId--,
                            videoUrl: `https://example.com/live${this.prevId + 1}.mp4`,
                            coverUrl: `https://picsum.photos/300/200?random=${Math.abs(this.prevId + 1)}`
                        })
                    }
                }

                if (direction === 'down') {
                    // 向下加载：添加到列表末尾
                    this.allLiveList.push(...newLives)

                    // 模拟数据耗尽（加载到ID 20就没了）
                    if (this.nextId > 20) {
                        this.hasMoreDown = false
                        console.log('向下数据已加载完毕')
                    }
                } else {
                    // 向上加载：添加到列表开头
                    this.allLiveList.unshift(...newLives.reverse())
                    // 更新当前数据索引（因为在前面插入了数据）
                    this.currentDataIndex += newLives.length

                    // 模拟数据耗尽（加载到ID -10就没了）
                    if (this.prevId < -10) {
                        this.hasMoreUp = false
                        console.log('向上数据已加载完毕')
                    }
                }

                // 重置加载状态
                if (direction === 'up') {
                    this.isLoadingUp = false
                } else {
                    this.isLoadingDown = false
                }

                console.log(`${direction === 'down' ? '向下' : '向上'}加载完成，新增: ${newLives.length} 个，总数据: ${this.allLiveList.length}`)

                // 更新可见列表
                this.updateVisibleLiveList()
            }, 1000) // 模拟1秒加载时间
        },

        /**
         * 关闭直播间
         */
        handleCloseLive() {
            // this.clearScreen(this.visibleLiveList[this.activeSlideIndex].id);
        },

        /**
         * 关注主播
         * @param liveId 直播间ID
         */
        handleFollowAnchor(liveId: number) {
            // 模拟关注逻辑
            const live = this.allLiveList.find(item => item.id === liveId);
            if (live) {
                live.follow = !live.follow;
                this.anchorInfo.follow = live.follow;
                this.showFollowAnchorPopup = true;
            }
        },

        /**
         * 发送礼物
         */
        handleSendGift() {
            // 模拟送礼物逻辑
            this.showNotFullGiftTip = false;
            this.showCoinsNotEnoughPopup = true;
        },

        /**
         * 发送消息
         */
        handleSendMessage() {
            if (this.messageText.trim()) {
                console.log('发送消息:', this.messageText);
                this.messageText = '';
                this.showReminderPopup = true;
            }
        },

        /**
         * 优化数据加载
         * 异步加载直播数据
         */
        async loadLiveData() {
            try {
                // 模拟API调用
                const response = await this.fetchLiveData();
                this.allLiveList = response as any[];
                this.updateVisibleLiveList();
            } catch (error) {
                console.error('加载直播数据失败:', error);
            }
        },

        /**
         * 模拟API调用
         * 获取直播数据
         * @returns Promise<any[]> 直播数据数组
         */
        fetchLiveData(): Promise<any[]> {
            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve([
                        {
                            id: 1,
                            anchorName: "Jane Cooper",
                            anchorAvatar: "https://picsum.photos/100/100?random=1",
                            viewCount: 1234,
                            onlineCount: 567,
                            onlineUsers: 10,
                            videoUrl: 'https://www.douyin.com/aweme/v1/play/?video_id=v0300fg10000c4b2hqjc77uab1jgt7og&line=0&file_id=8d35ca35a26649548388d7910162d67a&sign=ed509bf1cbbb555b18e3d1354ab451d3&is_play_url=1&source=PackSourceEnum_PUBLISH',
                            coverUrl: '',
                            follow: false
                        },
                        {
                            id: 2,
                            anchorName: "John Doe",
                            anchorAvatar: "https://picsum.photos/100/100?random=2",
                            viewCount: 2345,
                            onlineCount: 789,
                            onlineUsers: 15,
                            videoUrl: 'https://www.douyin.com/aweme/v1/play/?video_id=v0200f0f0000bm29e50858limi4ovvg0&line=0&file_id=1ca3018457374fd8bfe020dd98112b3f&sign=0a49e48c13dd281b66d0e233c673ef5f&is_play_url=1&source=PackSourceEnum_PUBLISH',
                            coverUrl: 'https://ilivegirl.s3.amazonaws.com/rekognition/36e80b024dd7a7b0522aae96e396ed6d20250801175620-medium.jpg',
                            follow: true
                        },
                        {
                            id: 3,
                            anchorName: "Alice Smith",
                            anchorAvatar: "https://picsum.photos/100/100?random=3",
                            viewCount: 3456,
                            onlineCount: 890,
                            onlineUsers: 20,
                            videoUrl: 'https://www.douyin.com/aweme/v1/play/?video_id=v0300f780000bj7drdon0160phos9cqg&line=0&file_id=e7bf37657a4f4418a472245a8cbe5195&sign=1c1655d9f81c9dc53a3ce087fd46a742&is_play_url=1&source=PackSourceEnum_PUBLISH',
                            coverUrl: 'https://ilivegirl.s3.amazonaws.com/rekognition/36e80b024dd7a7b0522aae96e396ed6d20250801175620-medium.jpg',
                            follow: false
                        }
                    ]);
                }, 500);
            });
        },

        /**
         * 添加礼物动画
         * 向当前激活的直播间添加礼物动画效果
         * @param userName 用户名
         * @param giftName 礼物名称
         * @param giftUrl 礼物图片URL
         * @param vipType VIP类型
         */
        addGiftAnimation(userName: string, giftName: string, giftUrl: string, vipType: number) {
            // 获取当前激活的live
            const currentLive = this.visibleLiveList[this.activeSlideIndex];
            if (!currentLive) {
                console.error('当前激活的live未找到');
                return;
            }

            // 使用动态ref名称获取礼物动画组件
            const refName = `giftAnimationRef_${currentLive.id}`;
            const giftAnimationRef = (this.$refs as any)[refName];

            // 如果ref是数组，取第一个元素
            let component = giftAnimationRef;
            if (Array.isArray(giftAnimationRef) && giftAnimationRef.length > 0) {
                component = giftAnimationRef[0];
            }

            if (component && typeof component.addGiftAnimation === 'function') {
                component.addGiftAnimation(userName, giftName, giftUrl, vipType);
            } else {
                console.error('GiftAnimation组件方法不可用');
            }
        },

        /**
         * 测试礼物动画 - 演示用
         * 自动添加多个礼物动画来测试效果
         */
        testGiftDemo() {
            const gifts = [
                { userName: '张三', giftName: '玫瑰花', giftUrl: 'https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1693288687Teddybear2.png', vipType: 1 },
                { userName: '李四', giftName: '钻石', giftUrl: 'https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1694772305rose.png', vipType: 2 },
                { userName: '王五', giftName: '跑车', giftUrl: 'https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1694772609Hi.png', vipType: 0 },
                { userName: '赵六', giftName: '别墅', giftUrl: 'https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/17301117351730110177B-Day_Cake.png', vipType: 2 }
            ];

            gifts.forEach((gift, index) => {
                setTimeout(() => {
                    this.addGiftAnimation(gift.userName, gift.giftName, gift.giftUrl, gift.vipType);
                }, index * 1000);
            });

            // 测试同人同礼物合并效果
            setTimeout(() => {
                this.addGiftAnimation('张三', '玫瑰花', 'https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1693288687Teddybear2.png', 1);
            }, 5000);

            setTimeout(() => {
                this.addGiftAnimation('张三', '玫瑰花', 'https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1693288687Teddybear2.png', 1);
            }, 6000);
        },

        /**
         * 进入私聊
         * @param anchorSeatDetail 主播座位详情
         */
        goPrivateChat(anchorSeatDetail: any) {
            console.log('goPrivateChat', anchorSeatDetail)
            this.selectAnchorSeatDetail = anchorSeatDetail
            this.showGoPrivatePopup = true
        },

        /**
         * 显示成为老板提示弹窗
         * 只在第一次显示，后续不再显示
         */
        changeShowBecomePopup() {
            if (!localStorage.getItem('isFirstBoss')) {
                this.showBecomeTipPopup = true
                localStorage.setItem('isFirstBoss', 'true')
            } else {
                this.showJoinAsBossPopup = true
            }
        },
        changeShowJoinAsBossPopup() {
            this.showJoinAsBossPopup = true
        },
        /**
         * 选择主播
         * 设置默认主播并打开聊天礼物弹窗
         * @param anchorInfo 主播信息
         */
        handleSelectAnchor(anchorInfo: any) {
            this.defaultAnchor = anchorInfo
            this.showChatGiftPopup = true
        }
    },

    computed: {
        /**
         * 计算多人直播主播列表
         * 包含主持人和所有主播座位
         */
        multiBeamLiveAnchorList() {
            return [this.anchorInfo, ...this.anchorSeatList.filter((item: any) => item.name)]
        },

        /**
         * 内层Swiper配置
         * 用于清屏功能的水平滑动配置
         */
        innerSwiperConfig() {
            return {
                direction: 'horizontal' as const,
                slidesPerView: 1,
                spaceBetween: 0,
                loop: false,
                centeredSlides: true,
                initialSlide: 0,
                allowTouchMove: true,
                touchRatio: 1,
                touchAngle: 45
            };
        },

        /**
         * 弹窗状态集合
         * 将所有弹窗状态整合到一个对象中，方便传递给LivePopups组件
         */
        popupStates() {
            return {
                showFollowAnchorPopup: this.showFollowAnchorPopup,
                showReminderPopup: this.showReminderPopup,
                showRoomTools: this.showRoomTools,
                showCoinsNotEnoughPopup: this.showCoinsNotEnoughPopup,
                showChatGiftPopup: this.showChatGiftPopup,
                liveRoomType: this.liveRoomType,
                anchorInfo: this.anchorInfo,
                userInfo: this.userInfo,
                showSwitchLiveRoomTip: this.showSwitchLiveRoomTip,
                showLiveRankingPopup: this.showLiveRankingPopup,
                showUserInfoPopup: this.showUserInfoPopup,
                showLiveRoomTip: this.showLiveRoomTip,
                showHonorMedalsDialog: this.showHonorMedalsDialog,
                showGiftWallDialog: this.showGiftWallDialog,
                showReportAnchorDialog: this.showReportAnchorDialog,
                showLevelPopup: this.showLevelPopup,
            };
        }
    }
}
</script>

<style lang="scss" scoped>
/* 多人直播间主容器样式 */
.live-container {
    width: 100%;
    height: 100vh;
    position: relative;
    background: #000;
    overflow: hidden;
    font-family: var(--font-family-urbanist);
    #canvas {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 9999;
    }
}

/* 主Swiper容器样式 */
.live-swiper {
    width: 100%;
    height: 100%;
}

/* 单个直播间滑动页面样式 */
.live-slide {
    width: 100%;
    height: 100vh;
    position: relative;

    /* 直播间背景图样式 */
    .live-slide-bg {
        z-index: 1;
        position: absolute;
        top: 0;
        left: 0;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        background-attachment: fixed;
        width: 100%;
        height: 100%;
        transition: transform 0.3s ease;

        /* 悬停时背景图放大效果 */
        &:hover {
            transform: scale(1.05);
        }
    }
}

/* 直播间内容区域样式 */
.live-item {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
}

/* 关闭直播间按钮样式 - 相对于live-item定位 */
.close-live-room {
    position: absolute;
    width: 24px;
    height: 24px;
    right: 15px;
    top: calc(env(safe-area-inset-top) + 20px);
    z-index: 9999;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;

    img {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
    }
}

/* 发送礼物图标样式 */
.send-gift-icon {
    position: absolute;
    width: 32px;
    height: 32px;
    right: 15px;
    bottom: calc(env(safe-area-inset-bottom) + 17px);
    z-index: 9999;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;

    /* 悬停效果 */
    &:hover {
        background: rgba(0, 0, 0, 0.6);
        transform: scale(1.1);
    }

    img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        box-sizing: border-box;
    }
}

/* 内嵌swiper样式 - 用于清屏功能 */
.inner-live-swiper {
    width: 100%;
    height: 100%;
}

.inner-live-slide {
    width: 100%;
    height: 100%;
    position: relative;
}

/* 直播间内容区域样式 */
.live-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .live-mul-tip {
        display: flex;
    }
}

/* 清屏模式样式 */
.live-clear-screen {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    z-index: 10;
    animation: fadeIn 0.3s ease-in-out;
}

/* 直播间主体布局区域 */
.live-flex {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 0 0 calc(env(safe-area-inset-bottom) + 17px);
    box-sizing: border-box;
    overflow: hidden;

    /* 上半部分：视频面板和礼物动画 */
    .live-flex-top {
        box-sizing: border-box;
        position: relative;
        z-index: 5;

        /* 视频面板层样式 */
        .video-panel-layer {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;

            /* 主持人和老板位置区域 */
            .video-panel-layer-top {
                height: 120px;
                margin: 8px 0 4px 0;
                display: flex;
                justify-content: center;
                gap: 35px;

                /* 主持人座位样式 */
                .video-panel-layer-top-admin {
                    margin-top: 10px;
                    width: 100px;
                    height: 100px;
                    background-color: blue;
                }

                /* 老板座位样式 */
                .video-panel-layer-top-boss {
                    width: 120px;
                    height: 120px;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;

                    /* 老板座位背景图 */
                    .mul-boss-bg {
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        top: 0;
                        left: 0;
                    }

                    /* 老板座位内容背景 */
                    .mul-boss-content {
                        width: 100px;
                        height: 100px;
                        background: linear-gradient(180deg, #A50C33 0%, #5D1847 100%);
                        position: absolute;
                        z-index: 0;
                    }

                    /* 老板座位图标 */
                    .mul-boss-seat {
                        width: 24px;
                        height: 24px;
                        position: relative;
                        z-index: 1;
                    }

                    /* 加入老板按钮文字 */
                    .mul-boss-join {
                        margin-top: 5px;
                        font-size: 12px;
                        color: #fff;
                        font-weight: 600;
                        line-height: 14px;
                        position: relative;
                        z-index: 1;
                    }

                    /* 老板座位价格信息 */
                    .mul-boss-price {
                        margin-top: 2px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 2px;
                        position: relative;
                        z-index: 1;

                        span {
                            font-size: 12px;
                            color: #fff;
                            font-weight: 600;
                            line-height: 14px;
                        }

                        img {
                            width: 12px;
                            height: 12px;
                        }
                    }
                }
            }

            /* 主播座位区域样式 - 使用网格布局 */
            .video-panel-layer-bottom {
                flex: 1;
                display: grid;
                grid-template-columns: repeat(3, 1fr);
            }
        }

        /* 礼物动画容器样式 */
        .gift-animation-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 999; // 提高z-index确保在最上层
            overflow: hidden;
        }

        /* 礼物滑入动画关键帧 */
        @keyframes slideInFromLeft {
            0% {
                transform: translateX(-100%);
                opacity: 0;
            }

            100% {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 礼物滑出动画过渡效果 */
        .gift-slide-enter-active {
            transition: all 0.5s ease-out;
        }

        .gift-slide-leave-active {
            transition: all 0.3s ease-in;
        }

        .gift-slide-enter-from {
            transform: translateX(-100%);
            opacity: 0;
        }

        .gift-slide-enter-to {
            transform: translateX(0);
            opacity: 1;
        }

        .gift-slide-leave-from {
            transform: translateX(0);
            opacity: 1;
        }

        .gift-slide-leave-to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    /* 下半部分：聊天区域和活动组件 */
    .live-flex-bottom {
        flex: 1;
        display: flex;
        gap: 13px;
        padding: 0 0 0 15px;
        overflow: hidden;
    }
}

/* 向上滑动过渡动画 */
.slide-up-enter-active,
.slide-up-leave-active {
    transition: all 0.5s ease-out;
}

.slide-up-enter-from {
    opacity: 0;
    transform: translateY(100%);
}

.slide-up-enter-to {
    opacity: 1;
    transform: translateY(0);
}

.slide-up-leave-from {
    opacity: 1;
    transform: translateY(0);
}

.slide-up-leave-to {
    opacity: 0;
    transform: translateY(100%);
}

/* 淡入动画关键帧 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 新礼物倒计时样式 */
.new-gift-countdown {
    position: fixed;
    top: calc(env(safe-area-inset-top) + 100px);
    right: 15px;
    width: 50px;
    height: 50px;
    background: url('@/assets/anchor/new-gift-bg.png') no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    align-items: flex-end;
    z-index: 10;

    /* 倒计时时间显示 */
    .new-gift-countdown-time {
        width: 100%;
        padding: 2px;
        font-size: 10px;
        font-weight: 600;
        color: #FF4651;
        text-align: center;
        background-color: #FFDDDF;
        border-radius: 20px;
    }
}
</style>
