<!--  -->
<template>
    <div>
        <var-popup position="bottom" v-model:show="showRoomTools" @click-overlay="close" :safe-area="true"
            :default-style="false" :overlay-style="{
                opacity: 0,
            }">
            <div class="room-tools-popup">
                <div class="room-tools-popup-content">
                    <div class="room-tools-popup-content-title">
                        Room tools
                    </div>
                    <div class="room-tools-popup-content-list">
                        <div class="room-tools-popup-content-list-item">
                            <img src="@/assets/anchor/recharge.png" alt="">
                            <span>Recharge</span>
                        </div>
                        <div class="room-tools-popup-content-list-item">
                            <img src="@/assets/anchor/broadcast.png" alt="">
                            <span>Broadcast</span>
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="RoomToolsPopup">
import { ref, watch, onMounted, nextTick } from 'vue'
const props = defineProps<Props>()
interface Props {
    show: boolean,
    anchorInfo: any
}

const emit = defineEmits(['update:show'])

const showRoomTools = ref(false)


watch(() => showRoomTools.value, (newVal: boolean) => {
    emit('update:show', newVal)
})


watch(() => props.show, (newVal: boolean) => {
    showRoomTools.value = newVal
}, {
    immediate: true
})

/**
 * Close the popup.
 */
const close = () => {
    showRoomTools.value = false
}

</script>
<style lang="scss" scoped>
.room-tools-popup {
    width: 100%;
    overflow: hidden;
    font-family: var(--font-family-urbanist);

    .room-tools-popup-content {
        width: 100%;
        padding: 20px 15px 25px;
        box-sizing: border-box;
        background-color: #0A0A1B;
        position: relative;
        border-radius: 16px 16px 0 0;
        display: flex;
        flex-direction: column;

        .room-tools-popup-content-title {
            font-size: 18px;
            font-weight: 700;
            line-height: 22px;
            color: #fff;
        }

        .room-tools-popup-content-list {
            margin-top: 20px;
            display: flex;
            gap: 18px;

            .room-tools-popup-content-list-item {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                gap: 6px;
               

                img {
                    width: 30px;
                    height: 30px;
                }
                span{
                    font-size: 12px;
                    font-weight: 600;
                    line-height: 14px;
                    color: rgba($color: #FFFFFF, $alpha: 0.5);
                }
            }
        }
    }
}
</style>