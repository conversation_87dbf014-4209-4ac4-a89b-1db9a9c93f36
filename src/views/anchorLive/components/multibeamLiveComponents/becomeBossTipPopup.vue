<!--  -->
<template>
    <var-popup position="center" v-model:show="showBecomeTipPopup" :default-style="false" @click-overlay="closeBecomeTipPopup">
        <div class="become-boss-tip-popup">
            <div class="become-boss-tip-popup-content">
                <img src="@/assets/live/first-boss-layer-en.bg.png" alt="" class="become-boss-tip-popup-content-bg">
                <div class="become-boss-tip-popup-content-btn">
                    <div class="become-boss-tip-popup-content-btn-btn"  v-ripple @click="changeShowJoinAsBossPopup">
                        OK
                    </div>
                </div>
            </div>
        </div>

    </var-popup>
</template>

<script lang="ts" setup name="BecomeBossTipPopup">
import { ref, watch } from 'vue'

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['update:show','changeShowJoinAsBossPopup'])

const showBecomeTipPopup = ref(props.show)

// 监听 props.show 的变化
watch(() => props.show, (newVal) => {
    showBecomeTipPopup.value = newVal
})

// 监听内部状态变化，向父组件发送事件
watch(showBecomeTipPopup, (newVal) => {
    emit('update:show', newVal)
})

const closeBecomeTipPopup = () => {
    showBecomeTipPopup.value = false
}
const changeShowJoinAsBossPopup = () => {
    emit('update:show', false)
    emit('changeShowJoinAsBossPopup')
}

</script>
<style lang="scss" scoped>
.become-boss-tip-popup {
    width: 100svw;
    font-family: var(--font-family-urbanist);
    box-sizing: border-box;
    padding: 0 48px;
    .become-boss-tip-popup-content{
        width: 100%;
        .become-boss-tip-popup-content-bg{
            width: 100%;
        }
        .become-boss-tip-popup-content-btn{
            width: 100%;
            padding:0 10px;
            box-sizing: border-box;
            background: #000;
            border-radius: 10px;
            .become-boss-tip-popup-content-btn-btn{
                margin-top: 30px;
                width: 100%;
                border-radius: 30px;
                height: 50px;
                line-height: 50px;
                font-size: 16px;
                font-weight:700;
                text-align: center;
                color: #fff;
                background:linear-gradient(90deg, #6E23FF 0%, #27A9FF 100%);
            }
        }
    }
}
</style>