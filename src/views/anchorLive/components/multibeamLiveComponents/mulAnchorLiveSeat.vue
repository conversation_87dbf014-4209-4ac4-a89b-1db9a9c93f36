<!--  -->
<template>
    <div class="anchor-seat" ref="anchorSeatRef"
        :style="{ backgroundImage: anchorSeatDetail.name ? `url(${anchorSeatDetail.avatar})` : '' }"
        :class="{ 'video-offline': anchorSeatDetail.name && !anchorSeatDetail.video && anchorSeatDetail.online,
            'is-admin':isAdmin
         }" @click="handleSelectAnchor">
        <div class="no-anchor" v-if="!anchorSeatDetail.name">
            <span class="no-anchor-num">{{ index + 1 }}</span>
            <span class="no-anchor-tip">Host on the way</span>
        </div>
        <div class="anchor-seat-video-offline-mask"
            v-if="anchorSeatDetail.name && !anchorSeatDetail.video && anchorSeatDetail.online">
            <img :src="anchorSeatDetail.avatar" alt="" class="avatar-img">
        </div>
        <div class="await-time" v-if="anchorSeatDetail.name && anchorSeatDetail.awaitTime&&!isAdmin" @click.stop="goPrivateChat"
            :class="{ 'await-time-offline': !anchorSeatDetail.online }">
            <img src="@/assets/common/wait-call.png" alt="" v-if="anchorSeatDetail.online">
            <span class="await-time-num">{{ awaitTime }}s</span>
        </div>
        <div class="anchor-seat-admin" v-if="isAdmin">
            Admin
        </div>
        <!--主播信息-->
        <div class="anchor-seat-top">
            <div class="anchor-seat-top-name">
                {{ anchorSeatDetail.name }}
            </div>
            <div class="anchor-seat-top-status" v-if="anchorSeatDetail.name">
                <img src="@/assets/common/mac-off.png" alt="" class="mac-off status-img" v-if="!anchorSeatDetail.voice">
                <img src="@/assets/common/video-off.png" alt="" class="video-off status-img"
                    v-if="!anchorSeatDetail.video">
                <img src="@/assets/common/mac-on.png" alt="" class="mac-on status-img" v-if="anchorSeatDetail.voice">
                <img src="@/assets/common/not-allow-call.png" alt="" class="not-allow-call status-img"
                    v-if="anchorSeatDetail.canCall">
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup name="mulAnchorLiveSeat">
import { ref, watch } from 'vue'
const props = defineProps<{
    anchorSeatDetail: any,
    index: number,
    isAdmin?: boolean
}>()
const emit = defineEmits(['goPrivateChat','handleSelectAnchor'])
const timer = ref<any>(null)
const anchorSeatRef = ref<HTMLElement>()

const awaitTime = ref(props.anchorSeatDetail.awaitTime)



const countDown = () => {
    timer.value = setInterval(() => {
        awaitTime.value--
        if (awaitTime.value <= 0) {
            clearInterval(timer.value)
        }
    }, 1000)
}

watch(() => props.anchorSeatDetail, (newVal) => {
    awaitTime.value = newVal.awaitTime
    countDown()
}, { immediate: true, deep: true })

const goPrivateChat = () => {
    emit('goPrivateChat', props.anchorSeatDetail)
}

const handleSelectAnchor = () => {
    
    emit('handleSelectAnchor', props.anchorSeatDetail)
}
</script>
<style lang="scss" scoped>
.anchor-seat {
    width: 100%;
    height: 130px;
    position: relative;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    &.is-admin{
        height: 100px;
    }
    &.video-offline {
        .anchor-seat-video-offline-mask {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;

            .avatar-img {
                width: 44px;
                height: 44px;
                border-radius: 50%;
                object-fit: cover;
                object-position: top;
            }
        }

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #00000080;
            backdrop-filter: blur(15px);
            z-index: 0;
        }
    }

    .no-anchor {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 8px;
        position: absolute;
        top: 0;
        left: 0;
        background-color: #00000033;
        .no-anchor-num {
            font-size: 40px;
            color: #fff;
            font-weight: 700;
            line-height: 48px;
        }

        .no-anchor-tip {
            font-size: 15px;
            color: #fff;
            font-weight: 700;
            line-height: 18px;
        }
    }

    .anchor-seat-top {
        position: absolute;
        top: 4px;
        left: 0;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 4px;
        box-sizing: border-box;
        gap: 10px;

        .anchor-seat-top-name {
            flex: 1;
            font-size: 14px;
            color: #fff;
            font-weight: 500;
            line-height: 17px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .anchor-seat-top-status {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            gap: 4px;

            .status-img {
                width: 16px;
                height: 16px;
            }
        }
    }

    .anchor-seat-admin {
        position: absolute;
        bottom: 2px;
        right: 2px;
        padding: 2px 4px;
        background-color: #0000004D;
        border-radius: 4px;
        font-size: 10px;
        font-weight: 700;
        line-height: 12px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .await-time {
        position: absolute;
        width: 50px;
        bottom: 10px;
        left: 0;
        right: 0;
        margin: auto;
        // width: max-content;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 4px 0;
        box-sizing: border-box;
        background: #7120FF;
        border-radius: 30px;
        gap: 2px;

        &.await-time-offline {
            padding: 5px 0;
            background: #00000080;
        }

        img {
            width: 12px;
            height: 12px;
        }

        span {
            font-size: 12px;
            color: #fff;
            font-weight: 600;
            line-height: 14px;

        }
    }
}
</style>