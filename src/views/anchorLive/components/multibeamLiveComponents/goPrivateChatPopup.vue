<!-- 私聊弹窗组件 -->
<template>
    <div>
        <var-popup 
            position="bottom" 
            v-model:show="showGoPrivatePopup" 
            @click-overlay="close" 
            :safe-area="true"
            :default-style="false"
        >
            <div class="go-private">
                <div class="go-private-content">
                    <div class="go-private-content-box-text">
                        Go private with her and she will be all yours
                    </div>
                    
                    <div class="go-private-content-box">
                        <img :src="anchorInfo.avatar" class="avatar-img" />
                        <img src="@/assets/live/star-icon.png" alt="" class="star-icon">
                    </div>
                    
                    <div class="anchor-name">
                        {{ anchorInfo.name }}
                    </div>
                    
                    <div class="go-private-content-btn">
                        <button type="button" class="go-private-content-btn-btn" @click="takeHer">
                            <span>Take her</span>
                        </button>
                    </div>
                    
                    <div class="go-private-content-tip">
                        First 3 mins 20 coins, origin call price afterwards
                    </div>
                </div>
            </div>
        </var-popup>
        
        <!-- 无媒体权限提示弹窗 -->
        <NoMediaPermissonPopup 
            :show="showNoMediaPermissionPopup" 
            @update:show="showNoMediaPermissionPopup = $event" 
        />
    </div>
</template>

<script lang="ts" setup name="GoPrivateChatPopup">
import { ref, watch } from 'vue'
import LazyImage from '@/components/LazyImage.vue'
import NoMediaPermissonPopup from '@/components/noMediaPermisson/noMediaPermissonPopup.vue'

// Props 定义
const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    anchorInfo: {
        type: Object,
        default: () => ({})
    }
})

// Emits 定义
const emit = defineEmits(['update:show'])

// 响应式数据
const showGoPrivatePopup = ref(false)
const showNoMediaPermissionPopup = ref(false)

// 监听弹窗显示状态变化
watch(() => showGoPrivatePopup.value, (newVal: boolean) => {
    emit('update:show', newVal)
})

// 监听外部传入的显示状态
watch(() => props.show, (newVal: boolean) => {
    showGoPrivatePopup.value = newVal
}, {
    immediate: true
})

/**
 * 关闭弹窗
 */
const close = () => {
    showGoPrivatePopup.value = false
}

/**
 * 点击 Take her 按钮，检查媒体权限
 */
const takeHer = async () => {
    try {
        // 检查麦克风权限
        const audioStream = await navigator.mediaDevices.getUserMedia({ audio: true })
        audioStream.getTracks().forEach(track => track.stop()) // 立即停止音频流
        
        // 检查摄像头权限
        const videoStream = await navigator.mediaDevices.getUserMedia({ video: true })
        videoStream.getTracks().forEach(track => track.stop()) // 立即停止视频流
        
        // 权限检查通过，可以继续执行
        console.log('麦克风和摄像头权限检查通过')
        
        // 这里可以添加你的业务逻辑
        // 比如跳转到私聊页面或发送请求等
        
    } catch (error: any) {
        console.error('权限检查失败:', error)
        
        // 根据错误类型给出不同的提示
        if (error.name === 'NotAllowedError') {
            showNoMediaPermissionPopup.value = true
        } else if (error.name === 'NotFoundError') {
            console.warn('未找到麦克风或摄像头设备')
        } else if (error.name === 'NotReadableError') {
            console.warn('麦克风或摄像头被其他应用占用')
        } else {
            console.warn('权限检查失败，请检查设备设置')
        }
    }
}
</script>

<style lang="scss" scoped>
.go-private {
    width: 100svw;
    font-family: var(--font-family-urbanist);
    box-sizing: border-box;

    .go-private-content {
        width: 100%;
        padding: 20px 0 25px;
        box-sizing: border-box;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: url('@/assets/live/mul-private-bg.png') no-repeat center center;
        background-size: 100% 100%;
        
        .go-private-content-box-text {
            padding: 0 58px;
            box-sizing: border-box;
            font-size: 18px;
            font-weight: 700;
            line-height: 22px;
            color: #fff;
            text-align: center;
        }
        
        .go-private-content-box {
            margin-top: 13px;
            width: 187px;
            height: 112px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            
            &::before {
                content: '';
                position: absolute;
                z-index: 1;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: url('@/assets/live/surround-anchor.png') no-repeat;
                background-size: contain;
            }   
            
            .avatar-img {
                width: 100px;
                height: 100px;
                border-radius: 50%;
                border: 2px solid #fff;
                object-fit: cover;
                object-position: top;
                box-sizing: border-box;
            }
            
            .star-icon {
                position: absolute;
                width: 40px;
                height: 40px;
                top: -10px;
                right: -5px;
                z-index: 3;
            }
        }
        
        .anchor-name {
            margin-top: 10px;
            font-size: 16px;
            font-weight: 700;
            line-height: 19px;
            color: #fff;
        }
        
        .go-private-content-btn {
            margin-top: 30px;
            width: 100%;
            display: flex;
            padding: 0 50px;
            box-sizing: border-box;
            align-items: center;
            justify-content: center;
            
            .go-private-content-btn-btn {
                width: 100%;
                height: 50px;
                background: #FF0B79;
                border-radius: 30px;
                border: none;
                font-size: 16px;
                font-weight: 700;
                color: #fff;
                outline: none;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                transition: all 0.3s ease;
                
                &:hover {
                    background: #e00a6b;
                    transform: translateY(-2px);
                }
                
                &:active {
                    transform: translateY(0);
                }
            }
        }
        
        .go-private-content-tip {
            margin-top: 12px;
            font-size: 14px;
            font-weight: 400;
            line-height: 17px;
            color: $common-color;
            text-align: center;
        }
    }
}
</style>