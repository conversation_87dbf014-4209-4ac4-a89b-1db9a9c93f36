<!-- 私聊弹窗组件 -->
<template>
    <div>
        <var-popup position="bottom" v-model:show="showJoinAsBossPopup" @click-overlay="close" :safe-area="true"
            :default-style="false">
            <div class="join-as-boss">
                <div class="join-as-boss-content">
                    <div class="join-as-boss-content-title">
                        <span>Take the mic and become the party boss! <br />You can:</span>
                    </div>
                    <div class="join-as-boss-content-list">
                        <div class="join-as-boss-content-list-item" v-for="(item, index) in list" :key="index">

                            <img src="@/assets/live/boss-layer-item-marker.png" alt=""
                                class="join-as-boss-content-list-item-icon">


                            <span class="join-as-boss-content-list-item-text">{{ item.text }}</span>
                        </div>
                    </div>
                    <img src="@/assets/live/join-as-boss-line.png" alt="" class="join-as-boss-content-line">
                    <div class="join-as-boss-content-media">
                        <div class="join-as-boss-content-media-item">
                            <div class="media-item-left">
                                <img src="@/assets/live/microphone.png" alt="">
                                <span>Microphone</span>
                            </div>
                            <div class="media-item-right">
                                <van-switch v-model="microphone" active-color="#7E380D" inactive-color="#F3E9D8"
                                    size="26" />
                            </div>
                        </div>
                        <div class="join-as-boss-content-media-item">
                            <div class="media-item-left">
                                <img src="@/assets/live/microphone.png" alt="">
                                <span>Camera</span>
                            </div>
                            <div class="media-item-right">
                                <van-switch v-model="camera" active-color="#7E380D" inactive-color="#F3E9D8"
                                    size="26" />
                            </div>
                        </div>
                    </div>
                    <!--下面的操作按钮-->
                    <div class="join-as-boss-content-btn-group">
                        <div class="cancel">
                            Cancel
                        </div>
                        <div class="join">
                            <span>Join</span>
                            <span class="price">5</span>
                            <img src="@/assets/common/coins.png" alt="">
                            <span>/min</span>
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="JoinAsBossPopup">
import { ref, watch } from 'vue'

// Props 定义
const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
})
const microphone = ref(true)
const camera = ref(false)
const list = ref([
    {

        text: 'Stand out with premium display'
    },
    {
        text: 'Play any music/game you like'
    },
    {
        text: 'Chat and enjoy with all hosts'
    }
])
// Emits 定义
const emit = defineEmits(['update:show'])

// 响应式数据
const showJoinAsBossPopup = ref(false)

// 监听弹窗显示状态变化
watch(() => showJoinAsBossPopup.value, (newVal: boolean) => {
    emit('update:show', newVal)
})

// 监听外部传入的显示状态
watch(() => props.show, (newVal: boolean) => {
    showJoinAsBossPopup.value = newVal
}, {
    immediate: true
})

/**
 * 关闭弹窗
 */
const close = () => {
    showJoinAsBossPopup.value = false
}

</script>

<style lang="scss" scoped>
.join-as-boss {
    width: 100svw;
    font-family: var(--font-family-urbanist);
    box-sizing: border-box;

    .join-as-boss-content {
        width: 100%;
        padding: 58px 20px 40px;
        box-sizing: border-box;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: url('@/assets/live/join-as-boss.png') no-repeat;
        background-size: 100% 100%;

        .join-as-boss-content-title {
            font-size: 16px;
            font-weight: 700;
            color: #7E380D;
            line-height: 19px;
        }

        .join-as-boss-content-list {
            margin-top: 10px;
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 8px;

            .join-as-boss-content-list-item {
                display: flex;
                align-items: center;
                padding: 9px 0 9px 10px;
                background: linear-gradient(90deg, #F0B96E 0%, rgba(240, 185, 110, 0) 100%);
                border-radius: 8px;
                gap: 8px;

                &-icon {
                    width: 24px;
                    height: 18px;
                }

                &-text {
                    font-size: 14px;
                    color: #9A5324;
                    font-size: 15px;
                    font-weight: 600;
                }
            }
        }

        .join-as-boss-content-line {
            margin-top: 10px;
            width: 100%;
        }

        .join-as-boss-content-media {
            margin-top: 20px;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;

            .join-as-boss-content-media-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                padding: 0 10px;

                .media-item-left {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    img {
                        width: 30px;
                        height: 30px;
                    }

                    span {

                        color: #7E380D;
                        font-size: 16px;
                        font-weight: 700;
                    }
                }

                :deep(.van-switch) {
                    width: 54px;
                    height: 28px;

                    .van-switch__node {
                        top: 1px;
                    }

                    &.van-switch--on {
                        .van-switch__node {
                            transform: translate(calc(var(--van-switch-width) - var(--van-switch-node-size)));
                        }
                    }
                }
            }
        }

        .join-as-boss-content-btn-group {
            margin-top: 38px;
            width: 100%;
            display: flex;
            align-items: center;
            gap: 10px;

            .cancel,
            .join {
                height: 50px;
                border-radius: 30px;
                display: flex;
                align-items: center;
                justify-content: center; 
                line-height: 50px;
            }
            .cancel{
                background: #FEF6E5;
                border: 1px solid #F9D6A0;
                color: #7E380D;
                font-size: 16px;
                padding: 0 42px;
                box-sizing: border-box;
                font-weight: 700;
            }
            .join{
                flex:1;
                background: linear-gradient(180deg, #F9D39A 0%, #F1BB70 100%);
                span{
                    font-size: 16px;
                    font-weight: 500;
                    color: #7E380D;
                }
                .price{
                    margin-left: 5px;
                    margin-right: 2px;
                    font-size: 24px;
                    font-weight: 700;
                    color: #7E380D;
                }
                img{
                    width:20px;
                    height: 20px;
                    margin-right: 4px;
                }
            }
        }
    }
}
</style>