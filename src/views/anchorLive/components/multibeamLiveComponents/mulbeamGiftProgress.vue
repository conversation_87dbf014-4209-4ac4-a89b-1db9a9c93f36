<template>
    <div class="live-not-full-gift">
        <div class="live-not-full-gift-left">
            <div class="live-not-full-gift-left-tip" @click.stop="$emit('toggle-mul-tip')">
                <img src="@/assets/live/mul-tip.png" alt="">
                <transition name="fade-scale">
                    <div class="mul-tip" v-if="showMulTip">
                        <div class="mul-tip-header">
                            <img src="@/assets/live/mul-tip-header.png" alt="">
                            <span>Announcement</span>
                        </div>
                        <div class="mul-tip-content">
                            <span>Glad to have you here, welcome!</span>
                        </div>
                    </div>
                </transition>
            </div>
            <div class="live-not-full-gift-left-item" @click.stop="$emit('toggle-tip')">
                <img src="@/assets/common/coins.png" alt="金币" class="coins-img">
                <span>{{ progress }}/1000</span>
                <div class="live-not-full-gift-left-progress"
                    :style="{ width: ((progress / 1000) >= 1 ? 1 : (progress / 1000)) * 100 + '%', borderRadius: (progress / 1000) * 100 >= 100 ? '18px' : '18px 0 0 18px' }">
                </div>
                <transition name="fade-scale">
                    <div class="not-full-gift-tip" v-if="showTip">
                        <div class="not-full-gift-tip-title">
                            <img src="@/assets/common/coins.png" alt="">
                            <span>Room Quota</span>
                        </div>
                        <!---礼物进度-->
                        <div class="not-full-gift-tip-progress">
                            <div class="not-full-gift-tip-progress-num">{{ progress }}/1000
                            </div>
                            <div class="not-full-gift-tip-progress-bar">
                                <div class="not-full-gift-tip-progress-bar-item"
                                    :style="{ width: ((progress / 1000) >= 1 ? 1 : (progress / 1000)) * 100 + '%' }">
                                </div>
                            </div>
                        </div>
                        <!--提示-->
                        <div class="not-full-gift-tip-title not-full-gift-tip-description">
                            <img src="@/assets/anchor/live-notice.png" alt="">
                            <span>Description</span>
                        </div>
                        <div class="not-full-gift-tip-description-content">
                            <span>Diislike = Bloock ed/Repoort</span>
                        </div>
                        <!--送礼物按钮-->
                        <div class="send-gift-btn">
                            <div class="send-gift-btn-item" @click="$emit('send-gift')">
                                Send Gift
                            </div>
                        </div>
                    </div>
                </transition>
            </div>

        </div>
        <div class="live-not-full-gift-right">
            ID:1232323455
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
defineProps<{
    progress: number
    showTip: boolean,
    showMulTip: boolean
}>()
defineEmits<{
    'toggle-tip': [],
    'toggle-mul-tip': []
    'send-gift': []
}>()
</script>

<style lang="scss" scoped>
.live-not-full-gift {
    margin-top: 11px;
    padding: 0 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .live-not-full-gift-left {
        display: flex;
        align-items: center;
        gap: 5px;

        &-tip {
            padding: 3px 10px;
            background: rgba($color: #000000, $alpha: .2);
            border-radius: 18px;
            display: flex;
            align-items: center;
            position: relative;

            img {
                width: 30px;
                height: 17px;

            }

            .mul-tip {
                position: absolute;
                left: 0;
                top: 33px;
                min-width: 166px;
                padding: 8px;
                box-sizing: border-box;
                background-color: #FFFFFF;
                border-radius: 12px;
                z-index: 11;

                &::before {
                    content: "";
                    position: absolute;
                    top: -7px;
                    left: 20px;
                    width: 0;
                    height: 0;
                    border-left: 7px solid transparent;
                    border-right: 7px solid transparent;
                    border-bottom: 7px solid #FFFFFF;
                }

                .mul-tip-header {
                    display: flex;
                    align-items: center;
                    gap: 2px;

                    img {
                        width: 12px;
                        height: 12px;
                    }

                    span {
                        font-weight: 700;
                        font-size: 12px;
                        line-height: 14px;
                        color: rgba($color: #000000, $alpha: .7);
                    }
                }

                .mul-tip-content {
                    margin-top: 4px;
                    padding: 8px 12px 8px 8px;
                    box-sizing: border-box;
                    background-color: #EFEFEF;
                    border-radius: 6px;

                    span {
                        font-size: 12px;
                        font-weight: 700;
                        line-height: 14px;
                        color: #000;
                    }
                }
            }
        }

        &-item {
            display: flex;
            align-items: center;
            gap: 6px;
            width: 100px;
            height: 22px;
            padding-left: 4px;
            background: rgba($color: #000000, $alpha: .2);
            border-radius: 18px;
            position: relative;
            cursor: pointer;

            img {
                width: 16px;
                height: 16px;
            }

            span {
                font-size: 14px;
                color: #fff;
                font-weight: 500;
            }
        }


        .live-not-full-gift-left-progress {
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            background-color: #7C17FF;
            border-radius: 18px 0 0 18px;
            z-index: -1;
        }

        .not-full-gift-tip {
            position: absolute;
            left: 0;
            top: 33px;
            padding: 7px 6px 10px;
            box-sizing: border-box;
            background-color: #7C17FF;
            border-radius: 12px;
            z-index: 11;

            &::before {
                content: "";
                position: absolute;
                top: -7px;
                left: 20px;
                width: 0;
                height: 0;
                border-left: 7px solid transparent;
                border-right: 7px solid transparent;
                border-bottom: 7px solid #7C17FF;
            }

            .not-full-gift-tip-title {
                display: flex;
                align-items: center;
                gap: 2px;

                img {
                    width: 12px;
                    height: 12px;
                }

                span {
                    font-size: 12px;
                    line-height: 14px;
                    font-weight: 600;
                    color: rgba($color: #fff, $alpha: .7);
                }
            }

            .not-full-gift-tip-progress {
                margin-top: 5px;
                display: flex;
                flex-direction: column;
                gap: 4px;

                .not-full-gift-tip-progress-num {
                    font-size: 12px;
                    line-height: 14px;
                    font-weight: 700;
                    color: #fff;
                }

                .not-full-gift-tip-progress-bar {
                    width: 98px;
                    height: 3px;
                    background-color: rgba($color: #fff, $alpha: .3);
                    border-radius: 2px;

                    .not-full-gift-tip-progress-bar-item {
                        height: 100%;
                        background-color: #fff;
                        border-radius: 2px;
                    }
                }
            }

            .not-full-gift-tip-description {
                margin-top: 9px;
            }

            .not-full-gift-tip-description-content {
                margin-top: 6px;

                span {
                    font-size: 12px;
                    line-height: 14px;
                    font-weight: 800;
                    color: #fff;
                }
            }

            .send-gift-btn {
                margin-top: 8px;
                padding: 0 4px;
                box-sizing: border-box;

                .send-gift-btn-item {
                    width: 100%;
                    height: 28px;
                    background-color: #fff;
                    border-radius: 30px;
                    font-size: 12px;
                    font-weight: 800;
                    color: #7120FF;
                    text-align: center;
                    line-height: 28px;
                    cursor: pointer;
                }
            }
        }
    }

    .live-not-full-gift-right {
        padding: 3px 6px;
        font-size: 10px;
        color: rgba($color: #fff, $alpha: .7);
        font-weight: 700;
        line-height: 12px;
        background-color: rgba($color: #000000, $alpha: .1);
        border-radius: 18px;
    }
}

// fade-scale transition
.fade-scale-enter-active,
.fade-scale-leave-active {
    transition: all 0.3s ease;
}

.fade-scale-enter-from,
.fade-scale-leave-to {
    opacity: 0;
    transform: scale(0.8);
}
</style>