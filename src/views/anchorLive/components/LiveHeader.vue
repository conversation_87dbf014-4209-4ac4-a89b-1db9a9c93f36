<template>
    <div class="live-item-header">
        <div class="live-item-header-left">
            <img :src="live.anchorAvatar || '@/assets/home/<USER>'" alt="主播头像" class="left-img" @click="openUserInfoPopup">
            <div class="live-name-num" >
                <span class="live-name">{{ live.anchorName || '<PERSON> Cooper' }}</span>
                <div class="live-view-num">
                    <img src="@/assets/anchor/live-view.png" alt="观看人数">
                    <span>{{ live.viewCount || 222 }}</span>
                </div>
            </div>
            <!--未关注时显示的图标-->
            <img src="@/assets/anchor/live-follow.png" alt="关注" class="unfollow-img" @click="$emit('follow', live.id)">
        </div>
        <div class="live-item-header-right">
            <div class="user-scroll">
                <div class="user-swiper">
                    <div class="user-swiper-item" v-for="item in live.onlineUsers || 10" :key="item" @click="openUserInfoPopup">
                        <img src="@/assets/home/<USER>" alt="在线用户">
                    </div>
                </div>
            </div>
            <div class="live-num-box" @click="showLiveRankingPopup">
                {{ live.onlineCount || 200 }}
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
defineProps<{
    live: any
}>()

const emit = defineEmits<{
    follow: [liveId: number]
    showLiveRankingPopup: [],
    openUserInfoPopup: []
}>()

const showLiveRankingPopup = () => {
    emit('showLiveRankingPopup')
}

const openUserInfoPopup = () => {
    emit('openUserInfoPopup')
}

</script>

<style lang="scss" scoped>
.live-item-header {
    margin-top: 11px;
    width: 100%;
    height: 44px;
    position: relative;
    padding: 0 49px 0 15px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-left {
        padding: 6px;
        box-sizing: border-box;
        background: rgba($color: #000000, $alpha: .15);
        border-radius: 37px;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);

        .left-img {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            object-fit: cover;
            object-position: top;
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;

            &:hover {
                border-color: rgba(255, 255, 255, 0.5);
                transform: scale(1.05);
            }
        }

        .live-name-num {
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-left: 6px;
            margin-top: -4px;

            .live-name {
                font-size: 14px;
                font-weight: 500;
                line-height: 17px;
                color: #fff;
                transition: color 0.3s ease;

                &:hover {
                    color: #ff6b9d;
                }
            }

            .live-view-num {
                display: flex;
                align-items: center;

                img {
                    width: 16px;
                    height: 16px;
                }

                span {
                    font-size: 10px;
                    font-weight: 700;
                    line-height: 12px;
                    color: #fff;
                }
            }
        }

        .unfollow-img {
            width: 34px;
            height: 30px;
            margin-inline-start: 4px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                transform: scale(1.1);
            }
        }
    }

    &-right {
        flex: 1;
        width: 200px;
        height: 100%;
        overflow: hidden;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        flex-direction: row;

        .user-scroll {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            overflow: hidden;
            height: 46px;
            padding: 4px;
            box-sizing: content-box;
            mask-image: linear-gradient(90deg, #ffffff 0%, #ffffff 60%, #ffffff 80%, rgba(255, 255, 255, 0) 97%, rgba(255, 255, 255, 0) 100%);

            .user-swiper {
                max-width: 120px;
                height: 46px;
                display: flex;
                flex-direction: row;
                align-items: center;
                overflow-x: auto;
                overflow-y: hidden;
                gap: 5px;

                .user-swiper-item {
                    flex-shrink: 0;
                    width: 30px;
                    height: 30px;

                    img {
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                        object-fit: cover;
                        object-position: top;
                    }
                }
            }
        }

        .live-num-box {
            width: 30px;
            height: 30px;
            background-color: #0000004D;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #fff;
            font-weight: 600;
        }
    }
}
</style> 