<!-- 用户信息弹窗组件 -->
<template>
    <div>
        <!-- 底部弹出的用户信息弹窗 -->
        <var-popup position="bottom" v-model:show="showUserInfoPopup" @click-overlay="close" :safe-area="true"
            :default-style="false" :overlay-style="{ opacity: 0 }">
            <div class="user-popup">
                <div class="user-popup__content">
                    <!-- 用户头像 -->
                    <img src="@/assets/home/<USER>" alt="" class="user-avatar">
                    <div class="user-info">
                        <!-- 设置按钮 -->
                        <img src="@/assets/anchor/anchor-setting.png" alt="" class="setting-btn">
                        <!-- 用户名称 -->
                        <div class="user-name">
                            <span>DANI💗🇺🇸</span>
                        </div>
                        <!-- 用户标签区域 -->
                        <div class="user-tags">
                            <!-- 年龄和性别标签 -->
                            <div class="tag-age">
                                <img src="@/assets/home/<USER>" alt="">
                                <span>20</span>
                            </div>
                            <!-- 热门标签 -->
                            <img src="@/assets/home/<USER>" alt="" class="tag-hot">
                            <!-- 认证标签 -->
                            <div class="tag-verified">
                                <img src="@/assets/anchor/verified.png" alt="" class="icon-img">
                                <span class="icon-text">Verified</span>
                            </div>
                        </div>
                        <!-- 主播专属标签区域 -->
                        <div class="anchor-tags">
                            <div class="anchor-tag item-1">Tattoos</div>
                            <div class="anchor-tag item-2">🌟Lively</div>
                            <div class="anchor-tag item-3">💕Sweet</div>
                            <div class="anchor-tag item-4">🏝️Talkative</div>
                            <div class="anchor-tag item-5">💋Sexy</div>
                            <div class="anchor-tag item-5">🌶️Spicy</div>
                        </div>
                        <!-- 用户描述 -->
                        <div class="user-desc">
                            A gentleman is everything that is good. 😍
                            D1slike = 🚫
                        </div>
                        <!-- 统计数据区域（关注、粉丝、礼物数量） -->
                        <div class="user-stats">
                            <div class="stat-item">
                                <span class="stat-num">100</span>
                                <span class="stat-desc">Following</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-num">5.30K</span>
                                <span class="stat-desc">Followers</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-num">4.39K</span>
                                <span class="stat-desc">Gifts Received</span>
                            </div>
                        </div>
                        <!-- 礼物展示和等级展示以及荣誉勋章区域 -->
                        <div class="user-level">
                            <!-- 主播专属区域 -->
                            <div class="anchor-level">
                                <!-- 礼物墙 -->
                                <div class="gift-wall">
                                    <div class="gift-title">Gift wall(47)</div>
                                    <div class="gift-content">
                                        <div class="gift-list">
                                            <img src="https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1747622069crown.gif"
                                                alt="" v-for="item in 10" class="gift-img">
                                        </div>
                                        <img src="@/assets/vip/arrow-right.png" alt="" class="arrow-right">
                                    </div>
                                </div>
                                <!-- 荣誉勋章 -->
                                <div class="honor-medals">
                                    <div class="honor-title">Honor Medals(47)</div>
                                    <div class="honor-content">
                                        <div class="honor-list">
                                            <span class="no-honor">NO honor medals</span>
                                        </div>
                                        <img src="@/assets/vip/arrow-right.png" alt="" class="arrow-right">
                                    </div>
                                </div>
                            </div>
                            <!-- 普通用户区域 -->
                            <div class="user-level-content">
                                <!-- SVIP用户区域 -->
                                <div class="svip-level">
                                    <div class="level-card">
                                        <div class="level-box">
                                            <img src="@/assets/vip/level-1.png" alt="" class="level-img">
                                            <div class="level-text">
                                                <span class="level-label">Level</span>
                                                <span class="level-value">lv.100</span>
                                            </div>
                                        </div>
                                    </div>
                                    <!--SVIP标识-->
                                    <div class="svip-icon">
                                        
                                    </div>
                                    <!-- 荣誉勋章 -->
                                    <div class="honor-medals">
                                        <div class="honor-title">Honor Medals(47)</div>
                                        <div class="honor-content">
                                            <div class="honor-list">
                                                <span class="no-honor">NO honor medals</span>
                                            </div>
                                            <img src="@/assets/vip/arrow-right.png" alt="" class="arrow-right">
                                        </div>
                                    </div>
                                </div>
                                <!-- 普通用户区域 -->
                                <div class="normal-level">
                                </div>
                            </div>
                        </div>
                        <!-- 操作按钮区域 -->
                        <div class="action-buttons">
                            <div class="btn btn-secondary" v-ripple="{ color: 'rgba(240,240,240,1)' }">
                                Message
                            </div>
                            <div class="btn btn-primary" v-ripple="{ color: 'rgba(240,240,240,1)' }">
                                Follow
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="userInfoPopup">
import { ref, watch, } from 'vue'

// 定义组件属性接口
const props = defineProps<Props>()
interface Props {
    show: boolean, // 控制弹窗显示状态
    userInfo: any  // 用户信息数据
}

// 定义事件发射器
const emit = defineEmits(['update:show'])

// 响应式数据
const isSvip = ref(true) // 是否为SVIP用户
const showUserInfoPopup = ref(false) // 弹窗显示状态

// 监听弹窗状态变化，向父组件发送更新事件
watch(() => showUserInfoPopup.value, (newVal: boolean) => {
    emit('update:show', newVal)
})

// 监听父组件传入的show属性变化
watch(() => props.show, (newVal: boolean) => {
    showUserInfoPopup.value = newVal
}, {
    immediate: true // 立即执行一次
})

/**
 * 关闭弹窗的方法
 */
const close = () => {
    console.log('close')
    showUserInfoPopup.value = false
}
</script>

<style lang="scss" scoped>
/* 用户信息弹窗主容器 */
.user-popup {
    width: 100%;
    overflow: hidden;
    font-family: var(--font-family-urbanist);

    /* 弹窗内容区域 */
    &__content {
        padding: 45px 0 0;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    /* 用户头像样式 */
    .user-avatar {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        top: 45px;
        z-index: 1;
        object-fit: cover;
        object-position: top;
    }

    /* 用户信息内容区域 */
    .user-info {
        width: 100%;
        padding: 45px 20px 20px;
        box-sizing: border-box;
        background-color: #0A0A1B;
        position: relative;
        border-radius: 16px 16px 0 0;
        display: flex;
        flex-direction: column;
        justify-content: center;

        /* 设置按钮 */
        .setting-btn {
            position: absolute;
            right: 15px;
            top: 15px;
            width: 24px;
            height: 24px;
        }

        /* 用户名称样式 */
        .user-name {
            margin-top: 10px;
            font-size: 18px;
            font-weight: 700;
            color: #fff;
            text-align: center;
            line-height: 22px;
        }

        /* 用户标签区域样式 */
        .user-tags {
            margin-top: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 3px;

            /* 年龄和性别标签 */
            .tag-age {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 1px;
                background-color: $common-color;
                padding: 1px 4px;
                border-radius: 6px;

                img {
                    width: 10px;
                    height: 10px;
                }

                span {
                    font-weight: 800;
                    font-style: Italic;
                    font-size: 10px;
                    color: #fff;
                    vertical-align: middle;
                }
            }

            /* 热门标签 */
            .tag-hot {
                width: 31px;
                height: 12px;
            }

            /* 认证标签 */
            .tag-verified {
                padding: 1.5px 4px;
                background: linear-gradient(90deg, #7F36FF 0%, #CA38FF 100%);
                border-radius: 10px;
                display: flex;
                align-items: center;
                font-size: 10px;
                font-weight: 800;
                color: #fff;
                font-style: italic;

                .icon-img {
                    width: 12px;
                }
            }
        }

        /* 主播专属标签区域 */
        .anchor-tags {
            margin-top: 12px;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 4px;
            font-size: 10px;
            color: #000;
            line-height: 12px;
            font-weight: 700;

            /* 标签项样式 */
            .anchor-tag {
                line-height: 12px;
                padding: 3px 8px;
                background: linear-gradient(98deg, #e8fee6 0%, #d6fbee 100%);
                border-radius: 20px;

                /* 不同标签的渐变背景色 */
                &:nth-child(4n-3) {
                    background: linear-gradient(88deg, #fff4cc 0%, #ffe5cd 100%)
                }

                &:nth-child(4n-1) {
                    background: linear-gradient(103deg, #e2efff 0%, #cbd7ff 100%);
                }

                &:nth-child(4n-2) {
                    background: linear-gradient(90deg, #ffe9e9 0%, #ffdbdb 100%);
                }
            }
        }

        /* 用户描述样式 */
        .user-desc {
            padding: 0 38px;
            box-sizing: border-box;
            margin-top: 20px;
            font-size: 14px;
            font-weight: 500;
            color: rgba($color: #fff, $alpha: .7);
            text-align: center;
            line-height: 17px;
        }

        /* 统计数据区域样式 */
        .user-stats {
            margin-top: 20px;
            padding: 0 33px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: space-between;

            /* 统计项样式 */
            .stat-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                /* 数字样式 */
                .stat-num {
                    font-weight: 700;
                    font-size: 18px;
                    line-height: 22px;
                    color: #fff;
                }

                /* 描述文字样式 */
                .stat-desc {
                    margin-top: 4px;
                    font-size: 12px;
                    line-height: 14px;
                    color: rgba($color: #fff, $alpha: .5);
                }
            }
        }

        /* 礼物和等级展示区域 */
        .user-level {
            margin-top: 15px;

            /* 主播专属区域 */
            .anchor-level {
                width: 100%;
                display: flex;
                align-items: center;
                gap: 9px;

                /* 礼物墙区域 */
                .gift-wall {
                    flex: 1;
                    display: flex;
                    height: 70px;
                    flex-direction: column;
                    gap: 5px;
                    background: url('@/assets/vip/gift-bg.png') no-repeat center center;
                    background-size: 100% 100%;
                    overflow: hidden;
                    padding: 12px 10px 6px;

                    /* 礼物墙标题 */
                    .gift-title {
                        font-size: 14px;
                        color: #fff;
                        line-height: 17px;
                        font-weight: 700;
                    }

                    /* 礼物展示区域 */
                    .gift-content {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        /* 礼物列表区域 */
                        .gift-list {
                            flex: 1;
                            display: flex;
                            align-items: center;
                            gap: 3px;
                            overflow-x: auto;
                            scrollbar-width: none;
                            -ms-overflow-style: none;

                            /* 隐藏滚动条 */
                            &::-webkit-scrollbar {
                                display: none;
                            }

                            /* 礼物图片样式 */
                            .gift-img {
                                width: 30px;
                                height: 30px;
                            }
                        }

                        /* 右箭头图标 */
                        .arrow-right {
                            margin-left: 5px;
                            width: 7px;
                            height: 7px;
                        }
                    }
                }

                /* 荣誉勋章区域 */
                .honor-medals {
                    height: 70px;
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 11px;
                    background: url('@/assets/vip/honor-medal.png') no-repeat center center;
                    background-size: 100% 100%;
                    overflow: hidden;
                    padding: 12px 10px 6px;

                    /* 荣誉勋章标题 */
                    .honor-title {
                        font-size: 14px;
                        color: #fff;
                        line-height: 17px;
                        font-weight: 700;
                    }

                    /* 勋章展示区域 */
                    .honor-content {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        /* 勋章列表区域 */
                        .honor-list {
                            flex: 1;
                            display: flex;
                            align-items: center;
                            gap: 3px;
                            overflow-x: auto;
                            scrollbar-width: none;
                            -ms-overflow-style: none;

                            .no-honor {
                                font-size: 12px;
                                color: rgba($color: #fff, $alpha: .5);
                                font-weight: 700;
                                line-height: 14px;
                            }

                            /* 隐藏滚动条 */
                            &::-webkit-scrollbar {
                                display: none;
                            }

                            /* 勋章图片样式 */
                            .honor-img {
                                height: 16px;
                            }
                        }

                        /* 右箭头图标 */
                        .arrow-right {
                            margin-left: 5px;
                            width: 7px;
                            height: 7px;
                        }
                    }
                }
            }

            /* 普通用户区域 */
            .user-level-content {
                width: 100%;
                display: flex;
                align-items: center;
                gap: 9px;
                overflow-x: auto;

                .svip-level {
                    display: flex;
                    align-items: center;
                    gap: 10px;

                    .level-card {
                        width: 120px;
                        height: 70px;
                        padding: 1px;
                        position: relative;

                        &::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: linear-gradient(180deg, #502B65 0%, rgba(121, 62, 46, 0) 100%);
                            border-radius: 10px;
                        }

                        .level-box {
                            width: 100%;
                            height: 100%;
                            background: linear-gradient(83.45deg, #411B4F 0%, #271E54 51.21%, #2B2110 100.48%);
                            border-radius: 10px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            gap: 8px;
                            position: relative;

                            .level-img {
                                width: 50px;
                                height: 50px;
                            }

                            .level-text {
                                display: flex;
                                flex-direction: column;

                                .level-label {
                                    font-size: 14px;
                                    color: #FFFFFF;
                                    font-weight: 700;
                                    line-height: 17px;
                                }

                                .level-value {
                                    margin-top: 8px;
                                    font-size: 12px;
                                    color: rgba($color: #fff, $alpha: .5);
                                    font-weight: 700;
                                    line-height: 14px;
                                }
                            }
                        }
                    }

                    .svip-icon {
                        width: 120px;
                        height: 70px;
                        background: url('@/assets/vip/svip-bg.png') no-repeat center center;
                        background-size: 100% 100%;

                    }

                    /* 荣誉勋章区域 */
                    .honor-medals {
                        width: 163px;
                        height: 70px;
                        display: flex;
                        flex-direction: column;
                        gap: 11px;
                        background: url('@/assets/vip/honor-medal.png') no-repeat center center;
                        background-size: 100% 100%;
                        overflow: hidden;
                        padding: 12px 10px 6px;

                        /* 荣誉勋章标题 */
                        .honor-title {
                            font-size: 14px;
                            color: #fff;
                            line-height: 17px;
                            font-weight: 700;
                        }

                        /* 勋章展示区域 */
                        .honor-content {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;

                            /* 勋章列表区域 */
                            .honor-list {
                                flex: 1;
                                display: flex;
                                align-items: center;
                                gap: 3px;
                                overflow-x: auto;
                                scrollbar-width: none;
                                -ms-overflow-style: none;

                                .no-honor {
                                    font-size: 12px;
                                    color: rgba($color: #fff, $alpha: .5);
                                    font-weight: 700;
                                    line-height: 14px;
                                }

                                /* 隐藏滚动条 */
                                &::-webkit-scrollbar {
                                    display: none;
                                }
                            }

                            /* 右箭头图标 */
                            .arrow-right {
                                margin-left: 5px;
                                width: 7px;
                                height: 7px;
                            }
                        }
                    }
                }

                /* 普通用户区域 */
                .normal-level {}
            }
        }

        /* 操作按钮区域 */
        .action-buttons {
            margin-top: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;

            /* 按钮项样式 */
            .btn {
                flex: 1;
                height: 50px;
                border-radius: 30px;
                font-size: 16px;
                font-weight: 700;
                color: #fff;
                text-align: center;
                line-height: 50px;

                /* 次要按钮样式 */
                &.btn-secondary {
                    background: rgba($color: #fff, $alpha: 0.2);
                }

                /* 主要按钮样式（关注按钮） */
                &.btn-primary {
                    background: linear-gradient(90deg, #6E23FF 0%, #27A9FF 100%);
                }
            }
        }
    }
}
</style>