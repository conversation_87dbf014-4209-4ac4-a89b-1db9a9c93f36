<!--  -->
<template>
    <div>
        <var-popup position="center" v-model:show="showLiveRoomTip" @click-overlay="close" :safe-area="true"
            :default-style="false">
            <div class="live-room-tip-popup">
                <div class="live-room-tip-popup-content">
                    <img src="@/assets/live/live-room-tip.png" alt="" class="live-room-tip-popup-content-img">
                    <div class="live-room-tip-popup-content-box">
                        <img src="@/assets/live/live-room-tip-bg.png" alt="" class="live-room-tip-popup-content-box-bg">
                        <div class="live-room-tip-popup-content-box-text">
                            {{ liveRoomType === 'leave' ? `This host is engaged in a private call. Please come back
                            later.`: `Live room has been closed` }}

                        </div>
                        <div class="live-room-tip-popup-content-box-btn">
                            <div class="btn-item wait" v-ripple="{ color: 'rgba(0,0,0,1)' }">
                                Wait
                            </div>
                            <div class="btn-item level" v-ripple="{ color: 'rgba(240,240,240,1)' }" >
                                More hosts
                            </div>
                        </div>
                        <div class="live-room-tip-popup-content-box-close">
                            <div class="level-button" v-ripple="{ color: 'rgba(240,240,240,1)' }" @click="close">
                                OK
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="LiveRoomTipPopup">
import { ref, watch } from 'vue'

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    liveRoomType: {
        type: String,
        default: 'leave'
    }
})
const emit = defineEmits(['update:show'])

const showLiveRoomTip = ref(false)


watch(() => showLiveRoomTip.value, (newVal: boolean) => {
    emit('update:show', newVal)
})


watch(() => props.show, (newVal: boolean) => {
    showLiveRoomTip.value = newVal
}, {
    immediate: true
})

/**
 * Close the popup.
 */
const close = () => {
    showLiveRoomTip.value = false
}

</script>
<style lang="scss" scoped>
.live-room-tip-popup {
    width: 100svw;
    overflow: hidden;
    font-family: var(--font-family-urbanist);
    padding: 0 38px;
    box-sizing: border-box;

    .live-room-tip-popup-content {
        width: 100%;
        padding-top: 36px;
        position: relative;

        &-img {
            width: 140px;
            height: 140px;
            position: absolute;
            top: 0;
            left: 50%;
            transform: translate(-50%, 0);
        }

        &-box {
            width: 100%;
            padding: 70px 30px 30px;
            box-sizing: border-box;
            background-color: #fff;
            border-radius: 16px;
            display: flex;
            flex-direction: column;
            align-items: center;

            &-bg {
                width: 180px;
                height: 30px;
            }

            &-text {
                margin-top: 68px;
                font-size: 16px;
                line-height: 19px;
                font-weight: 700;
                color: #000;
                text-align: center;
            }

            &-btn {
                width: 100%;
                margin-top: 30px;
                display: flex;
                align-items: center;
                gap: 20px;

                .btn-item {

                    flex: 1;
                    height: 44px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 30px;
                    font-size: 16px;
                    line-height: 19px;
                    font-weight: 700;

                    background-color: #EDEDED;

                    &.wait {
                        color: rgba($color: #000000, $alpha: .3);
                    }

                    &.level {
                        background: linear-gradient(90deg, #6E23FF 0%, #27A9FF 100%);
                        color: #fff;
                    }
                }
            }
            &-close{
                width: 100%;
                margin-top: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                .level-button{
                    width: 110px;
                    height: 44px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    color: #fff;
                    border-radius: 30px;
                    font-size: 16px;
                    line-height: 19px;
                    font-weight: 700;
                    background: linear-gradient(90deg, #6E23FF 0%, #27A9FF 100%);
                }
            }
        }
    }
}
</style>