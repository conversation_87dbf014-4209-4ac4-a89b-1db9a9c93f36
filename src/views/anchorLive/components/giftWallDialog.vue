<template>

    <var-popup position="bottom" v-model:show="showAllGiftWallDialog" :close-on-click-overlay="false" :safe-area="true"
        :default-style="false">
        <div class="all-gift-wall">
            <div class="all-gift-wall-header">
                <img src="@/assets/live/close-popup.png" alt="" class="all-gift-wall-header-img" @click="close"/>
               
            </div>
            <div class="all-gift-wall-content">
                <div class="all-gift-wall-content-user">
                    <div class="all-gift-wall-content-user-avatar">
                        <img src="@/assets/home/<USER>" alt="" class="all-gift-wall-content-user-avatar-img" />
                        <span class="all-gift-wall-content-user-info-name">Apex Race</span>
                    </div>
                    <div class="all-gift-wall-content-user-list">
                        <img src="@/assets/anchor/gift-list-left.png" alt="">
                        <span>Gifts lit up:<span class="gift-num">100</span>/163</span>
                        <img src="@/assets/anchor/gift-list-right.png" alt="">
                    </div>
                </div>
                <!---下面是礼物分类展示-->
                <div class="all-gift-wall-content-gift-list">
                    <div class="list-tab">
                        <div class="lsit-tab-item" v-for="item in tabsList" :key="item.value"
                            :class="{ active: activeTab === item.value }" @click="changeTab(item.value)">
                            {{ item.label }}
                        </div>
                    </div>
                    <var-swipe ref="giftSwipe" :loop="false" :indicator="false" @change="changeSwiper">
                        <var-swipe-item>
                            <var-list loading-text="" finished-text="" error-text="" :finished="tabsList[0].finished"
                                v-model:loading="tabsList[0].loading" @load="loadMoreData(0)">
                                <div class="gift-list">
                                    <div class="gift-list-item" v-for="item in 100" :key="item">
                                        <LazyImage
                                            src="https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1741571199goddess.gif"
                                            :placeholder-src="positonImg" alt="Gift" width="60px" height="60px" />
                                        <div class="gift-name">Racing CarRacing CarRacing CarRacing Car</div>
                                        <span class="gift-num">X1</span>
                                    </div>
                                </div>
                            </var-list>
                        </var-swipe-item>
                        <var-swipe-item>
                            <var-list loading-text="" finished-text="" error-text="" :finished="tabsList[1].finished"
                                v-model:loading="tabsList[1].loading" @load="loadMoreData(1)">
                                <div class="gift-list">
                                    <div class="gift-list-item" v-for="item in 100" :key="item">
                                        <LazyImage
                                            src="https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1720168900serpentfinal.gif"
                                            :placeholder-src="positonImg" alt="Gift" width="60px" height="60px" />
                                        <div class="gift-name">Racing CarRacing CarRacing CarRacing Car</div>
                                        <span class="gift-num">X1</span>
                                    </div>
                                </div>
                            </var-list>
                        </var-swipe-item>
                    </var-swipe>
                </div>
            </div>
        </div>
    </var-popup>

</template>

<script lang="ts">
import positonImg from '@/assets/common/position.png'
import LazyImage from '@/components/LazyImage.vue'
export default {
    name: 'allGiftWallDialog',
    props: {
        show: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            positonImg: positonImg,
            tabsList: [{
                value: 0,
                label: 'All Gifts',
                loading: false,
                finished: false,
                tableData: []
            }, {
                value: 1,
                label: 'Gifts lit up',
                loading: false,
                finished: false,
                tableData: []
            }],
            activeTab: 0,
            showAllGiftWallDialog: this.show
        };
    },
    components: {
        LazyImage   
    },
    watch: {
        show: {
            handler(newVal) {
                this.showAllGiftWallDialog = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        loadMoreData(index: number) {
            console.log('loadMoreData', index)
            this.tabsList[index].loading = true
            this.tabsList[index].finished = true
            this.tabsList[index].loading = false
        },
        close() {
            this.showAllGiftWallDialog = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
        changeTab(value: number) {
            this.activeTab = value
            // 修复类型报错：显式断言 $refs.giftSwipe 的类型
            const swipeRef = this.$refs.giftSwipe as { to: (v: number) => void } | undefined
            if (swipeRef) {
                swipeRef.to(value)
            }
        },
        changeSwiper(value: number) {
            console.log('changeSwiper', value)
            this.activeTab = value
        },

    }
}
</script>
<style lang='scss' scoped>
.all-gift-wall {
    width: 100svw;
    height: 70svh;
    font-family: var(--font-family-urbanist);
    background: url('@/assets/anchor/gift-bg.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .all-gift-wall-header {
        flex-shrink: 0;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 15px;
        box-sizing: border-box;
        font-weight: 700;
        font-size: 18px;
        color: #fff;
        position: relative;
        img {
            position: absolute;
            left: 15px;
            width: 12px;
            height: 12px;
        }
    }

    .all-gift-wall-content {
        flex: 1;
        padding: 7px 15px 0 15px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        overflow: auto;

        .all-gift-wall-content-user {
            .all-gift-wall-content-user-avatar {
                display: flex;
                align-items: center;
                gap: 8px;

                .all-gift-wall-content-user-avatar-img {
                    width: 36px;
                    height: 36px;
                    border-radius: 50%;
                    object-fit: cover;
                    object-position: top;
                }

                .all-gift-wall-content-user-info-name {
                    font-size: 16px;
                    font-weight: 700;
                    color: #fff;
                }

            }

            .all-gift-wall-content-user-list {
                width: max-content;
                margin-top: 10px;
                padding: 6px;
                display: flex;
                align-items: center;
                background: #00000026;
                border-radius: 6px;

                img {
                    width: 20px;
                    height: 20px;
                }

                span {
                    font-size: 16px;
                    font-weight: 900;
                    font-style: italic;
                    color: #fff;

                    .gift-num {
                        color: #FFF316;
                    }
                }
            }
        }

        .all-gift-wall-content-gift-list {
            margin-top: 20px;
            flex: 1;
            display: flex;
            flex-direction: column;

            .list-tab {
                width: 100%;
                height: 31px;
                display: flex;
                justify-content: space-between;

                .lsit-tab-item {
                    flex: 1;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 16px;
                    font-weight: 700;
                    color: rgba($color: #fff, $alpha: .7);
                    position: relative;

                    &.active {
                        color: #fff;

                        &::before {
                            content: '';
                            position: absolute;
                            width: 100%;
                            height: 2px;
                            background-color: #fff;
                            bottom: 0;
                            left: 0;
                        }
                    }
                }
            }

            .var-swipe {
                flex: 1;

                .gift-list {
                    margin-top: 20px;
                    display: grid;
                    grid-template-columns: repeat(4, 1fr);
                    gap: 8px;

                    .gift-list-item {
                        width: 100%;
                        height: 100%;
                        border: 1px solid rgba($color: #fff, $alpha: .2);
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        overflow: hidden;
                        background: linear-gradient(180deg, rgba(27, 11, 57, 0.1) 0%, #3E2669 100%);
                        border-radius: 10px;
                        padding: 7px 10px;
                        box-sizing: border-box;
                        .lazy-image-container{
                            width: 60px;
                            height: 60px;
                        }

                        .gift-name {
                            width: 100%;
                            margin-top: 2px;
                            font-size: 12px;
                            font-weight: 600;
                            line-height: 14px;
                            color: rgba($color: #fff, $alpha: .7);
                            text-align: center;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }

                        .gift-num {
                            margin-top: 2px;
                            font-size: 13px;
                            font-style: italic;
                            font-weight: 800;
                            color: #fff;
                            line-height: 16px;

                        }
                    }
                }
            }
        }
    }
}
</style>