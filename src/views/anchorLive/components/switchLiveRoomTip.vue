<!--  -->
<template>
    <div>
        <var-popup position="center" v-model:show="showSwitchLiveRoomTip" @click-overlay="close" :safe-area="true"
            :default-style="false" :overlay="false">
            <div class="switch-live-room">
                <div class="switch-live-room-content">
                    <div class="switch-live-room-content-title">
                        <div class="switch-live-room-content-title-left">
                            <img src="@/assets/live/up.png" alt="" class="up">
                            <img src="@/assets/live/down.png" alt="" class="down">
                        </div>
                        <div class="switch-live-room-content-title-right">
                            <img src="@/assets/live/point.png" alt="" class="point">
                        </div>
                    </div>
                    <div class="switch-live-room-content-desc">
                        Swipe up and down to
                        switch rooms quickly
                    </div>
                </div>

            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="SwitchLiveRoomTip">
import { ref, watch, onMounted, nextTick } from 'vue'
const props = defineProps<Props>()
interface Props {
    show: boolean,
}

const emit = defineEmits(['update:show'])

const showSwitchLiveRoomTip = ref(false)


watch(() => showSwitchLiveRoomTip.value, (newVal: boolean) => {
    emit('update:show', newVal)
})


watch(() => props.show, (newVal: boolean) => {
    showSwitchLiveRoomTip.value = newVal
}, {
    immediate: true
})
/**
 * Close the popup.
 */
const close = () => {
    showSwitchLiveRoomTip.value = false
}

</script>
<style lang="scss" scoped>
.switch-live-room {
    width: 100svw;
    overflow: hidden;
    font-family: var(--font-family-urbanist);
    box-sizing: border-box;

    .switch-live-room-content {
        width: 200px;
        height: 200px;
        padding: 26px 30px 30px;
        margin: auto;
        box-sizing: border-box;
        background: rgba(0, 0, 0, .7);
        border-radius: 16px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;

        .switch-live-room-content-title {
            display: flex;
            align-items: center;

            .switch-live-room-content-title-left {
                display: flex;
                flex-direction: column;
                gap: 20px;

                img {
                    width: 30px;
                    height: 30px;
                }


            }

            .switch-live-room-content-title-right {
                position: relative;
                left: -20px;

                img {
                    width: 60px;
                    height: 60px;
                }
            }
        }

        .switch-live-room-content-desc {
            font-size: 14px;
            line-height: 17px;
            font-weight: 500;
            color: #fff;
            text-align: center;
        }
    }

    @keyframes up {
        0% {
            transform: translateY(0);
        }

        100% {
            transform: translateY(-10%);
        }
    }

    @keyframes down {
        0% {
            transform: translateY(0);
        }

        100% {
            transform: translateY(10%);
        }
    }

    .up {
        animation: up 1s ease-in-out infinite alternate;
    }

    .down {
        animation: down 1s ease-in-out infinite alternate;
    }
}
</style>