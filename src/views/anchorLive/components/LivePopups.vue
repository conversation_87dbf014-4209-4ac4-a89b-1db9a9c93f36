<template>
    <!-- 关注主播 -->
    <follow-anchor-popup :show="showFollowAnchorPopup" :anchorInfo="anchorInfo"
        @update:show="$emit('update:follow', $event)" />
    <!-- 折扣 -->
    <discount-popup :show="showDiscountPopup" @update:show="$emit('update:discount', $event)" />
    <!-- reminder -->
    <reminder-popup :show="showReminderPopup" @update:show="$emit('update:reminder', $event)" />
    <!-- 更多设置 -->
    <room-tools-popup :show="showRoomTools" :anchorInfo="anchorInfo" @update:show="$emit('update:tools', $event)" />
    <!-- 金币不足 -->

    <recharge-popup :show="showCoinsNotEnoughPopup" @update:show="$emit('update:coins', $event)" />
    <!-- <coins-not-enough-popup :show="showCoinsNotEnoughPopup" @update:show="$emit('update:coins', $event)" /> -->
    <!-- 聊天礼物 -->
    <chat-gift-popup :show="showChatGiftPopup" 
    :isMultiBeamLive="isMultiBeamLive" 
    :multiBeamLiveAnchorList="multiBeamLiveAnchorList"
    :defaultAnchor="defaultAnchor"
        @update:show="$emit('update:chat-gift', $event)"
        @changeShowDiscountPopup="$emit('change-discount')" 
        @changeCoinsNotEnoughPopup="$emit('change-coins')"
         @handleSelectAdminCard="$emit('handleSelectAdminCard')" 
         @loadingGiftAnimation="$emit('loadingGiftAnimation',$event)"
         />
    <switch-live-room-tip :show="showSwitchLiveRoomTip" @update:show="$emit('update:switch-live-room-tip', $event)" />
    <!-- 直播榜排行榜 -->
    <live-ranking-popup :show="showLiveRankingPopup" @update:show="$emit('update:live-ranking', $event)" />
    <!-- 用户信息 -->
    <user-info-popup :show="showUserInfoPopup" :userInfo="userInfo" @update:show="$emit('update:user-info', $event)" />
    <!-- 直播提示 -->
    <live-room-tip-popup :show="showLiveRoomTip" :liveRoomType="liveRoomType"
        @update:show="$emit('update:live-room-tip', $event)" />
    <!-- 荣誉勋章 -->
    <honor-medals-dialog :show="showHonorMedalsDialog" @update:show="$emit('update:honor-medals', $event)" />
    <!-- 礼物墙 -->
    <gift-wall-dialog :show="showGiftWallDialog" @update:show="$emit('update:gift-wall', $event)" />
    <!-- 举报 -->
    <report-anchor-dialog :show="showReportAnchorDialog" @update:show="$emit('update:report-anchor', $event)" />
    <!-- 等级 -->
    <level-popup :show="showLevelPopup" :userInfo="userInfo" @update:show="$emit('update:level', $event)" />
</template>

<script lang="ts" setup>
import FollowAnchorPopup from './follwoAnchorPopup.vue'
import discountPopup from '@/components/discountPopup/discountPopup.vue'
import ReminderPopup from './reminderPopup.vue'
import RoomToolsPopup from './roomToolsPopup.vue'
import coinsNotEnoughPopup from '@/components/coinsNotEnough/coinsNotEnoughPopup.vue'
import chatGiftPopup from '@/components/chatGiftPopup/chatGiftPopup.vue'
import SwitchLiveRoomTip from './switchLiveRoomTip.vue'
import LiveRankingPopup from './liveRankingPopup.vue'
import UserInfoPopup from './userInfoPopup.vue'
import LiveRoomTipPopup from './liveRoomTipPopup.vue'
import HonorMedalsDialog from './honorMedalsDialog.vue'
import GiftWallDialog from './giftWallDialog.vue'
import ReportAnchorDialog from '@/components/reportAnchor.vue'
import LevelPopup from '@/components/levelPopup/levelPopup.vue'

import RechargePopup from '@/components/rechargePopup/rechargePopup.vue'
defineProps<{
    showFollowAnchorPopup: boolean
    showDiscountPopup?: boolean
    showReminderPopup: boolean
    showRoomTools: boolean
    showCoinsNotEnoughPopup: boolean
    showChatGiftPopup: boolean
    userInfo: any,
    anchorInfo: any,
    showSwitchLiveRoomTip: boolean
    showLiveRankingPopup: boolean
    showUserInfoPopup: boolean
    showLiveRoomTip: boolean
    liveRoomType: string
    showHonorMedalsDialog: boolean
    showGiftWallDialog: boolean
    showReportAnchorDialog: boolean
    showLevelPopup: boolean,
    isMultiBeamLive?: boolean,
    multiBeamLiveAnchorList?: any[],
    defaultAnchor?: any
}>()

defineEmits<{
    'update:follow': [value: boolean]
    'update:discount': [value: boolean]
    'update:reminder': [value: boolean]
    'update:tools': [value: boolean]
    'update:coins': [value: boolean]
    'update:chat-gift': [value: boolean]
    'change-discount': []
    'change-coins': []
    'update:switch-live-room-tip': [value: boolean]
    'update:live-ranking': [value: boolean]
    'update:user-info': [value: boolean]
    'update:live-room-tip': [value: boolean]
    'update:honor-medals': [value: boolean]
    'update:gift-wall': [value: boolean]
    'update:report-anchor': [value: boolean]
    'update:level': [value: boolean],
    'handleSelectAdminCard': []
    'loadingGiftAnimation': [value: string]
}>()
</script>