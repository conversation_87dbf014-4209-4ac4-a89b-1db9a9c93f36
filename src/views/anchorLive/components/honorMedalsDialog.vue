<template>

    <var-popup position="bottom" v-model:show="showAllHonorMedalsDialog" :close-on-click-overlay="false" :safe-area="true"
        :default-style="false">
        <div class="honor-medals">
            <div class="honor-medals-header">
                <img src="@/assets/live/close-popup.png" alt="" class="honor-medals-header-img" @click="close" />
            </div>
            <div class="honor-medals-content">
                <div class="honor-medals-content-user">
                    <div class="honor-medals-content-user-avatar">
                        <img src="@/assets/home/<USER>" alt="" class="honor-medals-content-user-avatar-img" />
                        <span class="honor-medals-content-user-info-name">Honor Medals</span>

                    </div>
                    <div class="honor-medals-content-user-list">
                        <img src="@/assets/anchor/gift-list-left.png" alt="">
                        <span>Obtained:<span class="gift-num">100</span></span>
                        <img src="@/assets/anchor/gift-list-right.png" alt="">
                    </div>
                </div>
                <div class="honor-medals-content-honor">
                    <img src="@/assets/live/honor.png" alt="">
                </div>
                <!---下面是礼物分类展示-->
                <div class="honor-medals-content-gift-list">
                    <var-list loading-text="" finished-text="" error-text="" :finished="finished"
                        v-model:loading="loading" @load="loadMoreData(0)">
                        <div class="gift-list">
                            <div class="gift-list-item" v-for="item in 100" :key="item">
                                <LazyImage src="https://iulia.iwlive.club/res/activity/Christmas/2024/<EMAIL>"
                                    :placeholder-src="positonImg" alt="Gift" width="60px" height="60px" />
                                <div class="gift-name">28/08/2024</div>

                            </div>
                        </div>
                    </var-list>
                </div>
            </div>
        </div>
    </var-popup>

</template>

<script lang="ts">
import positonImg from '@/assets/common/position.png'
import LazyImage from '@/components/LazyImage.vue'
export default {
    name: 'allHonorMedalsDialog',
    props: {
        show: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            positonImg: positonImg,
            finished: false,
            loading: false,
            pageNum: 1,
            pageSize: 20,
            tableData: [],
            showAllHonorMedalsDialog: this.show,
        };
    },
    components: {
        LazyImage,
    },
    watch: {
        show: {
            handler(newVal) {
                this.showAllHonorMedalsDialog = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        loadMoreData(index: number) {
            console.log('loadMoreData', index)

        },
        close() {
            this.showAllHonorMedalsDialog = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
    }
}
</script>
<style lang='scss' scoped>
.honor-medals {
    width: 100svw;
    height: 70svh;
    font-family: var(--font-family-urbanist);
    background: url('@/assets/anchor/medals-bg.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .honor-medals-header {
        flex-shrink: 0;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px;
        box-sizing: border-box;
        font-weight: 700;
        font-size: 18px;
        color: #fff;

        &-img {
            width: 12px;
            height: 12px;
        }
    }

    .honor-medals-content {
        flex: 1;
        padding: 7px 15px 0 15px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        overflow: auto;

        .honor-medals-content-user {
            .honor-medals-content-user-avatar {
                display: flex;
                align-items: center;
                gap: 8px;

                .honor-medals-content-user-avatar-img {
                    width: 36px;
                    height: 36px;
                    border-radius: 50%;
                    object-fit: cover;
                    object-position: top;
                }

                .honor-medals-content-user-info-name {
                    font-size: 16px;
                    font-weight: 700;
                    color: #fff;
                }

            }

            .honor-medals-content-user-list {
                width: max-content;
                margin-top: 10px;
                padding: 6px;
                display: flex;
                align-items: center;
                background: #00000026;
                border-radius: 6px;

                img {
                    width: 20px;
                    height: 20px;
                }

                span {
                    font-size: 16px;
                    font-weight: 900;
                    font-style: italic;
                    color: #fff;

                    .gift-num {
                        color: #FFF316;
                    }
                }
            }
        }
        .honor-medals-content-honor{
            margin-top: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            img{
                width: 114px;
                height: 19px;
            }
        }
        .honor-medals-content-gift-list {
            margin-top: 20px;
            flex: 1;
            display: flex;
            flex-direction: column;

            .gift-list {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;

                .gift-list-item {
                    width: 100%;
                    height: 100%;
                    border: 1px solid rgba($color: #fff, $alpha: .2);
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    overflow: hidden;
                    background: linear-gradient(180deg, rgba(45, 46, 136, 0.1) 0%, #2D2E88 100%);
                    border-radius: 10px;
                    padding: 30px 30px 15px;
                    box-sizing: border-box;

                    .lazy-image-container {
                        width: 105px;
                        height: 30px;
                    }

                    .gift-name {
                        margin-top: 21px;
                        width: 100%;
                        font-size: 12px;
                        font-weight: 600;
                        line-height: 14px;
                        color: rgba($color: #fff, $alpha: .7);
                        text-align: center;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
            }
        }
    }
}
</style>