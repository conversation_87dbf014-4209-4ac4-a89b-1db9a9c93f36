<!--  -->
<template>
    <div class="live-slide-premium" v-if="live.isPremium" :style="{ backgroundImage: `url(${live.coverUrl})` }">
        <div class="live-slide-premium-content">
            <div class="live-slide-premium-content-header">
                <img src="@/assets/common/back.png" alt="" class="back-img">
                <div class="live-slide-premium-content-header-right">
                    <img src="@/assets/live/premium-view.png" alt="" class="premium-view-img">
                    <span>200</span>
                </div>
            </div>
            <div class="live-slide-premium-content-section">
                <div class="live-slide-premium-content-section-top">
                    <img :src="live.anchorAvatar" alt="" class="live-slide-premium-content-section-top-avatar">
                    <img src="@/assets/live/premium-unfollow.png" alt="" v-if="!live.follow"
                        class="live-slide-premium-content-section-top-unfollow">
                </div>
                <div class="live-slide-premium-content-section-name">
                    {{ live.anchorName }}
                </div>
                <div class="live-slide-premium-content-section-desc">
                    {{ live.anchorDesc || 'COME IN HAVE FUN WITH US' }}
                </div>
                <!---下面是需要礼物解锁直播间-->
                <div class="live-slide-premium-content-section-gift">
                    <img src="https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1723794189diamond.png" alt=""
                        class="live-slide-premium-content-section-gift-img">
                    <div class="live-slide-premium-content-section-gift-progress">
                        <img src="@/assets/common/coins.png" alt="">
                        <span>1000</span>
                    </div>
                </div>
                <div class="live-slide-premium-content-section-join">
                    <div class="join-btn" @click="handleJoin">
                        Gift to join
                    </div>
                </div>
                <div class="live-slide-premium-content-section-tip">
                    <img src="@/assets/live/enter-unselect.png" class="enter-unselect-img">
                    <span>Enter as Mystery Man</span>
                </div>
            </div>

        </div>

    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
defineOptions({
    name: 'PremiumContent'
})
const props = defineProps<{
    live: any
}>()

const emit = defineEmits<{
    (e: 'wantJoin'): void
}>()
const handleJoin = () => {
    emit('wantJoin')
}
</script>
<style lang="scss" scoped>
.live-slide-premium {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    background-attachment: fixed;

    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: #00000080;
        backdrop-filter: blur(10px)
    }

    .live-slide-premium-content {
        width: 100%;
        height: 100%;
        position: relative;
        padding: calc(env(safe-area-inset-top)) 15px calc(env(safe-area-inset-bottom));
        box-sizing: border-box;

        .live-slide-premium-content-header {
            width: 100%;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .back-img {
                width: 24px;
                height: 24px;
            }

            &-right {
                display: flex;
                align-items: center;
                padding: 5px 15px;
                box-sizing: border-box;
                background-color: #FFFFFF1A;
                border-radius: 18px;
                gap: 2px;

                .premium-view-img {
                    width: 14px;
                    height: 14px;
                }

                span {
                    font-size: 12px;
                    font-weight: 500;
                    color: #fff;
                    margin-left: 5px;
                }
            }
        }

        .live-slide-premium-content-section {
            margin-top: 10px;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;

            .live-slide-premium-content-section-top {
                position: relative;
                width: 90px;
                height: 90px;

                .live-slide-premium-content-section-top-avatar {
                    width: 90px;
                    height: 90px;
                    border-radius: 50%;
                    border: 2px solid #fff;
                }

                .live-slide-premium-content-section-top-unfollow {
                    position: absolute;
                    width: 40px;
                    height: 24px;
                    right: 0;
                    bottom: 0;
                }
            }

            .live-slide-premium-content-section-name {
                margin-top: 12px;
                font-size: 18px;
                font-weight: 700;
                color: #fff;
                line-height: 22px;
                text-align: center;
            }

            .live-slide-premium-content-section-desc {
                margin-top: 10px;
                font-size: 15px;
                font-weight: 700;
                color: rgba($color: #fff, $alpha: .7);
                line-height: 18px;
            }

            .live-slide-premium-content-section-gift {
                margin-top: 50px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 20px;

                &-img {
                    width: 100px;
                    height: 100px;
                }

                &-progress {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 5px;

                    img {
                        width: 20px;
                        height: 20px;
                    }

                    span {
                        font-size: 18px;
                        font-weight: 700;
                        line-height: 22px;
                        color: #fff;
                    }
                }
            }

            .live-slide-premium-content-section-join {
                margin-top: 50px;
                width: 100%;
                padding: 0 43px;
                box-sizing: border-box;

                .join-btn {
                    width: 100%;
                    height: 50px;
                    background: linear-gradient(90deg, #6E23FF 0%, #27A9FF 100%);
                    border-radius: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 15px;
                    font-weight: 700;
                    color: #fff;
                }
            }

            .live-slide-premium-content-section-tip {
                margin-top: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 5px;

                img {
                    width: 15px;
                    height: 15px;
                }

                span {
                    font-size: 13px;
                    font-weight: 600;
                    color: #fff;
                    line-height: 16px;
                }
            }
        }
    }
}
</style>