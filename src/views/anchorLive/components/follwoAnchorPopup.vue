<!--  -->
<template>
    <div>
        <var-popup position="bottom" v-model:show="showFollowAnchorPopup" @click-overlay="close" :safe-area="true"
            :default-style="false" :overlay-style="{opacity:0}">
            <div class="anchor-popup">
                <div class="anchor-popup-content">
                    <img src="@/assets/home/<USER>" alt="" class="anchor-avatar">
                    <div class="anchor-popup-content-info">
                        <img src="@/assets/anchor/anchor-setting.png" alt="" class="anchor-setting">
                        <div class="anchor-popup-content-info-name">
                            <span>DANI💗🇺🇸</span>
                        </div>
                        <div class="anchor-popup-content-info-age">
                            <div class="anchor-popup-content-info-age-item">
                                <img src="@/assets/home/<USER>" alt="">
                                <span>20</span>
                            </div>
                        </div>
                        <div class="anchor-popup-content-info-desc">
                            A gentleman is everything that is good. 😍
                            D1slike = 🚫
                        </div>
                        <div class="anchor-popup-content-info-btn" v-ripple="{ color: 'rgba(240,240,240,1)' }">
                            Follow
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="FollowAnchorPopup">
import { ref, watch, onMounted, nextTick } from 'vue'
const props = defineProps<Props>()
interface Props {
    show: boolean,
    anchorInfo: any
}

const emit = defineEmits(['update:show'])

const showFollowAnchorPopup = ref(false)


watch(() => showFollowAnchorPopup.value, (newVal: boolean) => {
    emit('update:show', newVal)
})


watch(() => props.show, (newVal: boolean) => {
    showFollowAnchorPopup.value = newVal
}, {
    immediate: true
})

/**
 * Close the popup.
 */
const close = () => {
    showFollowAnchorPopup.value = false
}

</script>
<style lang="scss" scoped>
.anchor-popup {
    width: 100%;
    overflow: hidden;
    font-family: var(--font-family-urbanist);

    .anchor-popup-content {
        padding: 45px 0 0;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .anchor-avatar {
            width: 90px;
            height: 90px;
            border-radius: 50%;
            position: relative;
            left: 50%;
            transform: translateX(-50%);
            top: 45px;
            z-index: 1;
        }

        .anchor-popup-content-info {
            width: 100%;
            padding: 45px 20px 20px;
            box-sizing: border-box;
            background-color: #0A0A1B;
            position: relative;
            border-radius: 16px 16px 0 0;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .anchor-setting {
                position: absolute;
                right: 15px;
                top: 15px;
                width: 24px;
                height: 24px;
            }
            &-name {
                margin-top: 10px;
                font-size: 18px;
                font-weight: 700;
                color: #fff;
                text-align: center;
                line-height: 22px;
            }

            &-age {
                margin-top: 4px;
                display: flex;
                justify-content: center;
                align-items: center;

                &-item {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 1px;
                    background-color: $common-color;
                    padding: 1px 4px;
                    border-radius: 6px;

                    img {
                        width: 10px;
                        height: 10px;
                    }

                    span {
                        font-weight: 800;
                        font-style: Italic;
                        font-size: 10px;
                        color: #fff;
                        vertical-align: middle;

                    }
                }
            }

            &-desc {
                padding: 0 38px;
                box-sizing: border-box;
                margin-top: 20px;
                font-size: 14px;
                font-weight: 500;
                color: rgba($color: #fff, $alpha: .7);
                text-align: center;
                line-height: 17px;
            }

            &-btn {
                margin-top: 30px;
                width: 100%;
                height: 50px;
                background: linear-gradient(90deg, #6E23FF 0%, #27A9FF 100%);
                border-radius: 30px;
                font-size: 16px;
                font-weight: 700;
                color: #fff;
                text-align: center;
                line-height: 50px;

            }
        }
    }
}
</style>