<template>
    <div class="send-message-box">
        <div class="send-message-box-left">
            <img src="@/assets/anchor/edit-message.png" alt="消息" class="message-icon">
            <div class="send-message-box-left-input">
                <input type="text" placeholder="Say hi~" :value="modelValue"
                    @input="$emit('update:modelValue', ($event.target as HTMLInputElement).value)"
                    @keyup.enter="$emit('send')">
            </div>
        </div>
        <!---右边操作部分-->
        <div class="send-message-box-right">
            <div class="send-message-box-right-item" @click="$emit('show-tools')">
                <img src="@/assets/anchor/live-more.png" alt="更多">
            </div>
            <div class="send-message-box-right-item" @click="$emit('show-coins')">
                <img src="@/assets/anchor/live-say-hi.png" alt="打招呼">
            </div>
        </div>
        <!--这个是下面的tip 展示，比如打招呼，送礼物 say hi等等-->
        <div class="live-tip-box">
            <transition name="slide-up">
                <div class="say-hi" v-show="showSayHi">
                    <img src="@/assets/live/say-hi.png" alt="">
                    <span>Say hi the livestreamer</span>
                </div>
            </transition>
            <transition name="slide-up">
                <div class="say-hi" v-show="showSendGift">
                    <img src="@/assets/live/gift.png" alt="">
                    <span>Like her? Send a gift!</span>
                </div>
            </transition>
        </div>
    </div>
</template>

<script lang="ts" setup>
defineProps<{
    modelValue: string
    showSayHi?: boolean
    showSendGift?: boolean
}>()

defineEmits<{
    'update:modelValue': [value: string]
    'send': []
    'show-tools': []
    'show-coins': []
}>()
</script>

<style lang="scss" scoped>
.send-message-box {
    margin-top: 21px;
    width: 100%;
    height: 36px;
    padding: 0 15px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;

    .send-message-box-left {
        width: 140px;
        height: 100%;
        padding: 6px 10px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        background-color: rgba($color: #000000, $alpha: .15);
        border-radius: 45px;

        .message-icon {
            width: 24px;
            height: 24px;
        }

        .send-message-box-left-input {
            flex: 1;
            height: 100%;
            position: relative;

            input {
                width: 100%;
                height: 100%;
                border: none;
                outline: none;
                background-color: transparent;
                color: #fff;
                font-size: 14px;
                font-weight: 600;
                line-height: 17px;

                &::placeholder {
                    color: #fff;
                    font-size: 14px;
                    font-weight: 600;
                    line-height: 17px;
                }
            }
        }
    }

    .send-message-box-right {
        display: flex;
        align-items: center;
        gap: 11px;
        padding-right: 42px;
        box-sizing: border-box;

        .send-message-box-right-item {
            width: 32px;
            height: 32px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            background-color: rgba($color: #000000, $alpha: .1);
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                background-color: rgba($color: #000000, $alpha: .2);
                transform: scale(1.05);
            }

            img {
                width: 100%;
                height: 100%;
                border-radius: 50%;
                object-fit: contain;
            }
        }
    }

    .live-tip-box {
        position: absolute;
        max-width: 225px;
        box-sizing: border-box;
        bottom: calc(100% + 9px);
        transition: all .3s;
        display: flex;
        flex-direction: column;
        gap: 10px;

        .say-hi {
            padding: 6px 10px;
            max-width: 100%;
            width: max-content;
            display: flex;
            align-items: center;
            gap: 4px;
            background: linear-gradient(90deg, #6E23FF 0%, #27A9FF 100%);
            border-radius: 30px;

            img {
                width: 24px;
                height: 24px;
            }

            span {
                font-weight: 800;
                font-size: 12px;
                color: #fff;
            }
        }
    }
}

// slide-up transition
.slide-up-enter-active,
.slide-up-leave-active {
    transition: all 0.5s ease-out;
}

.slide-up-enter-from {
    opacity: 0;
    transform: translateY(100%);
}

.slide-up-enter-to {
    opacity: 1;
    transform: translateY(0);
}

.slide-up-leave-from {
    opacity: 1;
    transform: translateY(0);
}

.slide-up-leave-to {
    opacity: 0;
    transform: translateY(100%);
}
</style>