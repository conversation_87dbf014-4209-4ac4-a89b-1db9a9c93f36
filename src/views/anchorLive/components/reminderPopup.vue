<!--  -->
<template>
    <div>
        <var-popup position="center" v-model:show="showReminderPopup" @click-overlay="close" :safe-area="true"
            :default-style="false" :overlay="false">
            <div class="reminder-popup">
                <div class="reminder-popup-content">
                    <div class="reminder-popup-content-title">
                        Reminder
                    </div>
                    <div class="reminder-popup-content-desc">
                        You'll send this gift with one click. Confirm to send?
                    </div>
                    <div class="reminder-popup-content-choose">
                        <img src="@/assets/common/has-chose.png" alt="" class="reminder-popup-content-choose-img"
                            v-if="dontRemindAgain" @click="changeDontRemindAgain">
                        <img src="@/assets/common/not-chose.png" alt="" class="reminder-popup-content-choose-img"
                            v-else @click="changeDontRemindAgain">
                        <span>Don't remind again</span>
                    </div>
                    <div class="reminder-popup-content-btn">
                        <div class="reminder-popup-content-btn-item cancel-btn" @click="close" v-ripple="{ color: 'rgba(240,240,240,1)' }">
                            <span>Cancel</span>
                        </div>
                        <div class="reminder-popup-content-btn-item confirm-btn" v-ripple="{ color: 'rgba(240,240,240,1)' }">
                            <span>Confirm</span>
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="ReminderPopup">
import { ref, watch, onMounted, nextTick } from 'vue'
const props = defineProps<Props>()
interface Props {
    show: boolean,
}

const emit = defineEmits(['update:show'])

const showReminderPopup = ref(false)

const dontRemindAgain = ref(false)

watch(() => showReminderPopup.value, (newVal: boolean) => {
    emit('update:show', newVal)
})


watch(() => props.show, (newVal: boolean) => {
    showReminderPopup.value = newVal
}, {
    immediate: true
})
/**
 * Close the popup.
 */
const close = () => {
    showReminderPopup.value = false
}

const changeDontRemindAgain = () => {
    dontRemindAgain.value = !dontRemindAgain.value
}

</script>
<style lang="scss" scoped>
.reminder-popup {
    width: 100svw;
    overflow: hidden;
    font-family: var(--font-family-urbanist);
    padding: 0 38px;
    box-sizing: border-box;

    .reminder-popup-content {
        width: 100%;
        padding: 30px;
        box-sizing: border-box;
        background-color: #fff;
        border-radius: 16px;

        .reminder-popup-content-title {
            font-weight: 700;
            color: #000;
            font-size: 18px;
            line-height: 22px;
            text-align: center;

        }

        .reminder-popup-content-desc {
            margin-top: 15px;
            font-size: 14px;
            font-weight: 700;
            line-height: 19px;
            color: rgba($color: #000000, $alpha: .5);
            text-align: center;
        }

        .reminder-popup-content-choose {
            margin-top: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;

            .reminder-popup-content-choose-img {
                width: 15px;
                height: 15px;
            }

            span {
                font-size: 13px;
                font-weight: 700;
                color: #FD2967;
            }
        }

        .reminder-popup-content-btn {
            margin-top: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;

            .reminder-popup-content-btn-item {
                flex: 1;
                height: 44px;
                border-radius: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 16px;
                font-weight: 700;
                &.cancel-btn{
                    background-color: #EDEDED;
                    color: rgba($color: #000000, $alpha: .3);
                }
                &.confirm-btn{
                 background: linear-gradient(90deg, #6E23FF 0%, #27A9FF 100%);
                    color: #fff;
                }
            }
        }
    }
}
</style>