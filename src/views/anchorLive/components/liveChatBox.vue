<!-- live-chat -->
<template>
    <div class="live-chat">
        <div class="live-chat-list" ref="liveChatListRef">
            <div class="live-chat-list-point">
                Please don't spread vulgar, pornographic, abusive content, or any content violating customs, rights or
                laws. Exposure of personal information is also prohibited. Violators will be muted or banned.
            </div>
            <div class="live-chat-list-item" v-for="item in messageList" :key="item.id"
                :class="{ 'is-vip': item.vipType == 1, 'is-svip': item.vipType == 2 }">
                <div class="live-chat-list-item-content">
                    <!--是不是VIP展示的东西-->
                    <div class="live-chat-list-item-content-vip" v-if="item.vipType"
                        :class="getVipLevelClass(item.vipLevel)">
                        {{ item.vipLevel }}
                    </div>
                    <img src="@/assets/vip/vip-tag.png" alt="" class="vip-tag" v-if="item.vipType === 1">
                    <img src="@/assets/vip/svip-tag.png" alt="" class="svip-tag" v-if="item.vipType === 2">
                    <!-- 消息内容 -->
                    <!-- <div class="live-chat-list-item-content-message"> -->
                    <span class="user-name">{{ item.userName }} <span v-if="item.messageType == 1">:</span></span>
                    <span class="message-text">{{ item.message }}</span>
                    <!-- </div> -->
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useThrottleFn } from '@vueuse/core'

interface Props {
    messageList: any[]
}

const props = defineProps<Props>()
const liveChatListRef = ref<HTMLElement>()
const scrollDown = ref(false) // 判断向上滚动还是向下滚动
let beforeScrollTop = 0
let resetTimer: NodeJS.Timeout | null = null

// VIP等级配置
// const vipLevelConfig = {
//     1: { class: 'vip-level-1', range: '1-9' },
//     2: { class: 'vip-level-2', range: '10-19' },
//     3: { class: 'vip-level-3', range: '20-29' },
//     4: { class: 'vip-level-4', range: '30-39' },
//     5: { class: 'vip-level-5', range: '40-49' },
//     6: { class: 'vip-level-6', range: '50-59' },
//     7: { class: 'vip-level-7', range: '60-69' },
//     8: { class: 'vip-level-8', range: '70-79' },
//     9: { class: 'vip-level-9', range: '80-89' },
//     10: { class: 'vip-level-10', range: '90-99' }
// }

// 更优雅的VIP等级class获取方法
const getVipLevelClass = (vipLevel: number) => {
    if (!vipLevel || vipLevel < 1 || vipLevel > 100) return ''

    // 计算等级组 (1-9为第1组，10-19为第2组，以此类推)
    const levelGroup = Math.ceil(vipLevel / 10)

    // 确保等级组在1-9范围内
    if (levelGroup >= 1 && levelGroup <= 9) {
        return `vip-level-${levelGroup}`
    }

    return ''
}

// 或者使用更简洁的映射方式
const vipLevelMap = new Map([
    [1, 'vip-level-1'],   // 1-9级
    [2, 'vip-level-2'],   // 10-19级
    [3, 'vip-level-3'],   // 20-29级
    [4, 'vip-level-4'],   // 30-39级
    [5, 'vip-level-5'],   // 40-49级
    [6, 'vip-level-6'],   // 50-59级
    [7, 'vip-level-7'],   // 60-69级
    [8, 'vip-level-8'],   // 70-79级
    [9, 'vip-level-9'],    // 80-89级
    [10, 'vip-level-10']    // 90-99级
])

// 使用Map的替代方法
const getVipLevelClassMap = (vipLevel: number) => {
    if (!vipLevel || vipLevel < 1 || vipLevel > 100) return ''

    const levelGroup = Math.ceil(vipLevel / 10)
    return vipLevelMap.get(levelGroup) || ''
}

// 处理滚动事件
const handleScroll = () => {
    if (!liveChatListRef.value) return

    const currentScrollTop = liveChatListRef.value.scrollTop
    const delta = currentScrollTop - beforeScrollTop

    // 向上滚动时设置为false，向下滚动时设置为true
    scrollDown.value = delta > 0
    beforeScrollTop = currentScrollTop

    console.log('滚动方向:', scrollDown.value ? '向下' : '向上')

    // 清除之前的定时器
    if (resetTimer) {
        clearTimeout(resetTimer)
    }

    // 2秒后自动设置为true
    resetTimer = setTimeout(() => {
        scrollDown.value = true
        console.log('2秒后自动设置为向下滚动')
    }, 2000)
}

// 节流处理滚动事件
const throttledScrollHandler = useThrottleFn(handleScroll, 100)

//设置滚动到底部
const scrollBottom = () => {
    liveChatListRef.value?.scrollTo({
        top: liveChatListRef.value?.scrollHeight,
        behavior: 'smooth'
    })
}

// 导出scrollDown状态供父组件使用
defineExpose({
    scrollDown,
    scrollBottom
})

onMounted(() => {
    if (liveChatListRef.value) {
        liveChatListRef.value.addEventListener('scroll', throttledScrollHandler)
    }
})

onUnmounted(() => {
    if (liveChatListRef.value) {
        liveChatListRef.value.removeEventListener('scroll', throttledScrollHandler)
    }
    if (resetTimer) {
        clearTimeout(resetTimer)
    }
})
</script>
<style lang="scss" scoped>
.live-chat {
    flex-shrink: 0;
    width: 260px;
    height: 100%;
    overflow-y: hidden;
    position: relative;

    .live-chat-list {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        gap: 4px;
        padding: 10px 0;
        box-sizing: border-box;

        .live-chat-list-point {
            width: 100%;
            font-size: 12px;
            color: #53FFF0;
            line-height: 17px;
            padding: 6px 8px;
            background-color: #00000033;
            border-radius: 8px;
        }

        .live-chat-list-item {
            padding: 6px 8px;
            border-radius: 10px;
            background-color: #00000033;

            &.is-vip {
                background-color: #2B01FF33;
            }

            &.is-svip {
                border: 1px solid #FE8502;
                background-color: #FE850159;
            }

            .live-chat-list-item-content {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                gap: 4px;

                .live-chat-list-item-content-vip {
                    width: 42px;
                    height: 14px;
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    background-position: center;
                    display: inline-block;
                    color: #fff;
                    padding-left: 12px;
                    box-sizing: border-box;
                    font-size: 12px;
                    font-weight: 600;
                    text-align: center;
                    line-height: 14px;

                    // VIP等级背景图样式
                    &.vip-level-1 {
                        background-image: url('@/assets/vip/1-9.png');
                    }

                    &.vip-level-2 {
                        background-image: url('@/assets/vip/10-19.png');
                    }

                    &.vip-level-3 {
                        background-image: url('@/assets/vip/20-29.png');
                    }

                    &.vip-level-4 {
                        background-image: url('@/assets/vip/30-39.png');
                    }

                    &.vip-level-5 {
                        background-image: url('@/assets/vip/40-49.png');
                    }

                    &.vip-level-6 {
                        background-image: url('@/assets/vip/50-59.png');
                    }

                    &.vip-level-7 {
                        background-image: url('@/assets/vip/60-69.png');
                    }

                    &.vip-level-8 {
                        background-image: url('@/assets/vip/70-79.png');
                    }

                    &.vip-level-9 {
                        background-image: url('@/assets/vip/80-89.png');
                    }

                    &.vip-level-10 {
                        background-image: url('@/assets/vip/90-99.png');
                    }
                }

                .vip-tag {
                    width: 31px;
                    height: 14px;
                }

                .svip-tag {
                    width: 40px;
                    height: 14px;
                }

                .user-name {
                    font-size: 14px;
                    font-weight: 700;
                    color: rgba($color: #fff, $alpha: .7);
                }

                .message-text {
                    font-size: 14px;
                    font-weight: 700;
                    color: #fff;
                }
            }
        }
    }
}
</style>