<!-- live-active -->
<template>
    <div class="live-active">
        <div class="live-active-top">
            <Swiper class="swiper-active-top" :loop="true" :autoplay="{ delay: 2500, disableOnInteraction: false }"
                :mousewheel="true" :keyboard="true" :slides-per-view="1" :space-between="0"
                :modules="[Autoplay, Pagination]" :pagination="{
                    clickable: true,
                    dynamicBullets: true,
                    el: '.swiper-pagination'
                }">
                <SwiperSlide v-for="item in activeList" :key="item.id">
                    <LazyImage :src="item.icon" class="swiper-active-top-img"></LazyImage>
                </SwiperSlide>
                <!-- 分页器指示器 -->
                <div class="swiper-pagination"></div>
            </Swiper>
        </div>

        <!--私人聊天-->
        <div class="live-active-private-chat" @click="openPrivateChatPopupFunc" v-if="showPrivateChat">
            <div class="live-active-private-chat-box">
                Private

            </div>
            <div class="height-line">

            </div>
            <div class="private-msg">

            </div>
            <div class="private-phone">

            </div>
        </div>
    </div>
</template>

<script lang="ts" setup name="liveActiveComponents">
import { ref } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay, Pagination } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/autoplay'
import 'swiper/css/pagination'
import LazyImage from '@/components/LazyImage.vue'
const emit = defineEmits(['openPrivateChatPopup'])
const props = defineProps<{
    showPrivateChat: boolean
}>()
const activeList = ref([
    {
        id: 1,
        title: 'Live',
        icon: 'https://d2sbw7ntmjyyf3.cloudfront.net/nb/user_invite_mini.png'
    },
    {
        id: 2,
        title: 'Live',
        icon: 'https://d2sbw7ntmjyyf3.cloudfront.net/nb/xiaobanner_e.png'
    },
    {
        id: 3,
        title: 'Live',
        icon: 'https://d2sbw7ntmjyyf3.cloudfront.net/nb/user_new_start_mini_2024.png'
    },
    {
        id: 4,
        title: 'Live',
        icon: 'https://d2sbw7ntmjyyf3.cloudfront.net/nb/user_new_start_mini_2024.png'
    }
])

const openPrivateChatPopupFunc = () => {
    emit('openPrivateChatPopup')
}
</script>
<style lang="scss" scoped>
.live-active {
    /* 修复宽度异常问题 */
    flex: 1;
    flex-shrink: 1;
    /* 允许收缩 */
    min-width: 0;
    /* 防止flex子元素溢出 */
    max-width: 100%;
    /* 限制最大宽度 */
    width: 100%;
    /* 明确设置宽度 */
    display: flex;
    flex-direction: column;
    justify-content: flex-end;

    .live-active-top {
        padding-right: 15px;
        box-sizing: border-box;
        overflow: hidden;

        .swiper-active-top {
            width: 100%;
            height: 88px;
            max-width: 100%;
            /* 确保swiper不会超出容器 */
            position: relative;

            /* 确保swiper内部元素也不会溢出 */
            :deep(.swiper-wrapper) {
                width: 100%;
                max-width: 100%;
            }

            :deep(.swiper-slide) {
                width: 100%;
                max-width: 100%;
            }

            .swiper-active-top-img {
                width: 100%;
                height: auto;
                border-radius: 8px;
                object-fit: cover;
            }

            /* 分页器样式 */
            :deep(.swiper-pagination) {
                position: absolute;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 10;
            }

            :deep(.swiper-pagination-bullet) {
                width: 6px;
                height: 6px;
                background: rgba(255, 255, 255, 0.5);
                border-radius: 50%;
                margin: 0 3px;
                opacity: 1;
                transition: all 0.3s ease;
            }

            :deep(.swiper-pagination-bullet-active) {
                background: #fff;
                transform: scale(1.2);
            }
        }
    }



    .live-active-private-chat {
        padding: 10px 15px 10px 0;
        position: relative;

        .live-active-private-chat-box {
            width: 100%;
            height: 28px;
            font-family: var(--font-family-urbanist);
            background: linear-gradient(180deg, #EB3EFF 0%, #9A3DFF 100%);
            padding: 5px 0 5px 10px;
            box-sizing: border-box;
            font-size: 15px;
            font-weight: 700;
            color: #fff;
            border-radius: 20px;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;

            // /* 刮光效果 - 矩形 */
            // &::before {
            //     content: '';
            //     position: absolute;
            //     top: 0;
            //     left: -100%;
            //     width: 60px;
            //     height: 100%;
            //     background: linear-gradient(
            //         90deg,
            //         transparent,
            //         rgba(255, 255, 255, 0.4),
            //         transparent
            //     );
            //     animation: shimmer 3s infinite;
            // }
        }

        .height-line {
            position: absolute;
            top: 10px;
            left: 0;
            width: 72px;
            height: 28px;
            border-radius: 14px;
            overflow: hidden;
            transform: rotate(0);
            -webkit-transform: rotate(0deg);

            &::after {
                content: "";
                display: block;
                position: absolute;
                left: 0;
                width: 14px;
                height: 38px;
                transform: skew(-20deg);
                background-color: #fff6;
                animation: moving-line 3s infinite;
            }
        }

        .private-msg {
            width: 21px;
            height: 21px;
            position: absolute;
            top: 0px;
            right: 21px;
            background: linear-gradient(180deg, #fffbf0 0%, #ffeaba 100%);
            box-shadow: 1px 2px 2px #ffeb8a80;
            border-radius: 25px 25px 3px;

            &::before {
                content: "";
                top: 13px;
                left: 9px;
                position: absolute;
                width: 7px;
                height: 3px;
                background: #ffb2b2;
                border-radius: 2px;
            }

            &::after {
                content: "";
                position: absolute;
                top: 6px;
                left: 5px;
                width: 11px;
                height: 3px;
                background: #ffb2b2;
                border-radius: 2px;
            }
        }

        .private-phone {
            position: absolute;
            top: -3px;
            right: -5px;
            width: 50px;
            height: 53px;
            background: url('@/assets/live/private-phone.png') no-repeat center center;
            background-size: 100% 100%;
            animation: call-swing 3s 0s infinite;
        }
    }
}

/* 刮光动画 */
@keyframes shimmer {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}
</style>