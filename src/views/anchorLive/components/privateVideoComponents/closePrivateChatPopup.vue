<!--  -->
<template>
    <div>
        <var-popup position="center" v-model:show="showClosePrivateChatPopup" @click-overlay="close" :safe-area="true"
            :default-style="false" :overlay-style="{ opacity: 0 }">
            <div class="show-close">
                <div class="show-close-content">
                    <img src="@/assets/live/private-close.png" alt="" class="show-close-content-img">
                    <div class="show-close-content-box">
                        <div class="show-close-content-box-text">
                            Are you sure you want to leave?
                        </div>
                        <div class="show-close-content-box-btn">

                            <button class="show-close-content-box-btn-item cancel"
                                v-ripple="{ color: 'rgba(240,240,240,1)' }" @click="close">
                                Cancel
                            </button>
                            <button class="show-close-content-box-btn-item" v-ripple="{ color: 'rgba(240,240,240,1)' }">
                                Leave
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="ClosePrivateChatPopup">
import { ref, watch } from 'vue'

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
})
const emit = defineEmits(['update:show'])

const showClosePrivateChatPopup = ref(false)


watch(() => showClosePrivateChatPopup.value, (newVal: boolean) => {
    emit('update:show', newVal)
})


watch(() => props.show, (newVal: boolean) => {
    showClosePrivateChatPopup.value = newVal
}, {
    immediate: true
})

/**
 * Close the popup.
 */
const close = () => {
    showClosePrivateChatPopup.value = false
}

</script>
<style lang="scss" scoped>
.show-close {
    width: 100svw;
    overflow: hidden;
    font-family: var(--font-family-urbanist);
    padding: 0 48px;
    box-sizing: border-box;

    .show-close-content {
        width: 100%;
        padding-top: 46px;
        position: relative;

        .show-close-content-img {
            width: 100px;
            height: 100px;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            margin: auto;
        }

        .show-close-content-box {
            width: 100%;
            background-color: #1E1D38;
            ;
            border-radius: 16px;
            padding: 70px 0 25px 0;

            .show-close-content-box-text {
                font-size: 14px;
                font-weight: 500;
                line-height: 17px;
                color: rgba($color: #fff, $alpha: .7);
                text-align: center;
            }

            .show-close-content-box-btn {
                margin-top: 35px;
                width: 100%;
                padding: 0 12.5px;
                display: flex;
                gap: 15px;

                .show-close-content-box-btn-item {
                    flex: 1;
                    height: 44px;
                    display: flex;
                    outline: none;
                    border: none;
                    align-items: center;
                    justify-content: center;
                    background-color: #FFFFFF0D;
                    border-radius: 10px;
                    font-size: 16px;
                    font-weight: 700;
                    line-height: 17px;
                    color: #fff;
                    &.cancel{
                        background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                    }
                }
            }
        }
    }
}
</style>