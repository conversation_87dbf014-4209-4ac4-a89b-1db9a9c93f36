<template>
    <div class="send-message-box">
        <div class="send-message-box-left">
            <img src="@/assets/live/no-translate.png" alt="message" class="message-icon" v-if="!showTranslate" @click="changeShowTranslate(true)">
            <img src="@/assets/live/show-translate.png" alt="message" class="message-icon" v-else @click="changeShowTranslate(false)">
            <div class="send-message-box-left-input">
                <input type="text" placeholder="Say hi~" :value="modelValue" @input="$emit('update:modelValue', ($event.target as HTMLInputElement).value)"
                    @keyup.enter="$emit('send')">
            </div>
        </div>
        <!---右边操作部分-->
        <div class="send-message-box-right">
            <div class="send-message-box-right-item" @click="$emit('show-tools')">
                <img src="@/assets/live/reverse-camera.png" alt="camera-reverse">
            </div>
            <div class="send-message-box-right-item" @click="$emit('show-coins')">
                <img src="@/assets/live/mac-open.png" alt="mac">
            </div>
            <div class="send-message-box-right-item" @click="$emit('show-coins')">
                <img src="@/assets/live/camera-open.png" alt="camera">
            </div>
            <div class="send-message-box-right-item" @click="$emit('show-gift')">
                <img src="@/assets/live/send-gift.png" alt="gift">
            </div>
        </div>
        
    </div>
</template>

<script lang="ts" setup name="PrivateChatMessage">
defineProps<{
    modelValue: string
    showTranslate: boolean
}>()

const emit = defineEmits<{
    'update:modelValue': [value: string]
    'send': []
    'show-tools': []
    'show-coins': []
    'update:showTranslate': [value: boolean]
    'show-gift': []
}>()

const changeShowTranslate = (show: boolean) => {
    emit('update:showTranslate', show)
}
</script>

<style lang="scss" scoped>
.send-message-box {
    width: 100%;
    height: 40px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    .send-message-box-left {
        width: 140px;
        height: 100%;
        padding: 6px 10px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        background-color: rgba($color: #000000, $alpha: .15);
        border-radius: 45px;
        .message-icon {
            width: 20px;
            height: 20px;
        }

        .send-message-box-left-input {
            flex: 1;
            height: 100%;
            position: relative;
            input {
                width: 100%;
                height: 100%;
                border: none;
                outline: none;
                background-color: transparent;
                color: #fff;
                font-size: 14px;
                font-weight: 600;
                line-height: 17px;

                &::placeholder {
                    color: #fff;
                    font-size: 14px;
                    font-weight: 600;
                    line-height: 17px;
                }
            }
        }
    }

    .send-message-box-right {
        display: flex;
        align-items: center;
        gap: 10px;
        box-sizing: border-box;

        .send-message-box-right-item {
            width: 40px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            transition: all 0.3s ease;
            img {
                width: 100%;
                height: 100%;
                border-radius: 50%;
                object-fit: cover;
            }
        }
    }

}
</style> 