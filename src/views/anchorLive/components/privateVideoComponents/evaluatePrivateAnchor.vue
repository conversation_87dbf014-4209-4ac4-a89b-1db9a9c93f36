<!--  -->
<template>
    <div>
        <var-popup position="bottom" v-model:show="showEvaluateAnchor" :safe-area="true" :default-style="false"
            :overlay="false">
            <div class="show-evaluate-anchor">
                <div class="show-evaluate-anchor-content">
                    <div class="show-evaluate-anchor-content-header">
                        <LazyImage :src="img1" class="show-evaluate-anchor-content-header-img" />
                        <div class="unfollow-anchor">
                            <img src="@/assets/live/un-follow.png" alt="" class="unfollow-anchor-img">
                        </div>
                    </div>
                    <div class="show-evaluate-anchor-content-box">
                        <div class="anchor-info">
                            <span class="name">Cookie🍪</span>
                            <div class="age">
                                <img src="@/assets/home/<USER>" alt="">
                                <span>32</span>
                            </div>
                        </div>
                        <div class="anchor-desc">
                            How do you feel about this call?
                        </div>
                        <!--选择喜欢还是不喜欢-->
                        <div class="anchor-like">
                            <div class="anchor-like-item" :class="{ active: activeLike === 1 }" @click="changeLike(1)">
                                <div class="item-box">
                                    <img src="@/assets/live/like.png" alt="">
                                    <span>Like</span>
                                </div>

                            </div>
                            <div class="anchor-like-item" :class="{ active: activeLike === 2 }" @click="changeLike(2)">
                                <div class="item-box">
                                    <img src="@/assets/live/dislike.png" alt="">
                                    <span>Dislike</span>
                                </div>
                            </div>
                        </div>
                        <div class="anchor-like-desc" v-if="activeLike !== 2">
                            <div class="anchor-like-desc-item" v-for="item in likeDesc" :key="item.value"
                                @click="changeLikeDesc(item.value)" :class="{ active: activeLikeDesc === item.value }">
                                <span>{{ item.label }}</span>
                            </div>
                        </div>
                        <div class="anchor-like-desc" v-else>
                            <div class="anchor-like-desc-item" v-for="item in dislikeDesc" :key="item.value"
                                @click="changeLikeDesc(item.value)" :class="{ active: activeLikeDesc === item.value }">
                                <span>{{ item.label }}</span>
                            </div>
                        </div>
                        <!--订单Confirm-->
                        <div class="order-confirm">
                            <button class="order-confirm-item" :disabled="activeLike === 0 || activeLikeDesc === ''" @click="confirmOrder" v-ripple="{burst:true,round:true}">Confirm</button>
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="EvaluatePrivateAnchor">
import { ref, watch } from 'vue'
import LazyImage from '@/components/LazyImage.vue'
import img1 from '@/assets/home/<USER>'
const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
})
const emit = defineEmits(['update:show'])
const activeLike = ref(0)
const showEvaluateAnchor = ref(false)
const activeLikeDesc = ref('')
const likeDesc = ref([
    {
        label: 'Pretty Look',
        value: 'prettyLook'
    },
    {
        label: 'Good Attitude',
        value: 'goodAttitude'
    },
    {
        label: 'Talkative',
        value: 'talkative'
    },
    {
        label: 'Satisfying',
        value: 'satisfying'
    },
    {
        label: 'Attractive Voice',
        value: 'attractiveVoice'
    },
    {
        label: 'Nice Figure',
        value: 'niceFigure'
    },
])
const dislikeDesc = ref([
    {
        label: 'Fake Photos',
        value: 'fakePhotos'
    },
    {
        label: 'Negative Attitude',
        value: 'fakeVideos'
    },
    {
        label: 'Glitches',
        value: 'glitches'
    },
    {
        label: 'Bad Conditions',
        value: 'badConditions'
    },
    {
        label: 'Others',
        value: 'others'
    }, {
        label: "Over-retouched Photos",
        value: 'overRetouchedPhotos'
    }
])

watch(() => showEvaluateAnchor.value, (newVal: boolean) => {
    emit('update:show', newVal)
})


watch(() => props.show, (newVal: boolean) => {
    showEvaluateAnchor.value = newVal
}, {
    immediate: true
})

/**
 * Close the popup.
 */
const close = () => {
    showEvaluateAnchor.value = false
}

/**
 * 
 */
const changeLike = (type: number) => {
    activeLike.value = type
}

const changeLikeDesc = (value: string) => {
    if (activeLike.value === 0) activeLike.value = 1;
    activeLikeDesc.value = value
}

const confirmOrder = () => {
    console.log(activeLike.value, activeLikeDesc.value)
}
</script>
<style lang="scss" scoped>
.show-evaluate-anchor {
    width: 100svw;
    overflow: hidden;
    font-family: var(--font-family-urbanist);
    box-sizing: border-box;

    .show-evaluate-anchor-content {
        width: 100%;
        padding-top: 46px;
        position: relative;

        .show-evaluate-anchor-content-header {

            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            margin: 0 auto;
            width: max-content;

            &-img {
                width: 90px;
                height: 90px;
                border-radius: 50%;
                object-fit: cover;
                object-position: top;
            }

            .unfollow-anchor {
                position: absolute;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                right: 0;
                bottom: 0;
                background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                display: flex;
                justify-content: center;
                align-items: center;

                img {
                    width: 18px;
                    height: 18px;
                }
            }
        }

        .show-evaluate-anchor-content-box {
            width: 100%;
            height: 100%;
            background-color: #1E1D38;
            border-radius: 16px 16px 0 0;
            padding: 57px 12px 30px;

            .anchor-info {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;

                .name {
                    font-size: 18px;
                    font-weight: 700;
                    line-height: 22px;
                    color: #fff;
                }

                .age {
                    padding: 0px 4px;
                    display: flex;
                    align-items: center;
                    gap: 1px;
                    background-color: $common-color;
                    border-radius: 6px;

                    img {
                        width: 10px;
                        height: 10px;
                    }

                    span {
                        font-size: 10px;
                        font-weight: 800;
                        font-style: italic;
                        line-height: 12px;
                        color: #fff;
                    }
                }
            }

            .anchor-desc {
                margin-top: 16px;
                font-size: 18px;
                font-weight: 700;
                line-height: 22px;
                background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                text-align: center;
            }

            .anchor-like {
                margin-top: 30px;
                width: 100%;
                height: 50px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 18px;
                box-sizing: border-box;
                background-color: rgba($color: #fff, $alpha: 0.05);
                border-radius: 40px;

                .anchor-like-item {
                    flex: 1;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 1px;
                    box-sizing: border-box;
                    position: relative;

                    .item-box {
                        width: 100%;
                        height: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 8px;
                        box-sizing: border-box;

                        img {
                            width: 32px;
                            height: 32px;
                        }

                        span {
                            font-weight: 700;
                            font-size: 16px;
                            color: #fff;
                        }
                    }



                    &.active {
                        position: relative;
                        z-index: 1;

                        .item-box {
                            background: #542554;
                            border-radius: 40px;
                        }

                        &::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                            border-radius: 40px;
                            z-index: -1;
                        }
                    }
                }
            }

            .anchor-like-desc {
                margin-top: 10px;
                width: 100%;
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;

                .anchor-like-desc-item {
                    width: 100%;
                    height: 44px;
                    background-color: #FFFFFF0D;
                    border-radius: 30px;
                    text-align: center;
                    font-size: 14px;
                    font-weight: 700;
                    line-height: 44px;
                    color: rgba($color: #fff, $alpha: 0.5);
                    transition: background-color 0.3s ease;

                    &.active {
                        background-color: #FF1A9E33;
                        color: #fff;
                    }
                }
            }

            .order-confirm {
                margin-top: 20px;
                width: 100%;
                display: flex;
                padding: 0 18px;
                justify-content: center;

                .order-confirm-item {
                    width: 100%;
                    height: 50px;
                    background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                    border-radius: 10px;
                    text-align: center;
                    font-size: 16px;
                    font-weight: 700;
                    line-height: 44px;
                    color: #fff;
                    outline: none;
                    border: none;
                    &:disabled{
                        opacity: 0.5;
                        cursor: not-allowed;
                    }
                }
            }
        }
    }
}
</style>