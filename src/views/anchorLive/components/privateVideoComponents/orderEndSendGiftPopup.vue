<!--  -->
<template>
    <div>
        <var-popup position="bottom" v-model:show="showOrderEndSendGiftPopup" :safe-area="true" @click-overlay="close"
            :default-style="false" :overlay-style="{
                opacity: 0
            }">
            <div class="show-evaluate-anchor">
                <div class="show-evaluate-anchor-content">
                    <div class="show-evaluate-anchor-content-header">
                        <LazyImage :src="img1" class="show-evaluate-anchor-content-header-img" />
                        <div class="unfollow-anchor">
                            <img src="@/assets/live/un-follow.png" alt="" class="unfollow-anchor-img">
                        </div>
                    </div>

                    <div class="show-evaluate-anchor-content-box">
                        <span class="skip" @click="close">Skip</span>
                        <div class="anchor-info">
                            <span class="name">Cookie🍪</span>
                            <div class="age">
                                <img src="@/assets/home/<USER>" alt="">
                                <span>32</span>
                            </div>
                        </div>
                        <div class="anchor-desc">
                            Thanks for the love!
                            <br />
                            Choose a gift to send her
                        </div>
                        <div class="gift-list">
                            <div class="gift-item" v-for="value in 3" :key="value"
                                :class="{ active: activeGift === value }">
                                <img src="@/assets/test/gift.png" alt="" class="gift-item-img"
                                    @click="changeGift(value)">
                                <div class="gift-item-bottom">
                                    <img src="@/assets/common/coins.png" alt="">
                                    <span>100</span>
                                </div>
                            </div>
                        </div>
                        <!--订单Confirm-->
                        <div class="order-confirm">
                            <button class="order-confirm-item" :disabled="!activeGift" @click="confirmOrder"
                                v-ripple="{ burst: true, round: true }">send</button>
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="OrderEndSendGiftPopup">
import { ref, watch } from 'vue'
import LazyImage from '@/components/LazyImage.vue'
import img1 from '@/assets/home/<USER>'
const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
})
const emit = defineEmits(['update:show'])
const activeLike = ref(0)
const showOrderEndSendGiftPopup = ref(false)
const activeGift = ref(1)

watch(() => showOrderEndSendGiftPopup.value, (newVal: boolean) => {
    emit('update:show', newVal)
})


watch(() => props.show, (newVal: boolean) => {
    showOrderEndSendGiftPopup.value = newVal
}, {
    immediate: true
})

/**
 * Close the popup.
 */
const close = () => {
    showOrderEndSendGiftPopup.value = false
}





const confirmOrder = () => {

}

const changeGift = (value: number) => {
    activeGift.value = value
}
</script>
<style lang="scss" scoped>
.show-evaluate-anchor {
    width: 100svw;
    overflow: hidden;
    font-family: var(--font-family-urbanist);
    box-sizing: border-box;

    .show-evaluate-anchor-content {
        width: 100%;
        padding-top: 46px;
        position: relative;

        .show-evaluate-anchor-content-header {

            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            margin: 0 auto;
            width: max-content;
            z-index: 10;

            &-img {
                width: 90px;
                height: 90px;
                border-radius: 50%;
                object-fit: cover;
                object-position: top;
            }

            .unfollow-anchor {
                position: absolute;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                right: 0;
                bottom: 0;
                background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                display: flex;
                justify-content: center;
                align-items: center;

                img {
                    width: 18px;
                    height: 18px;
                }
            }
        }

        .show-evaluate-anchor-content-box {
            width: 100%;
            height: 100%;
            background-color: #1E1D38;
            border-radius: 16px 16px 0 0;
            padding: 57px 12px 30px;
            position: relative;

            .skip {
                width: max-content;
                position: absolute;
                right: 15px;
                top: 15px;
                font-size: 15px;
                font-weight: 700;
                line-height: 18px;
                color: rgba($color: #fff, $alpha: .7);
            }

            .anchor-info {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;

                .name {
                    font-size: 18px;
                    font-weight: 700;
                    line-height: 22px;
                    color: #fff;
                }

                .age {
                    padding: 0px 4px;
                    display: flex;
                    align-items: center;
                    gap: 1px;
                    background-color: $common-color;
                    border-radius: 6px;

                    img {
                        width: 10px;
                        height: 10px;
                    }

                    span {
                        font-size: 10px;
                        font-weight: 800;
                        font-style: italic;
                        line-height: 12px;
                        color: #fff;
                    }
                }
            }

            .anchor-desc {
                margin-top: 16px;
                font-size: 18px;
                font-weight: 700;
                line-height: 22px;
                background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                text-align: center;
            }

            .gift-list {
                margin-top: 30px;
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 10px;

                .gift-item {
                    width: 100%;
                    height: 130px;
                    background-color: #FFFFFF1A;
                    border-radius: 10px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    gap: 10px;

                    .gift-item-img {
                        width: 60px;
                        height: 60px;
                    }

                    .gift-item-bottom {
                        margin-top: 2px;
                        display: flex;
                        align-items: center;
                        gap: 2px;

                        img {
                            width: 12px;
                            height: 12px;
                        }

                        span {
                            font-weight: 700;
                            font-size: 10px;
                            color: #fff;
                        }
                    }

                    &.active {
                        border: 1px solid #FF1A9E;
                    }
                }
            }

            .order-confirm {
                margin-top: 20px;
                width: 100%;
                display: flex;
                padding: 0 18px;
                justify-content: center;

                .order-confirm-item {
                    width: 100%;
                    height: 50px;
                    background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                    border-radius: 10px;
                    text-align: center;
                    font-size: 16px;
                    font-weight: 700;
                    line-height: 44px;
                    color: #fff;
                    outline: none;
                    border: none;

                    &:disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }
                }
            }
        }
    }
}
</style>