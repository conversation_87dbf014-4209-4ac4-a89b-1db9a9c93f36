<!--  -->
<template>
    <div>
        <var-popup position="center" v-model:show="showPrivateRemindPopup" @click-overlay="close" :safe-area="true"
            :default-style="false" :overlay-style="{ opacity: 0 }">
            <div class="private-remind-popup">

                <div class="private-remind-popup-content">
                    <img src="@/assets/live/live-recharge.png" alt="" class="private-remind-popup-content-img">
                    <div class="private-remind-popup-content-box">
                        <div class="private-remind-popup-content-box-text">
                            First 5 mins for free, then 20 coins/min
                        </div>
                        <div class="private-remind-popup-content-box-btn">
                            <button class="private-remind-popup-content-box-btn-item"
                                v-ripple="{ color: 'rgba(240,240,240,1)' }">
                                Don't remind again
                            </button>
                        </div>

                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="PrivateRemindPopup">
import { ref, watch } from 'vue'

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
})
const emit = defineEmits(['update:show'])

const showPrivateRemindPopup = ref(false)


watch(() => showPrivateRemindPopup.value, (newVal: boolean) => {
    emit('update:show', newVal)
})


watch(() => props.show, (newVal: boolean) => {
    showPrivateRemindPopup.value = newVal
}, {
    immediate: true
})

/**
 * Close the popup.
 */
const close = () => {
    showPrivateRemindPopup.value = false
}

</script>
<style lang="scss" scoped>
.private-remind-popup {
    width: 100svw;
    overflow: hidden;
    font-family: var(--font-family-urbanist);
    padding: 0 48px;
    box-sizing: border-box;

    .private-remind-popup-content {
        width: 100%;
        padding-top: 46px;
        position: relative;

        .private-remind-popup-content-img {
            width: 100px;
            height: 100px;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            margin: auto;
        }

        .private-remind-popup-content-box {
            width: 100%;
            background-color: #1E1D38;
            ;
            border-radius: 16px;
            padding: 70px 0 25px 0;

            .private-remind-popup-content-box-text {
                font-size: 14px;
                font-weight: 500;
                line-height: 17px;
                color: rgba($color: #fff, $alpha: .7);
                text-align: center;
            }

            .private-remind-popup-content-box-btn {
                margin-top: 35px;
                width: 100%;
                padding: 0 40px;

                .private-remind-popup-content-box-btn-item {
                    width: 100%;
                    height: 44px;
                    display: flex;
                    outline: none;
                    border: none;
                    align-items: center;
                    justify-content: center;
                    background-color: #FFFFFF0D;
                    border-radius: 10px;
                    font-size: 16px;
                    font-weight: 700;
                    line-height: 17px;
                    color: #fff;
                }
            }
        }
    }
}
</style>