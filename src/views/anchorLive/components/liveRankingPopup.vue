<!--  -->
<template>
    <div>
        <var-popup position="bottom" v-model:show="showLiveRankingPopup" @click-overlay="close" :safe-area="true"
            :default-style="false" :overlay-style="{
                opacity: 0
            }">
            <div class="live-ranking-popup">
                <div class="live-ranking-popup-content">
                    <div class="title">
                        Audience
                    </div>
                    <div class="list-title">
                        <span>User</span>
                        <span>Send Gift</span>
                    </div>
                    <div class="is-not-svip-tip">
                        <span class="is-not-svip-tip-text">SVIP are displayed with priority and enjoy special
                            logo.</span>
                        <div class="is-not-svip-tip-btn" v-ripple="{ color: 'rgba(240,240,240,1)' }">
                            <img src="@/assets/common/vip.png" alt="" class="is-not-svip-tip-btn-img">
                            <span>Get Now</span>
                            <img src="@/assets/common/svip-arrow-right.png" alt="" class="is-not-svip-tip-btn-arrow">
                        </div>
                    </div>
                    <!---排行榜列表-->
                    <div class="ranking-list">
                        <div class="ranking-list-item" v-for="item in 100" :key="item">
                            <div class="ranking-list-item-left">
                                <span class="ranking-list-item-left-num" :class="{'ranking-list-item-left-num-1': item == 1,'ranking-list-item-left-num-2': item == 2,'ranking-list-item-left-num-3': item == 3}">{{ item }}</span>
                                <div class="left-content">
                                    <lazy-image :src="img1" class="left-content-img" />
                                    <div class="left-content-info">
                                        <span class="left-content-info-name">Visitor-323232</span>
                                        <div class="left-content-info-tag">
                                            <div class="left-content-info-tag-level">
                                                Lv.{{ item }}
                                            </div>
                                            <img src="@/assets/vip/vip-tag.png" alt="" class="left-content-info-tag-vip"
                                                v-if="item % 2 === 0" />
                                            <img src="@/assets/vip/svip-tag.png" alt=""
                                                class="left-content-info-tag-svip" v-if="item % 3 === 0" />
                                            <div class="left-content-info-tag-age"
                                                :class="{ 'left-content-info-tag-age-girl': item % 4 === 3 }">
                                                <img src="@/assets/home/<USER>" alt=""
                                                    class="left-content-info-tag-age-img" v-if="item % 4 !== 3" />
                                                <img src="@/assets/home/<USER>" alt=""
                                                    class="left-content-info-tag-age-img" v-else />
                                                <span class="left-content-info-tag-age-text">18</span>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="ranking-list-item-right">
                               <span>{{ Math.floor(Math.random() * 100) }}</span>
                            </div>
                        </div>
                    </div>
                    <!--提示-->
                    <div class="tip">
                        <span>Only show the top 100 viewers</span>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="LiveRankingPopup">
import { ref, watch, onMounted, nextTick } from 'vue'
import LazyImage from '@/components/LazyImage.vue';
import img1 from '@/assets/home/<USER>';
const props = defineProps<Props>()
interface Props {
    show: boolean,
}

const emit = defineEmits(['update:show'])

const showLiveRankingPopup = ref(false)

const dontRemindAgain = ref(false)

watch(() => showLiveRankingPopup.value, (newVal: boolean) => {
    emit('update:show', newVal)
})


watch(() => props.show, (newVal: boolean) => {
    showLiveRankingPopup.value = newVal
}, {
    immediate: true
})
/**
 * Close the popup.
 */
const close = () => {
    showLiveRankingPopup.value = false
}

const changeDontRemindAgain = () => {
    dontRemindAgain.value = !dontRemindAgain.value
}

</script>
<style lang="scss" scoped>
.live-ranking-popup {
    width: 100svw;
    overflow: hidden;
    font-family: var(--font-family-urbanist);
    box-sizing: border-box;

    .live-ranking-popup-content {
        width: 100%;
        height: 460px;
        padding: 20px 0 0;
        box-sizing: border-box;
        background-color: rgba(10, 10, 27, 1);
        border-radius: 16px 16px 0 0;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .title {
            text-align: center;
            font-weight: 700;
            font-size: 18px;
            line-height: 22px;
            color: #fff;
            margin-bottom: 10px;
        }

        .list-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            font-size: 14px;
            color: rgba($color: #fff, $alpha: 0.5);
            line-height: 17px;
        }

        .is-not-svip-tip {
            margin-top: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            padding: 4px 15px;
            box-sizing: border-box;
            background: linear-gradient(90deg, #F6C85B 0%, #FDE9A7 100%);

            .is-not-svip-tip-text {
                font-size: 10px;
                font-weight: 800;
                color: #481F00;
                line-height: 12px;
            }

            .is-not-svip-tip-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 2px;
                padding: 2px 4px;
                border-radius: 20px;
                background-color: #3F3429;
                font-size: 10px;
                font-weight: 700;
                color: #FDECB7;

                .is-not-svip-tip-btn-img {
                    width: 20px;
                    height: 20px;
                }

                .is-not-svip-tip-btn-arrow {
                    width: 12px;
                    height: 12px;
                }
            }
        }

        .ranking-list {
            margin-top: 10px;
            flex: 1;
            overflow-y: auto;

            .ranking-list-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 15px;
                box-sizing: border-box;
                margin-bottom: 20px;
                

                
                .ranking-list-item-left {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 10px;
                    .ranking-list-item-left-num{
                    font-size: 15px;
                    font-weight: 700;
                    color: #fff;
                    line-height: 18px;
                    &.ranking-list-item-left-num-1{
                        color: #FB9801;
                    }
                    &.ranking-list-item-left-num-2{
                        color: #88BAEC;
                    }
                    &.ranking-list-item-left-num-3{
                        color: #8B7A4A;
                    }
                }
                    .left-content {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 10px;
                        

                        .left-content-img {
                            width: 44px;
                            height: 44px;
                            border-radius: 50%;
                        }

                        .left-content-info {
                            .left-content-info-name {
                                font-size: 15px;
                                font-weight: 700;
                                color: #fff;
                                line-height: 18px;
                            }

                            .left-content-info-tag {
                                margin-top: 4px;
                                display: flex;
                                align-items: center;
                                gap: 3px;

                                .left-content-info-tag-level {
                                    padding: 2px 8px;
                                    font-size: 10px;
                                    font-weight: 500;
                                    color: #fff;
                                    line-height: 12px;
                                    background: linear-gradient(90deg, #A1A7B4 0%, #6C7485 100%);
                                    border: 0.5px solid #FFFFFF;
                                    box-sizing: border-box;
                                    border-radius: 20px;
                                }

                                .left-content-info-tag-vip {
                                    width: 32px;
                                    height: 16px;
                                }

                                .left-content-info-tag-svip {
                                    width: 40px;
                                    height: 16px;
                                }

                                .left-content-info-tag-age {
                                    display: flex;
                                    align-items: center;
                                    gap: 1.5px;
                                    width: 40px;
                                    height: 16px;
                                    justify-content: center;
                                    background: #448FFC;
                                    border-radius: 8px;
                                    &.left-content-info-tag-age-girl{
                                        background: $common-color;
                                    }
                                    .left-content-info-tag-age-img {
                                        width: 13px;
                                        height: 13px;
                                    }

                                    .left-content-info-tag-age-text {
                                        font-size: 10px;
                                        font-weight: 800;
                                        font-style: italic;
                                        color: #fff;
                                    }
                                }
                            }
                        }
                    }
                }
                .ranking-list-item-right{
                    font-size: 15px;
                    font-weight: 700;
                    color: #fff;
                    line-height: 18px;
                }
            }
        }

        .tip {
            width: 100%;
            height: 24px;
            text-align: center;
            background-color: #181834;
            font-size: 12px;
            font-weight: 600;
            color: rgba($color: #fff, $alpha: .7);
            line-height: 24px;
        }
    }
}
</style>