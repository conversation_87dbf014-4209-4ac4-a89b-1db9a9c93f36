<!-- 聊天 -->
<template>
    <div class="app-container chat-container">
        <div class="chat-container-header">
            <div class="chat-container-header-left">
                <div class="anchor-header-left">
                    <img src="@/assets/common/go-back.png" alt="" class="go-back-icon" @click="goBack">
                    <img :src="momDetail.avatar" alt="" class="chat-container-header-left-avatar">
                    <div class="is-online" v-if="momDetail.isOnline">

                    </div>
                </div>
                <div class="left-right-box">
                    <span class="name">{{ momDetail.name }}</span>
                    <div class="bradge-box">
                        <!-- <div class="age-box">
                            <img src="@/assets/home/<USER>" alt="" class="gril-icon">
                            <span class="age">{{ momDetail.age }}</span>
                        </div> -->
                        <img src="@/assets/home/<USER>" alt="" class="hot-icon" v-if="momDetail.isHot">
                    </div>
                </div>
            </div>
            <div class="chat-container-header-right">
                <div class="follow-btn" v-if="momDetail.isFollow">
                    <span class="follow-btn-text">+ Follow</span>
                </div>
            </div>
        </div>
        <!---中间聊天的部分-->
        <div class="free-chat-num" @click="showGetChatSvipPopup = true">
            <div class="free-chat-num-left">
                Free chats letf:3
            </div>
            <div class="free-chat-num-right">
                <img src="@/assets/common/vip.png" alt="" class="free-chat-num-right-icon">
                <span>Unlock all chats</span>
            </div>
        </div>
        <div class="chat-container-content">
            <!--展示主播的名称 图片-->
            <div class="chat-container-content-anchor">
                <div class="anchor-header">
                    <div class="anchor-header-left">
                        <img :src="momDetail.avatar" alt="" class="anchor-header-avatar">
                    </div>
                    <div class="left-right-box">
                        <span class="name">{{ momDetail.name }}</span>
                        <div class="bradge-box">
                            <div class="age-box">
                                <img src="@/assets/home/<USER>" alt="" class="gril-icon">
                                <span class="age">{{ momDetail.age }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="anchor-content">
                    <div class="anchor-content-title">
                        <span class="title">{{ momDetail.title }}</span>
                    </div>
                    <div class="anchor-content-image">
                        <div class="anchor-content-image-item" v-for="(item, index) in momDetail.imgList.slice(0, 5)"
                            :key="item.id">
                            <img :src="item.img" alt="" class="anchor-content-image-item-img">
                            <div class="anchor-content-image-item-content" v-if="index === 4">
                                +{{ momDetail.imgList.length - 5 }}
                            </div>
                            <img src="@/assets/anchor/video-play.png" alt="" class="video-play"
                                v-if="item.type === 'video'">
                        </div>
                        <!-- <div class="anchor-content-image-more" v-if="momDetail.imgList.length > 5">
                            <span class="anchor-content-image-more-text">+{{ momDetail.imgList.length - 5 }}</span>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
        <transition name="fade">
            <div class="gift-container" v-if="showGiftTip">
                Gift message will be at the top of the host's chat.
                <img src="@/assets/anchor/tips-close.png" alt="" class="tips-close-icon" @click="showGiftTip = false">
            </div>
        </transition>
        <!--下面发生消息部分-->
        <div class="chat-container-footer">
            <div class="chat-container-footer-input">

                <img src="@/assets/message/translation-no.png" alt="" class="trans-icon">

                <div class="chat-container-footer-input-box">
                    <input type="text" placeholder="Send message for free~">
                </div>

                <img src="@/assets/message/send-img.png" alt="" class="trans-icon send-img-icon">

            </div>
            <div class="chat-container-footer-img chat-container-footer-gift" @click="changeGiftPopup">
                <img src="@/assets/message/gift.png" alt="" class="gift-icon">
            </div>
            <div class="chat-container-footer-img chat-container-footer-video">
                <img src="@/assets/message/video-call.png" alt="" class="video-icon">
            </div>
        </div>
        <!---礼物弹窗-->
        <ChatGiftPopup :show="showGiftPopup" @close="showGiftPopup = false" @update:show="showGiftPopup = $event"
            @changeShowDiscountPopup="showDiscountPopup = true" @changeCoinsNotEnoughPopup="showCoinsNotEnoughPopup = true"></ChatGiftPopup>

        <getChatSvipPopup :show="showGetChatSvipPopup" @close="showGetChatSvipPopup = false"
            @update:show="showGetChatSvipPopup = $event"></getChatSvipPopup>
        <!--折扣窗口-->
        <discountPopup v-model:show="showDiscountPopup" @update:show="showDiscountPopup = $event" />
        <!--金币展示窗口-->
        <RechargePopup v-model:show="showCoinsNotEnoughPopup" @update:show="showCoinsNotEnoughPopup = $event">
        </RechargePopup>
    </div>
</template>

<script lang="ts" setup>

import { ref } from 'vue'
import ChatGiftPopup from '@/components/chatGiftPopup/chatGiftPopup.vue'
import getChatSvipPopup from '@/components/getChatSvipPopup/getChatSvipPopup.vue'
import discountPopup from '@/components/discountPopup/discountPopup.vue'
import RechargePopup from '@/components/rechargePopup/rechargePopup.vue'

const showGiftPopup = ref(false)

const showGetChatSvipPopup = ref(false)
const showDiscountPopup = ref(false)
const showCoinsNotEnoughPopup = ref(false)
const momDetail = ref({
    id: 1,
    avatar: 'https://picsum.photos/300/200?random=2',
    name: 'SAYRA💎',
    title: 'Are you a good driver on curves?!🔥☁️Are you a good driver on curves?!🔥☁️Are you a good driver on curves?!🔥☁️',
    isFollow: true,
    age: 32,
    isOnline: true,
    isHot: true,
    imgList: [
        {
            id: 1,
            img: 'https://d36zjp6dbikxo9.cloudfront.net/rekognition/FB_IMG_172362924436520240814192934.jpg',
            content: 'Bad girls club ✨',
        },
        {
            id: 2,
            img: 'https://d36zjp6dbikxo9.cloudfront.net/main/f9066e467f2a7b359d5c9a5fcd2cb0f520250730033725.jpg',
            content: 'Bad girls club ✨',
        },
        {
            id: 2,
            img: 'https://d36zjp6dbikxo9.cloudfront.net/rekognition/545bd0cd28109f8c438560ee3bb340e220250728024847.jpg',
            content: 'Bad girls club ✨',
            type: 'video',
        },
        {
            id: 2,
            img: 'https://ilivegirl.s3.amazonaws.com/main/79ebc489b875357c6ae8dfee20b7836120250727164040-medium.jpg',
            content: 'Bad girls club ✨',
        },
        {
            id: 2,
            img: 'https://d36zjp6dbikxo9.cloudfront.net/rekognition/d8530550c83e9c41af040f8ffb5dfbd120250727024654.jpg',
            content: 'Bad girls club ✨',
        },
        {
            id: 2,
            img: 'https://ilivegirl.s3.amazonaws.com/main/79ebc489b875357c6ae8dfee20b7836120250727164040-medium.jpg',
            content: 'Bad girls club ✨',
        },
        {
            id: 2,
            img: 'https://ilivegirl.s3.amazonaws.com/main/79ebc489b875357c6ae8dfee20b7836120250727164040-medium.jpg',
            content: 'Bad girls club ✨',
        },
        {
            id: 2,
            img: 'https://ilivegirl.s3.amazonaws.com/main/79ebc489b875357c6ae8dfee20b7836120250727164040-medium.jpg',
            content: 'Bad girls club ✨',
        },

    ]
})

const showGiftTip = ref(false)

const changeGiftPopup = () => {
    showGiftPopup.value = !showGiftPopup.value
}
</script>
<style lang="scss" scoped>
.app-container {
    overflow: hidden;
    justify-content: space-between;
    font-family: var(--font-family-urbanist);

    .chat-container-header {
        width: 100%;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px;
        box-sizing: border-box;

        .chat-container-header-left {
            display: flex;
            align-items: center;
            gap: 6px;

            .anchor-header-left {
                display: flex;
                align-items: center;
                position: relative;

                .go-back-icon {
                    width: 24px;
                    height: 24px;
                    margin-right: 3px;
                }

                .chat-container-header-left-avatar {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    object-fit: cover;
                }

                .is-online {
                    position: absolute;
                    right: 2px;
                    bottom: 2px;
                    width: 6px;
                    height: 6px;
                    border-radius: 50%;
                    background-color: $success-color;
                }
            }

            .left-right-box {
                display: flex;
                flex-direction: column;
                justify-content: center;

                .name {
                    font-size: 15px;
                    font-weight: 700;
                    color: #fff;
                    line-height: 18px;
                }

                .bradge-box {
                    margin-top: 3px;
                    display: flex;
                    align-items: center;
                    gap: 4px;


                    // .age-box {
                    //     display: flex;
                    //     align-items: center;
                    //     gap: 1px;
                    //     width: max-content;
                    //     background-color: $common-color;
                    //     padding: 1px 4px;
                    //     border-radius: 6px;

                    //     .gril-icon {
                    //         width: 10px;
                    //         height: 10px;
                    //     }

                    //     .age {
                    //         font-size: 10px;
                    //         font-weight: 800;
                    //         color: #fff;
                    //         line-height: 12px;
                    //     }
                    // }

                    .hot-icon {
                        height: 12px;
                    }
                }
            }
        }

        .chat-container-header-right {
            display: flex;
            align-items: center;
            gap: 10px;

            .follow-btn {
                color: #fff;
                padding: 5.5px 9px;
                background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                border-radius: 14px;
                font-weight: 700;
                line-height: 17px;
            }
        }
    }

    .free-chat-num {
        position: fixed;
        top: 44px;
        height: 36px;
        padding: 0 15px;
        width: 100%;
        background: linear-gradient(90deg, #F6C85B 0%, #FDE9A7 100%);
        z-index: 100;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;

        .free-chat-num-left {
            font-size: 13px;
            font-weight: 800;
            line-height: 16px;
            color: #481F00;
        }

        .free-chat-num-right {
            display: flex;
            align-items: center;
            padding: 2px 4px;
            gap: 2px;
            background-color: #3F3429;
            border-radius: 20px;

            img {
                width: 20px;
                height: 20px;
            }

            span {
                font-size: 12px;
                font-weight: 600;
                color: #FDECB7;
                line-height: 14px;

            }
        }
    }

    .chat-container-content {
        flex: 1;
        overflow: auto;
        padding: 52px 15px 10px;
        box-sizing: border-box;
        position: relative;

        .chat-container-content-anchor {
            width: 100%;
            padding: 10px;
            box-sizing: border-box;
            background: linear-gradient(80.52deg, #160E3C 0%, #2C1857 45.56%, #131433 100%);
            border-radius: 12px;

            .anchor-header {
                display: flex;
                align-items: center;
                gap: 8px;

                .anchor-header-left {
                    display: flex;
                    align-items: center;
                    position: relative;

                    .anchor-header-avatar {
                        width: 36px;
                        height: 36px;
                        border-radius: 50%;
                        object-fit: cover;
                    }
                }

                .left-right-box {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;

                    .name {
                        font-size: 16px;
                        font-weight: 700;
                        color: #fff;
                        line-height: 19px;
                    }

                    .bradge-box {
                        margin-top: 3px;
                        display: flex;
                        align-items: center;
                        gap: 4px;

                        .age-box {
                            display: flex;
                            align-items: center;
                            gap: 1px;
                            width: max-content;
                            background-color: $common-color;
                            padding: 1px 4px;
                            border-radius: 6px;

                            .gril-icon {
                                width: 10px;
                                height: 10px;
                            }

                            .age {
                                font-size: 10px;
                                font-weight: 800;
                                color: #fff;
                                line-height: 12px;
                            }
                        }

                    }
                }
            }

            .anchor-content {
                margin-top: 11px;

                &-title {
                    width: 100%;
                    font-size: 12px;
                    font-weight: 600;
                    color: rgba($color: #fff, $alpha: .7);
                    line-height: 14px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .anchor-content-image {
                    margin-top: 10px;
                    display: flex;
                    position: relative;
                    gap: 7px;

                    &-item {
                        width: 100%;
                        height: 60px;
                        border-radius: 6px;
                        overflow: hidden;
                        position: relative;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            object-position: 50% 0;
                            border-radius: 6px;
                        }

                        &-content {
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: rgba($color: #000, $alpha: 0.3);
                            border-radius: 6px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            color: #fff;
                            font-size: 12px;
                            font-weight: 600;
                        }

                        .video-play {
                            position: absolute;
                            left: 5px;
                            bottom: 5px;
                            width: 16px;
                            height: 16px;
                        }
                    }
                }
            }
        }
    }

    .gift-container {
        position: absolute;
        bottom: 71px;
        right: 47px;
        width: 280px;
        height: 69px;
        background: url('@/assets/message/gift-tip.png') no-repeat center center;
        background-size: 100% 100%;
        padding: 15px 39px 0 15px;
        box-sizing: border-box;
        font-size: 14px;
        font-weight: 400;
        color: #fff;
        line-height: 17px;

        .tips-close-icon {
            position: absolute;
            right: 9.5px;
            top: 9.5px;
            width: 8px;
            height: 8px;
        }
    }

    .chat-container-footer {
        width: 100%;
        height: 63px;
        padding: 8px 15px 15px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        gap: 10px;
        background: #010101;
        backdrop-filter: blur(5px);

        .chat-container-footer-input {
            flex: 1;
            height: 100%;
            background-color: #1E1D38;
            border-radius: 20px;
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 0 10px;
            box-sizing: border-box;

            .trans-icon {
                width: 20px;
                height: 20px;
            }

            .chat-container-footer-input-box {
                flex: 1;
                height: 20px;

                input {
                    width: 100%;
                    height: 100%;
                    border: none;
                    outline: none;
                    background: transparent;
                    color: #fff;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 17px;
                }
            }
        }

        .chat-container-footer-img {
            width: 40px;
            height: 40px;

            img {
                width: 100%;
                height: 100%;
            }
        }
    }
}
</style>