<template>
  <div class="test-page">
    <h1>动态闪电测试页面</h1>
    
    <div class="test-section">
      <h2>简单动态闪电</h2>
      <SimpleLightning />
    </div>
    
    <div class="test-section">
      <h2>快速闪烁版本</h2>
      <SimpleLightning variant="fast" />
    </div>
    
    <div class="test-section">
      <h2>慢速脉冲版本</h2>
      <SimpleLightning variant="slow" />
    </div>
    
    <div class="test-section">
      <h2>随机闪烁版本</h2>
      <SimpleLightning variant="random" />
    </div>
    
    <div class="test-section">
      <h2>不同尺寸</h2>
      <div class="size-test">
        <div>
          <h3>小尺寸</h3>
          <SimpleLightning size="small" />
        </div>
        <div>
          <h3>中尺寸</h3>
          <SimpleLightning size="medium" />
        </div>
        <div>
          <h3>大尺寸</h3>
          <SimpleLightning size="large" />
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <h2>高级动态闪电</h2>
      <AdvancedLightning 
        :show-controls="true"
        height="150px"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import SimpleLightning from '@/components/SimpleLightning.vue'
import AdvancedLightning from '@/components/AdvancedLightning.vue'
</script>

<style scoped>
.test-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: #1a1a1a;
  min-height: 100vh;
  color: #fff;
}

.test-page h1 {
  color: #ffff00;
  text-align: center;
  margin-bottom: 30px;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  background: #2a2a2a;
  border-radius: 8px;
  border: 1px solid #333;
}

.test-section h2 {
  color: #ffff00;
  margin-bottom: 20px;
}

.test-section h3 {
  color: #ffff00;
  margin-bottom: 10px;
  text-align: center;
}

.size-test {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.size-test > div {
  background: #333;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #444;
}

@media (max-width: 768px) {
  .test-page {
    padding: 10px;
  }
  
  .size-test {
    grid-template-columns: 1fr;
  }
}
</style> 