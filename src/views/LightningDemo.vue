<template>
  <div class="lightning-demo">
    <div class="demo-header">
      <h1>动态闪电效果演示</h1>
      <p>将静态的闪电线条转换为生动的动态效果</p>
    </div>
    
    <div class="demo-section">
      <h2>基础动态闪电</h2>
      <div class="demo-item">
        <AnimatedLightning />
        <div class="description">
          <h3>基础版本</h3>
          <p>包含闪烁效果、光晕和粒子系统的基础动态闪电</p>
        </div>
      </div>
    </div>
    
    <div class="demo-section">
      <h2>高级动态闪电</h2>
      <div class="demo-item">
        <AdvancedLightning 
          :show-controls="true"
          :auto-play="true"
          height="150px"
        />
        <div class="description">
          <h3>高级版本</h3>
          <p>包含分支闪电、背景光晕、粒子效果和控制按钮的高级版本</p>
        </div>
      </div>
    </div>
    
    <div class="demo-section">
      <h2>不同尺寸演示</h2>
      <div class="size-demos">
        <div class="size-demo">
          <h4>小尺寸</h4>
          <AnimatedLightning />
        </div>
        <div class="size-demo">
          <h4>中尺寸</h4>
          <AdvancedLightning height="100px" />
        </div>
        <div class="size-demo">
          <h4>大尺寸</h4>
          <AdvancedLightning height="200px" />
        </div>
      </div>
    </div>
    
    <div class="demo-section">
      <h2>自定义效果</h2>
      <div class="custom-demos">
        <div class="custom-demo">
          <h4>快速闪烁</h4>
          <AdvancedLightning 
            :speed="2"
            height="120px"
            :show-controls="true"
          />
        </div>
        <div class="custom-demo">
          <h4>慢速脉冲</h4>
          <AdvancedLightning 
            :speed="0.5"
            height="120px"
            :show-controls="true"
          />
        </div>
      </div>
    </div>
    
    <div class="demo-section">
      <h2>使用说明</h2>
      <div class="instructions">
        <div class="instruction-item">
          <h3>1. 基础使用</h3>
          <pre><code>&lt;AnimatedLightning /&gt;</code></pre>
        </div>
        <div class="instruction-item">
          <h3>2. 高级配置</h3>
          <pre><code>&lt;AdvancedLightning 
  :show-controls="true"
  :auto-play="true"
  height="150px"
/&gt;</code></pre>
        </div>
        <div class="instruction-item">
          <h3>3. 自定义样式</h3>
          <pre><code>&lt;AdvancedLightning 
  width="300px"
  height="100px"
  :speed="1.5"
/&gt;</code></pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import AnimatedLightning from '@/components/AnimatedLightning.vue'
import AdvancedLightning from '@/components/AdvancedLightning.vue'
</script>

<style scoped>
.lightning-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #1a1a1a;
  min-height: 100vh;
  color: #fff;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 20px;
  background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
  border-radius: 12px;
}

.demo-header h1 {
  color: #ffff00;
  margin-bottom: 10px;
  font-size: 2.5rem;
}

.demo-header p {
  color: #ccc;
  font-size: 1.1rem;
}

.demo-section {
  margin-bottom: 50px;
  padding: 20px;
  background: #2a2a2a;
  border-radius: 12px;
  border: 1px solid #333;
}

.demo-section h2 {
  color: #ffff00;
  margin-bottom: 20px;
  font-size: 1.8rem;
  border-bottom: 2px solid #ffff00;
  padding-bottom: 10px;
}

.demo-item {
  display: flex;
  gap: 30px;
  align-items: center;
  margin-bottom: 30px;
}

.demo-item .description {
  flex: 1;
}

.demo-item .description h3 {
  color: #ffff00;
  margin-bottom: 10px;
}

.demo-item .description p {
  color: #ccc;
  line-height: 1.6;
}

.size-demos {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.size-demo {
  background: #333;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #444;
}

.size-demo h4 {
  color: #ffff00;
  margin-bottom: 15px;
  text-align: center;
}

.custom-demos {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.custom-demo {
  background: #333;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #444;
}

.custom-demo h4 {
  color: #ffff00;
  margin-bottom: 15px;
  text-align: center;
}

.instructions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.instruction-item {
  background: #333;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #444;
}

.instruction-item h3 {
  color: #ffff00;
  margin-bottom: 15px;
}

.instruction-item pre {
  background: #1a1a1a;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  border: 1px solid #555;
}

.instruction-item code {
  color: #00ff00;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lightning-demo {
    padding: 10px;
  }
  
  .demo-header h1 {
    font-size: 2rem;
  }
  
  .demo-item {
    flex-direction: column;
    gap: 20px;
  }
  
  .size-demos,
  .custom-demos {
    grid-template-columns: 1fr;
  }
  
  .instructions {
    grid-template-columns: 1fr;
  }
}
</style> 