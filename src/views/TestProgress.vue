<template>
  <div class="test-progress">
    <h1>进度条测试</h1>
    
    <div class="test-section">
      <h2>原始进度条</h2>
      <div class="progress-test">
        <div class="live-not-full-gift-left">
          <img src="@/assets/common/coins.png" alt="" class="coins-img">
          <span>{{ progress }}/1000 ({{ progressWidth }})</span>
          <div class="live-not-full-gift-left-progress"
            :style="{ width: progressWidth, borderRadius: progressBorderRadius }">
          </div>
        </div>
      </div>
      
      <div class="controls">
        <button @click="progress = 0">0%</button>
        <button @click="progress = 250">25%</button>
        <button @click="progress = 500">50%</button>
        <button @click="progress = 750">75%</button>
        <button @click="progress = 1000">100%</button>
        <input type="range" v-model="progress" min="0" max="1000" step="10">
      </div>
    </div>
    
    <div class="test-section">
      <h2>调试信息</h2>
      <div class="debug-info">
        <p><strong>progress:</strong> {{ progress }}</p>
        <p><strong>progressWidth:</strong> {{ progressWidth }}</p>
        <p><strong>progressBorderRadius:</strong> {{ progressBorderRadius }}</p>
        <p><strong>计算过程:</strong> ({{ progress }} / 1000) * 100 = {{ (progress / 1000) * 100 }}%</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const progress = ref(500)

const progressWidth = computed(() => {
  const percentage = (progress.value / 1000) * 100
  return percentage >= 100 ? '100%' : percentage + '%'
})

const progressBorderRadius = computed(() => {
  const percentage = (progress.value / 1000) * 100
  return percentage >= 100 ? '18px' : '18px 0 0 18px'
})
</script>

<style scoped>
.test-progress {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: #1a1a1a;
  min-height: 100vh;
  color: #fff;
}

.test-progress h1 {
  color: #ffff00;
  text-align: center;
  margin-bottom: 30px;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  background: #2a2a2a;
  border-radius: 12px;
  border: 1px solid #333;
}

.test-section h2 {
  color: #ffff00;
  margin-bottom: 20px;
}

.progress-test {
  margin-bottom: 20px;
}

.live-not-full-gift-left {
  display: flex;
  align-items: center;
  gap: 6px;
  width: 200px;
  height: 22px;
  padding-left: 4px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 18px;
  position: relative;
  overflow: hidden;
}

.coins-img {
  width: 16px;
  height: 16px;
}

.live-not-full-gift-left span {
  font-size: 14px;
  color: #fff;
  font-weight: 500;
  z-index: 2;
  position: relative;
}

.live-not-full-gift-left-progress {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background-color: #7C17FF;
  border-radius: 18px 0 0 18px;
  z-index: 1;
}

.controls {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.controls button {
  padding: 8px 16px;
  background: #7C17FF;
  color: #fff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.controls button:hover {
  background: #6a14e6;
}

.controls input[type="range"] {
  flex: 1;
  min-width: 200px;
}

.debug-info {
  background: #333;
  padding: 15px;
  border-radius: 8px;
}

.debug-info p {
  margin: 5px 0;
  font-family: monospace;
  font-size: 14px;
}
</style> 