<!-- 获取svip -->
<template>
    <div class="app-container get-svip-container">
        <headerNav title="Get Svip" />
        <div class="get-svip-content">
            <div class="get-svip-content-user">
                <div class="get-svip-content-user-avatar">
                    <img src="@/assets/default.png" alt="" class="get-svip-content-user-avatar-img">
                    <div class="get-svip-content-user-avatar-name">
                        <span class="name">Visitor-22323</span>
                        <span class="svip" v-if="!isSvip">You are not a SVIP user yet</span>
                        <span class="is-svip" v-else>You can enjoy the following 10 benefits</span>
                        <!--join& enjoy-->
                        <div class="get-svip-content-join-enjoy" v-if="!isSvip">
                            Join & Enjoy benefits!
                        </div>
                        <div class="get-svip-content-join-enjoy svip-expire" v-else>
                            Membership expires on 09/07/2025
                        </div>
                    </div>

                </div>
                <!--右边部分-->
                <img src="@/assets/memberShip/not-svip.png" alt="" class="get-svip-content-user-svip-img"
                    v-if="!isSvip">
                <img src="@/assets/memberShip/is-svip.png" alt="" class="get-svip-content-user-svip-img" v-else>
            </div>

            <!--Premium-->
            <div class="get-svip-content-premium">
                <img src="@/assets/memberShip/left.png" alt="" class="left-img">
                <span class="premium-text">Premium Privileges</span>
                <img src="@/assets/memberShip/left.png" alt="" class="right-img">
            </div>
            <div class="is-vip-live-title" v-if="isSvip">
                <span class="is-vip-live-title-text">Go to Live to feel the rights and benefits</span>
                <div class="is-vip-live-title-btn">
                    <span>GO</span>
                    <img src="@/assets/memberShip/go-live.png" alt="">
                </div>
            </div>
            <!--svip 的部分--->
            <div class="get-svip-content-store" :class="{'get-svip-content-store-is-svip': isSvip}">
                <div class="get-svip-content-store-item" v-for="item in 10" :key="item">
                    <img src="https://iulia.iwlive.club/webapp/aso/pro/@1.0.6p/png/rights_04-4ff647e7.png" alt="">
                </div>
            </div>
            <!--concat us -->
            <div class="concat-us">
                <img src="@/assets/common/customer-concat.png" alt="">
                <span class="concat-us-text">Contact Us</span>
                <span class="concat-us-text-desc">if you have questions</span>
            </div>
            <!--bottom-btn-->
            <div class="bottom-btn" v-if="!isSvip">
                <div class="bottom-btn-item">
                    <div class="bottom-btn-item-left">
                        $19.99 /Mo
                    </div>
                    <div class="bottom-btn-item-right">
                        Get Now
                    </div>
                </div>
            </div>
        </div>
        <becomeSvipPopup :show="showBecomeSvipPopup" @close="showBecomeSvipPopup = false" @update:show="showBecomeSvipPopup = $event" />
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import headerNav from '@/components/headerNav/headerNav.vue'
import becomeSvipPopup from './components/becomeSvipPopup.vue'
const isSvip = ref(true)
const showBecomeSvipPopup = ref(true)
</script>
<style lang="scss" scoped>
.get-svip-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    font-family: var(--font-family-urbanist);

    .get-svip-content {

        flex: 1;
        padding: 11px 15px 147px;
        box-sizing: border-box;
        overflow-y: auto;

        .get-svip-content-user {
            display: flex;
            // align-items: center;

            .get-svip-content-user-avatar {
                padding-top: 11px;
                box-sizing: border-box;
                display: flex;
                // align-items: center;
                gap: 10px;

                .get-svip-content-user-avatar-img {
                    width: 50px;
                    height: 50px;
                    border-radius: 50%;
                }

                .get-svip-content-user-avatar-name {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;

                    .name {
                        font-size: 16px;
                        font-weight: 700;
                        color: #fff;
                    }

                    .svip {
                        font-size: 12px;
                        font-weight: 600;
                        line-height: 14px;
                        color: rgba($color: #fff, $alpha: .5);
                    }

                    .is-svip {
                        margin-top: 6px;
                        font-size: 12px;
                        font-weight: 600;
                        line-height: 14px;
                        color: #FFBD10;
                    }

                    .get-svip-content-join-enjoy {
                        margin-top: 20px;
                        font-size: 12px;
                        font-weight: 600;
                        color: rgba($color: #fff, $alpha: .7);
                        line-height: 14px;

                        &.svip-expire {
                            line-height: 12px;
                            font-size: 10px;
                            margin-top: 6px;
                            color: #fff;
                            white-space: nowrap;
                        }
                    }
                }
            }

            .get-svip-content-user-svip-img {
                width: 140px;
                height: 131px;
            }
        }



        .get-svip-content-premium {
            // margin-top: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;

            .left-img {
                width: 28px;
                height: 10px;
            }

            .premium-text {
                font-size: 18px;
                font-weight: 900;
                color: #fff;
                font-style: italic;
                line-height: 22px;
            }

            .right-img {
                width: 28px;
                height: 10px;
                transform: rotate(180deg);
            }
        }
        .is-vip-live-title{
            margin-top: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            .is-vip-live-title-text{
                font-size: 10px;
                font-weight: 700;
                color: #FFE39F;
                line-height: 12px;
            }
            .is-vip-live-title-btn{
                display: flex;
                align-items: center;
                padding: 1px 3px;
                font-size: 10px;
                font-weight: 700;
                background: linear-gradient(88.43deg, #FBEECB 1.34%, #E0AD69 98.66%);
                color: #322403;
                border-radius: 10px;
                img{
                    width: 10px;
                    height: 10px;
                }
            }
        }
        .get-svip-content-store {
            margin-top: 27px;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            &.get-svip-content-store-is-svip{
                margin-top: 10px;
            }
            &-item {
                width: 100%;
                height: 196px;
                border-radius: 10px;

                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }

        .concat-us {
            margin-top: 33px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 600;
            color: #fff;

            img {
                width: 20px;
                height: 20px;
            }

            .concat-us-text {
                color: $common-color;
                text-decoration: underline;
            }
        }

        .bottom-btn {
            position: fixed;
            bottom: calc(env(safe-area-inset-bottom) + 54px);
            left: 0;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 23px;
            box-sizing: border-box;

            .bottom-btn-item {
                width: 100%;
                padding: 15px 20px;
                background: linear-gradient(163.19deg, #FBEECB 1.55%, #FBF5E5 49.7%, #F6CD96 78.14%, #EDBEC4 96.94%);
                border-radius: 16px;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .bottom-btn-item-left {
                    font-size: 20px;
                    font-weight: 700;
                    color: #000;
                }

                .bottom-btn-item-right {
                    padding: 6px 12px;
                    font-size: 15px;
                    font-weight: 700;
                    line-height: 18px;
                    color: #FFE39F;
                    background: linear-gradient(90deg, #574F3A 0%, #15120A 100%);
                    border-radius: 20px;
                }
            }
        }
    }
}
</style>