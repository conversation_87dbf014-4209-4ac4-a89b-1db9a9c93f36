<!-- 成为svip弹窗 -->
<template>
    <div>
        <var-popup position="center" v-model:show="showRateDialog" @click-overlay="close" :safe-area="true"
            :default-style="false">
            <div class="become-svip">
                <div class="become-svip-box">
                    <div class="become-svip-box-black">

                    </div>
                    <div class="become-svip-box-img">
                        <div class="become-svip-box-title">
                            SVIP Bonus
                        </div>
                        <img src="@/assets/memberShip/coins.png" alt="" class="become-svip-box-coins">
                        <div class="become-svip-box-desc">
                            Your SVIP bonus coins<span>(170 coins)</span>have been credited.
                        </div>
                    </div>
                    <div class="become-svip-box-btn">
                        <div class="become-svip-box-btn-item" v-ripple="{color:`rgba($color: #fff, $alpha: .5)`}">
                            <span>Go have fun!</span>
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">
export default {
    name: 'becomeSvipPopup',
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {

            showRateDialog: this.show
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.showRateDialog = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        close() {
            this.showRateDialog = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
    }
}
</script>
<style lang='scss' scoped>
.become-svip {
    font-family: var(--font-family-urbanist);

    .become-svip-box {
        width: 270px;
        height: 326px;
        background: linear-gradient(0deg, #FFB887 0%, #E13390 100%);
        border-radius: 30px 10px 30px 30px;
        padding: 17px 0 0;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;

        .become-svip-box-black {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 10px;
            height: 10px;
            background: #000;
            border-radius: 50%;
        }



        .become-svip-box-img {
            width: 240px;
            height: 231px;
            background: url('@/assets/memberShip/svip-bonus.png') no-repeat;
            background-size: 100% 100%;
            position: relative;
            padding: 0 21px 7px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .become-svip-box-coins {
                position: absolute;
                width: 50px;
                height: 50px;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                margin: auto;

            }

            .become-svip-box-title {
                font-size: 30px;
                font-weight: 600;
                line-height: 36px;
                font-style: italic;
                color: #fff;
                text-align: center;
            }

            .become-svip-box-desc {
                font-size: 14px;
                font-weight: 400;
                line-height: 17px;
                color: #fff;
                text-align: center;
                span{
                    color: $common-color;
                }
            }
        }
        .become-svip-box-btn{
            width:100%;
            margin-top:13px;
            padding:0 55px;
            .become-svip-box-btn-item{
                width: 100%;
                height: 40px;
                background: #fff;
                border-radius: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                font-weight: 800;
                font-style: italic;
                color: $common-color;
            }
        }
    }
}
</style>