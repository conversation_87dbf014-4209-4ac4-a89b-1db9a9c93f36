

import { DirectiveBinding } from 'vue';

interface DragOptions {
  disabled?: boolean;
  boundary?: boolean;
  boundaryPadding?: number;       // 边界内边距 (默认0)
  preventScroll?: boolean;        // 是否阻止页面滚动
  preventZoom?: boolean;          // 是否阻止双击缩放
  touchSensitivity?: number;      // 触摸灵敏度 (默认1)
  onDragStart?: (event: MouseEvent | TouchEvent) => void;
  onDragMove?: (event: MouseEvent | TouchEvent, offsetX: number, offsetY: number) => void;
  onDragEnd?: (event: MouseEvent | TouchEvent) => void;
  onBoundaryHit?: (direction: 'left' | 'right' | 'top' | 'bottom') => void; // 边界碰撞回调
}

export default function (el: HTMLElement, binding: DirectiveBinding) {
  // 获取指令的配置选项
  const options: DragOptions = binding.value || {};
  
  // 拖拽状态
  let isDragging = false;
  let startX = 0;
  let startY = 0;
  let offsetX = 0;
  let offsetY = 0;
  let initialLeft = 0;
  let initialTop = 0;
  
  // 获取元素当前位置
  const getElementPosition = () => {
    const rect = el.getBoundingClientRect();
    const computedStyle = window.getComputedStyle(el);
    
    return {
      left: rect.left + window.scrollX,
      top: rect.top + window.scrollY
    };
  };
  
  // 设置元素位置
  const setElementPosition = (left: number, top: number) => {
    // 检查边界限制
    if (options.boundary) {
      const rect = el.getBoundingClientRect();
      const parentElement = el.parentElement;
      const boundaryPadding = options.boundaryPadding || 0;
      
      if (parentElement) {
        const parentRect = parentElement.getBoundingClientRect();
        const parentStyle = window.getComputedStyle(parentElement);
        
        // 获取父容器的实际尺寸（减去padding和border）
        const parentWidth = parentRect.width - 
          parseFloat(parentStyle.paddingLeft) - 
          parseFloat(parentStyle.paddingRight) - 
          parseFloat(parentStyle.borderLeftWidth) - 
          parseFloat(parentStyle.borderRightWidth);
        
        const parentHeight = parentRect.height - 
          parseFloat(parentStyle.paddingTop) - 
          parseFloat(parentStyle.paddingBottom) - 
          parseFloat(parentStyle.borderTopWidth) - 
          parseFloat(parentStyle.borderBottomWidth);
        
        // 应用边界内边距
        const minLeft = boundaryPadding;
        const maxLeft = parentWidth - rect.width - boundaryPadding;
        const minTop = boundaryPadding;
        const maxTop = parentHeight - rect.height - boundaryPadding;
        
        // 检测边界碰撞
        if (left < minLeft && options.onBoundaryHit) {
          options.onBoundaryHit('left');
        }
        if (left > maxLeft && options.onBoundaryHit) {
          options.onBoundaryHit('right');
        }
        if (top < minTop && options.onBoundaryHit) {
          options.onBoundaryHit('top');
        }
        if (top > maxTop && options.onBoundaryHit) {
          options.onBoundaryHit('bottom');
        }
        
        // 限制在父容器内，考虑边界内边距
        left = Math.max(minLeft, Math.min(left, maxLeft));
        top = Math.max(minTop, Math.min(top, maxTop));
      } else {
        // 如果没有父容器，限制在视口内
        const minLeft = boundaryPadding;
        const maxLeft = window.innerWidth - rect.width - boundaryPadding;
        const minTop = boundaryPadding;
        const maxTop = window.innerHeight - rect.height - boundaryPadding;
        
        // 检测边界碰撞
        if (left < minLeft && options.onBoundaryHit) {
          options.onBoundaryHit('left');
        }
        if (left > maxLeft && options.onBoundaryHit) {
          options.onBoundaryHit('right');
        }
        if (top < minTop && options.onBoundaryHit) {
          options.onBoundaryHit('top');
        }
        if (top > maxTop && options.onBoundaryHit) {
          options.onBoundaryHit('bottom');
        }
        
        left = Math.max(minLeft, Math.min(left, maxLeft));
        top = Math.max(minTop, Math.min(top, maxTop));
      }
    }
    
    el.style.position = 'absolute';
    el.style.left = `${left}px`;
    el.style.top = `${top}px`;
  };
  
  // 鼠标按下事件
  const handleMouseDown = (event: MouseEvent) => {
    if (options.disabled) return;
    
    // 只响应左键点击
    if (event.button !== 0) return;
    
    event.preventDefault();
    
    isDragging = true;
    startX = event.clientX;
    startY = event.clientY;
    
    // 记录初始位置
    const position = getElementPosition();
    initialLeft = position.left;
    initialTop = position.top;
    
    // 添加拖拽样式
    el.style.cursor = 'grabbing';
    el.style.userSelect = 'none';
    
    // 触发拖拽开始回调
    if (options.onDragStart) {
      options.onDragStart(event);
    }
    
    // 添加事件监听器
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };
  
  // 鼠标移动事件
  const handleMouseMove = (event: MouseEvent) => {
    if (!isDragging) return;
    
    event.preventDefault();
    
    // 计算偏移量
    offsetX = event.clientX - startX;
    offsetY = event.clientY - startY;
    
    // 设置新位置
    const newLeft = initialLeft + offsetX;
    const newTop = initialTop + offsetY;
    setElementPosition(newLeft, newTop);
    
    // 触发拖拽移动回调
    if (options.onDragMove) {
      options.onDragMove(event, offsetX, offsetY);
    }
  };
  
  // 鼠标释放事件
  const handleMouseUp = (event: MouseEvent) => {
    if (!isDragging) return;
    
    isDragging = false;
    
    // 恢复样式
    el.style.cursor = '';
    el.style.userSelect = '';
    
    // 触发拖拽结束回调
    if (options.onDragEnd) {
      options.onDragEnd(event);
    }
    
    // 移除事件监听器
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };
  
  // 触摸事件支持
  const handleTouchStart = (event: TouchEvent) => {
    if (options.disabled) return;
    
    // 根据配置决定是否阻止默认行为
    if (options.preventScroll !== false) {
      event.preventDefault();
    }
    
    isDragging = true;
    const touch = event.touches[0];
    startX = touch.clientX;
    startY = touch.clientY;
    
    // 记录初始位置
    const position = getElementPosition();
    initialLeft = position.left;
    initialTop = position.top;
    
    // 添加拖拽样式
    el.style.cursor = 'grabbing';
    el.style.userSelect = 'none';
    el.style.touchAction = options.preventScroll !== false ? 'none' : 'manipulation';
    
    // 触发拖拽开始回调
    if (options.onDragStart) {
      options.onDragStart(event);
    }
    
    // 添加触摸事件监听器
    document.addEventListener('touchmove', handleTouchMove, { passive: options.preventScroll === false });
    document.addEventListener('touchend', handleTouchEnd);
    document.addEventListener('touchcancel', handleTouchEnd);
  };
  
  const handleTouchMove = (event: TouchEvent) => {
    if (!isDragging) return;
    
    // 根据配置决定是否阻止默认行为
    if (options.preventScroll !== false) {
      event.preventDefault();
    }
    
    const touch = event.touches[0];
    
    // 计算偏移量，应用触摸灵敏度
    const sensitivity = options.touchSensitivity || 1;
    offsetX = (touch.clientX - startX) * sensitivity;
    offsetY = (touch.clientY - startY) * sensitivity;
    
    // 设置新位置
    const newLeft = initialLeft + offsetX;
    const newTop = initialTop + offsetY;
    setElementPosition(newLeft, newTop);
    
    // 触发拖拽移动回调
    if (options.onDragMove) {
      options.onDragMove(event, offsetX, offsetY);
    }
  };
  
  const handleTouchEnd = (event: TouchEvent) => {
    if (!isDragging) return;
    
    isDragging = false;
    
    // 恢复样式
    el.style.cursor = '';
    el.style.userSelect = '';
    el.style.touchAction = '';
    
    // 触发拖拽结束回调
    if (options.onDragEnd) {
      options.onDragEnd(event);
    }
    
    // 移除触摸事件监听器
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
    document.removeEventListener('touchcancel', handleTouchEnd);
  };
  
  // 添加事件监听器
  el.addEventListener('mousedown', handleMouseDown);
  el.addEventListener('touchstart', handleTouchStart, { passive: false });
  
  // 移动端优化：防止双击缩放
  if (options.preventZoom !== false) {
    let lastTouchEnd = 0;
    el.addEventListener('touchend', (event) => {
      const now = Date.now();
      if (now - lastTouchEnd <= 300) {
        event.preventDefault();
      }
      lastTouchEnd = now;
    }, false);
  }
  
  // 清理函数
  const cleanup = () => {
    el.removeEventListener('mousedown', handleMouseDown);
    el.removeEventListener('touchstart', handleTouchStart);
    el.removeEventListener('touchend', () => {});
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
    document.removeEventListener('touchcancel', handleTouchEnd);
  };
  
  // 返回清理函数，供指令卸载时调用
  return cleanup;
}