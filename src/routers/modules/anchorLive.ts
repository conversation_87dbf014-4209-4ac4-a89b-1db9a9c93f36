const anchorLiveModule = [
    {
        path: '/anchorLive',
        name: 'AnchorLive',
        component: () => import('@/views/anchorLive/index.vue'),
        meta: {
            title: 'anchorLive'
        }
    },
    {
        path: '/privateVideoChat',
        name: 'PrivateVideoChat',
        component: () => import('@/views/anchorLive/privateVideoChat.vue'),
        meta: {
            title: 'privateVideoChat'
        }
    },
    {
        path: '/multiBeamLive',
        name: 'MultiBeamLive',
        component: () => import('@/views/anchorLive/multiBeamLive.vue'),
        meta: {
            title: 'multiBeamLive'
        }
    },
    {
        path: '/pkLive',
        name: 'PkLive',
        component: () => import('@/views/anchorLive/pkLive.vue'),
        meta: {
            title: 'pkLive'
        }
    }
]

export default anchorLiveModule