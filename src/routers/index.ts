import { createRouter, createWebHistory } from 'vue-router'
import type { AppRouteRecordRaw } from './type'
import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/layout/layout.vue'
import profileModule from './modules/profile'
import memberShipModule from './modules/memberShip'
import anchorModule from './modules/anchor'
import anchorLiveModule from './modules/anchorLive'
// 路由配置
const routes: AppRouteRecordRaw[] = [
    {
        path: '/',
        redirect: '/tabs/home'
    },
    // {
    //     path: '/home',
    //     name: 'Home',
    //     component: () => import('@/views/home/<USER>'),
    //     meta: {
    //         title: '首页',
    //         keepAlive: true
    //     }
    // },
    {
        path: '/detail',
        name: 'Detail',
        component: () => import('@/views/detail/index.vue'),
        meta: {
            title: '图片详情',
            keepAlive: false
        }
    },
    {
        path: '/tabs',
        name: 'Tabs',
        component: Layout,
        meta: {
            title: '标签页'
        },
        children: [
            {
                path: 'home',
                name: 'home',
                component: () => import('@/views/home/<USER>'),
                meta: {
                    title: 'home',
                    keepAlive: false
                }
            },
            {
                path: 'message',
                name: 'message',
                component: () => import('@/views/message/index.vue'),
                meta: {
                    title: 'message',
                    keepAlive: false
                }
            },
            {
                path: 'moments',
                name: 'moments',
                component: () => import('@/views/moments/index.vue'),
                meta: {
                    title: 'moments',
                    keepAlive: true
                }
            },
            {
                path: 'recharge',
                name: 'recharge',
                component: () => import('@/views/recharge/index.vue'),
                meta: {
                    title: 'recharge',
                    keepAlive: true
                }
            },
            {
                path: 'profile',
                name: 'profile',
                component: () => import('@/views/profile/index.vue'),
                meta: {
                    title: 'profile',
                    keepAlive: true
                }
            },
        ]
    },
    {
        path: '/searchPage',
        name: 'SearchPage',
        component: () => import('@/views/home/<USER>'),
        meta: {
            title: '搜索页面'
        }
    },
    {
        path: '/match',
        name: 'Match',
        component: () => import('@/views/common/match.vue'),
        meta: {
            title: 'match'
        }
    },
    {
        path: '/rankings',
        name: 'Rankings',
        component: () => import('@/views/common/rankings.vue'),
        meta: {
            title: 'rankings'
        }
    },
    {
        path: '/lightning-demo',
        name: 'LightningDemo',
        component: () => import('@/views/LightningDemo.vue'),
        meta: {
            title: '动态闪电演示'
        }
    },
    {
        path: '/test-lightning',
        name: 'TestLightning',
        component: () => import('@/views/TestLightning.vue'),
        meta: {
            title: '闪电组件测试'
        }
    },
    {
        path: '/lightning-integration',
        name: 'LightningIntegration',
        component: () => import('@/components/LightningIntegration.vue'),
        meta: {
            title: '闪电效果集成示例'
        }
    },
    {
        path: '/test-progress',
        name: 'TestProgress',
        component: () => import('@/views/TestProgress.vue'),
        meta: {
            title: '进度条测试'
        }
    },
    // 404 页面
    {
        path: '/:pathMatch(.*)*',
        name: 'NotFound',
        component: () => import('@/views/error/404.vue'),
        meta: {
            title: '页面不存在'
        }
    },
    ...profileModule,
    ...memberShipModule,
    ...anchorModule,
    ...anchorLiveModule
]

// 创建路由实例
const router = createRouter({
    history: createWebHistory(),
    routes: routes as RouteRecordRaw[],
    scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
            return savedPosition
        } else {
            return { top: 0, behavior: 'smooth' }
        }
    }
})

// 路由守卫
router.beforeEach((to, from, next) => {
    // 设置页面标题
    const title = to.meta?.title
    if (title) {
        document.title = `${title} - Live Web`
    }
    next()
})

export default router