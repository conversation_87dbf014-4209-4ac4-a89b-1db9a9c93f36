declare module 'video-animation-player' {
    interface VapConfig {
        src: string; // 视频地址
        config: any; // 配置文件
        container: HTMLElement; // DOM容器
        autoplay?: boolean;
        loop?: boolean;
        controls?: boolean;
    }

    interface VapOptions {
        autoplay?: boolean;
        loop?: boolean;
        controls?: boolean;
    }

    class Vap {
        // 支持对象配置方式
        constructor(config: VapConfig);
        // 支持参数方式
        constructor(container: HTMLElement, src: string, config: any, options?: VapOptions);

        play(): void;
        pause(): void;
        stop(): void;
        destroy(): void;

        // 可能的事件监听方法
        on?(event: string, callback: Function): void;
        off?(event: string, callback: Function): void;

        // 可能的其他方法
        setLoop?(loop: boolean): void;
        setMute?(mute: boolean): void;
    }

    export default Vap;
}
