declare module 'video-animation-player' {
    interface VapConfig {
        src: string; // 视频地址
        config: any; // 配置文件
        container: HTMLElement; // DOM容器
        autoplay?: boolean;
        loop?: boolean;
        controls?: boolean;
        width?: number;
        height?: number;
        fps?: number;
        mute?: boolean;
        precache?: boolean;
        beginPoint?: number;
        accurate?: boolean;
    }

    interface VapOptions {
        autoplay?: boolean;
        loop?: boolean;
        controls?: boolean;
    }

    class Vap {
        // 支持对象配置方式
        constructor(config?: VapConfig);
        // 支持参数方式
        constructor(container?: HTMLElement, src?: string, config?: any, options?: VapOptions);

        play(config?: VapConfig): Vap;
        pause(): void;
        stop(): void;
        destroy(): void;

        // 事件监听方法
        on(event: string, callback: Function): Vap;
        off(event: string, callback: Function): Vap;
    }

    export default Vap;
}
