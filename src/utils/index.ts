//时间戳转化为年/月/日 时:分:秒
export const timestampToTime = (timestamp: number): string => {
    const date = new Date(Number(timestamp) * 1000)
    const Y = date.getFullYear() + '-'
    const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
    const D = (date.getDate() + 1 <= 10 ? '0' + date.getDate() : date.getDate()) + ' '
    const h = (date.getHours() + 1 <= 10 ? '0' + date.getHours() : date.getHours()) + ':'
    const m = (date.getMinutes() + 1 <= 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
    const s = date.getSeconds() + 1 <= 10 ? '0' + date.getSeconds() : date.getSeconds()
    return Y + M + D + h + m + s
}
export const timestampToDays = (timestamp: number): string => {
    const date = new Date(Number(timestamp))
    const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '/'
    const D = (date.getDate() + 1 <= 10 ? '0' + date.getDate() : date.getDate())
    return  M + D
}
